# PasaBuy Pal Reporting, Invoicing, Expenses & Payments Tracking System
## Implementation Roadmap & Technical Specification
---

## 1. Executive Summary

This document outlines the comprehensive implementation plan for enhancing PasaBuy Pal with advanced reporting, invoicing, expense tracking, and payment management capabilities. The focus is on internal tracking and business intelligence without external payment gateway integration.

### 1.1 Objectives
- **Basic Business Reporting**: Practical order and financial reporting with filtering capabilities
- **Enhanced Filtering System**: Upgrade filtering across all existing pages with persistence and export
- **Enhanced Invoicing**: Build upon existing invoice system with advanced features
- **Expense Management**: New module for business expense tracking and categorization
- **Payment Tracking**: Internal payment status management and reconciliation

### 1.2 Scope
- **Internal Tracking Only**: No external payment gateway integration
- **Practical Reporting Focus**: Table-based reports with filtering and export capabilities
- **Enhanced User Experience**: Improved filtering and data export across all pages
- **Mobile-First Design**: Maintain responsive design patterns

---

## 2. Current State Analysis

### 2.1 Existing Invoicing Infrastructure

**✅ Already Implemented:**
- Basic invoice creation and management (`/api/invoices`)
- Enhanced invoice service with advanced features (`/lib/enhanced-invoice-service.ts`)
- Invoice status tracking (DRAFT, SENT, PAID, OVERDUE, CANCELLED)
- PDF generation capabilities (`/lib/pdf-generator.ts`)
- Invoice-order relationship management
- Daily batch invoice generation
- Invoice analytics and reporting foundation

**Database Schema (Existing):**
```sql
-- Core invoice tables already exist
Invoice {
  id, invoiceNumber, customerId, status, invoiceType, priority
  subtotal, discountAmount, taxAmount, shippingCost, total
  paymentTerms, paymentMethod, currency
  issueDate, dueDate, sentDate, paidDate, overdueDate
  approvalStatus, notes, internalNotes, customerNotes
  // ... extensive tracking fields
}

InvoiceItem {
  id, invoiceId, orderId, description, quantity
  unitPrice, discountAmount, taxAmount, totalPrice
  itemType, category, sku, notes
}

InvoicePayment {
  id, invoiceId, paymentNumber, amount, paymentMethod
  paymentDate, reference, notes, processorFee
}
```

### 2.2 Current Data Models & Relationships

**Core Entities:**
- **Orders**: 40+ fields with comprehensive tracking
- **Customers**: Enhanced with business metrics and segmentation
- **Stores**: Pricing matrix integration and configuration management
- **Pricing**: Range-based tiers with automatic calculation

**API Patterns:**
- RESTful endpoints with consistent error handling
- Advanced filtering and pagination (`/lib/query-builder.ts`)
- Enhanced services pattern (`/lib/enhanced-*-service.ts`)
- Bulk operations support
- Import/export capabilities

### 2.3 UI/UX Patterns

**Design System:**
- Mobile-first responsive design (44px touch targets)
- Card-based layouts with compact variants
- Bottom navigation with context-aware actions
- Form patterns with validation and error handling
- Filter modals and advanced search capabilities

**Component Architecture:**
- Reusable UI components (`/components/ui/`)
- Responsive grid layouts (`grid-cols-1 md:grid-cols-2`)
- Consistent spacing and typography
- Accessibility-compliant interactions

---

## 3. Implementation Modules

### 3.1 Basic Reporting System

**Priority:** High
**Complexity:** Low-Medium
**Timeline:** 2-3 weeks

#### 3.1.1 Core Report Pages

**Orders by Store Report (`/reports/orders-by-store`)**
```typescript
interface OrdersByStoreReport {
  storeCode: string
  storeName: string
  totalOrders: number
  totalRevenue: number
  averageOrderValue: number
  orderCount: {
    pending: number
    bought: number
    packed: number
    delivered: number
  }
  categoryBreakdown: Array<{
    category: string
    orderCount: number
    revenue: number
  }>
  dateRange: {
    from: Date
    to: Date
  }
}
```

**Orders by Customer Report (`/reports/orders-by-customer`)**
```typescript
interface OrdersByCustomerReport {
  customerId: number
  customerName: string
  totalOrders: number
  totalSpent: number
  averageOrderValue: number
  lastOrderDate: Date
  orderStatusBreakdown: {
    pending: number
    bought: number
    packed: number
    delivered: number
  }
  topCategories: Array<{
    category: string
    orderCount: number
    totalSpent: number
  }>
}
```

**Orders by Product Report (`/reports/orders-by-product`)**
```typescript
interface OrdersByProductReport {
  productName: string
  totalQuantity: number
  totalOrders: number
  totalRevenue: number
  averagePricePerUnit: number
  orderStatusBreakdown: {
    pending: number
    bought: number
    packed: number
    delivered: number
  }
  topStores: Array<{
    storeCode: string
    storeName: string
    quantity: number
    revenue: number
  }>
  topCustomers: Array<{
    customerId: number
    customerName: string
    quantity: number
    totalSpent: number
  }>
}
```

**Orders by Category Report (`/reports/orders-by-category`)**
```typescript
interface OrdersByCategoryReport {
  category: string
  totalOrders: number
  totalQuantity: number
  totalRevenue: number
  averageOrderValue: number
  uniqueProducts: number
  orderStatusBreakdown: {
    pending: number
    bought: number
    packed: number
    delivered: number
  }
  topProducts: Array<{
    productName: string
    quantity: number
    revenue: number
  }>
  storeDistribution: Array<{
    storeCode: string
    storeName: string
    orderCount: number
    revenue: number
  }>
}
```

**Key Features:**
- Table-based layouts with sorting capabilities
- Date range filtering (daily, weekly, monthly, custom ranges)
- Status filtering (pending, bought, packed, delivered)
- Store, customer, and category filtering
- Product name search and aggregation
- Export functionality (CSV, Excel, PDF)
- Mobile-responsive table design with horizontal scrolling
- Pagination for large datasets

#### 3.1.2 Enhanced Filtering System

**Universal Filter Components:**
- Advanced date range picker with presets
- Multi-select dropdowns for stores, customers, statuses, categories
- Product name search with autocomplete
- Category selection with predefined options
- Search functionality with debounced input
- Filter persistence using localStorage
- Clear all filters functionality
- Active filter indicators

**Category Management:**
- Predefined category options (Electronics, Clothing, Food, Books, etc.)
- Custom category creation and management
- Category-based filtering across all reports
- Category validation and standardization

**Filter Integration:**
- Upgrade existing Orders page filtering with category support
- Enhance Customers page with category-based filters
- Improve Stores page filtering capabilities
- Add comprehensive filtering to Invoices page
- Category filtering in all report pages
- Consistent filter UI across all pages

#### 3.1.3 Export Functionality

**Export Features:**
```typescript
interface ExportOptions {
  format: 'csv' | 'excel' | 'pdf'
  includeFilters: boolean
  dateRange?: { from: Date; to: Date }
  columns: string[]
  filename?: string
}
```

**Export Integration:**
- Export filtered results from all list pages
- Bulk export functionality
- Custom column selection
- Formatted data with proper headers
- Progress indicators for large exports

### 3.2 Enhanced Invoicing System

**Priority:** Medium  
**Complexity:** Low (builds on existing)  
**Timeline:** 2-3 weeks  

#### 3.2.1 Invoice Templates & Customization

**Template Management:**
- Multiple invoice templates (Standard, Premium, Custom)
- Company branding customization (logo, colors, fonts)
- Custom field configuration
- Template preview and editing interface

**Enhanced PDF Generation:**
```typescript
interface InvoiceTemplate {
  id: string
  name: string
  logoUrl?: string
  headerText?: string
  footerText?: string
  colorScheme: {
    primary: string
    secondary: string
    accent: string
  }
  layout: 'standard' | 'modern' | 'minimal'
  customFields: CustomField[]
}
```

#### 3.2.2 Bulk Invoice Operations

**Batch Processing:**
- Bulk invoice generation by date range
- Bulk status updates (send, mark paid, etc.)
- Bulk export and printing
- Automated recurring invoice generation

#### 3.2.3 Invoice Analytics

**Performance Metrics:**
- Invoice aging analysis
- Payment collection rates
- Average payment time
- Customer payment behavior patterns

### 3.3 Expense Tracking Module

**Priority:** High  
**Complexity:** High (new module)  
**Timeline:** 4-5 weeks  

#### 3.3.1 Database Schema Extensions

**New Tables:**
```sql
-- Expense categories
ExpenseCategory {
  id: Int @id @default(autoincrement())
  name: String @unique
  description: String?
  parentCategoryId: Int? // For subcategories
  color: String? // For UI visualization
  isActive: Boolean @default(true)
  createdAt: DateTime @default(now())
  updatedAt: DateTime @updatedAt
}

-- Main expense tracking
Expense {
  id: Int @id @default(autoincrement())
  expenseNumber: String @unique
  categoryId: Int
  amount: Float
  currency: String @default("PHP")
  description: String
  expenseDate: DateTime
  paymentMethod: String? // Cash, Bank Transfer, Credit Card, etc.
  vendor: String?
  receiptUrl: String? // File upload path
  notes: String?
  tags: String[] // For flexible categorization
  
  // Business context
  storeCodeId: Int? // If expense is store-specific
  orderId: Int? // If expense is order-specific
  customerId: Int? // If expense is customer-specific
  
  // Approval workflow
  status: String @default("PENDING") // PENDING, APPROVED, REJECTED, PAID
  approvedBy: String?
  approvedAt: DateTime?
  rejectedReason: String?
  
  // Financial tracking
  taxAmount: Float @default(0.00)
  taxRate: Float @default(0.00)
  isRecurring: Boolean @default(false)
  recurringFrequency: String? // MONTHLY, QUARTERLY, YEARLY
  nextRecurringDate: DateTime?
  
  createdAt: DateTime @default(now())
  updatedAt: DateTime @updatedAt
}

-- Expense attachments (receipts, invoices)
ExpenseAttachment {
  id: Int @id @default(autoincrement())
  expenseId: Int
  filename: String
  originalName: String
  mimeType: String
  fileSize: Int
  uploadedAt: DateTime @default(now())
}
```

#### 3.3.2 Expense Management Interface

**Mobile-First Forms:**
```typescript
// Expense creation form following established patterns
interface ExpenseFormData {
  categoryId: number
  amount: number
  description: string
  expenseDate: string
  paymentMethod?: string
  vendor?: string
  storeCodeId?: number
  orderId?: number
  notes?: string
  tags: string[]
  receiptFiles: File[]
}
```

**Key Features:**
- Quick expense entry with camera integration
- Receipt photo upload and OCR processing
- Recurring expense setup
- Bulk expense import from CSV/Excel
- Expense approval workflow

#### 3.3.3 Expense Analytics

**Reporting Features:**
- Expense breakdown by category
- Monthly/quarterly expense trends
- Vendor spending analysis
- Store-specific expense tracking
- Tax reporting and summaries

### 3.4 Payment Records & Reconciliation

**Priority:** Medium  
**Complexity:** Medium  
**Timeline:** 3-4 weeks  

#### 3.4.1 Enhanced Payment Tracking

**Extended Payment Schema:**
```sql
-- Enhanced payment records (extends existing InvoicePayment)
PaymentRecord {
  id: Int @id @default(autoincrement())
  paymentNumber: String @unique
  
  // Payment details
  amount: Float
  currency: String @default("PHP")
  paymentMethod: String // Cash, Bank Transfer, GCash, etc.
  paymentDate: DateTime
  
  // References
  invoiceId: Int? // If payment is for an invoice
  customerId: Int // Always required
  orderId: Int? // If payment is for specific order
  
  // Payment tracking
  reference: String? // Bank reference, transaction ID, etc.
  status: String @default("COMPLETED") // PENDING, COMPLETED, FAILED, REFUNDED
  notes: String?
  
  // Reconciliation
  isReconciled: Boolean @default(false)
  reconciledAt: DateTime?
  reconciledBy: String?
  bankStatementRef: String?
  
  // Fees and adjustments
  processingFee: Float @default(0.00)
  adjustmentAmount: Float @default(0.00)
  adjustmentReason: String?
  
  createdAt: DateTime @default(now())
  updatedAt: DateTime @updatedAt
}

-- Bank reconciliation records
BankReconciliation {
  id: Int @id @default(autoincrement())
  reconciliationDate: DateTime
  bankAccount: String
  statementBalance: Float
  systemBalance: Float
  difference: Float
  status: String @default("PENDING") // PENDING, COMPLETED, DISCREPANCY
  notes: String?
  reconciledBy: String?
  createdAt: DateTime @default(now())
}
```

#### 3.4.2 Payment Management Interface

**Payment Dashboard:**
- Payment status overview
- Pending payments tracking
- Payment method analytics
- Customer payment history
- Reconciliation status monitoring

**Reconciliation Tools:**
- Bank statement import and matching
- Automatic payment matching algorithms
- Manual reconciliation interface
- Discrepancy reporting and resolution

---

## 4. Technical Implementation Details

### 4.1 API Endpoint Structure

Following established patterns:

```typescript
// Basic reporting endpoints
GET /api/reports/orders-by-store
GET /api/reports/orders-by-customer
GET /api/reports/orders-by-product
GET /api/reports/orders-by-category
POST /api/reports/export

// Enhanced filtering endpoints
GET /api/enhanced/orders (with category filtering)
GET /api/enhanced/customers (with category filtering)
GET /api/enhanced/stores (with category filtering)
GET /api/enhanced/invoices (with category filtering)

// Category management endpoints
GET /api/categories
POST /api/categories
PUT /api/categories/[id]
DELETE /api/categories/[id]

// Export endpoints
POST /api/export/orders
POST /api/export/customers
POST /api/export/stores
POST /api/export/invoices

// Enhanced invoicing
GET /api/enhanced/invoices
POST /api/enhanced/invoices/bulk-operations
GET /api/enhanced/invoices/templates
POST /api/enhanced/invoices/templates

// Expense management
GET /api/expenses
POST /api/expenses
PUT /api/expenses/[id]
DELETE /api/expenses/[id]
GET /api/expenses/categories
POST /api/expenses/bulk-import
GET /api/expenses/reports

// Payment tracking
GET /api/payments
POST /api/payments
PUT /api/payments/[id]
POST /api/payments/reconcile
GET /api/payments/reconciliation-status

// Analytics (Phase 5)
GET /api/analytics/dashboard
GET /api/analytics/trends
GET /api/analytics/insights
```

### 4.2 Service Layer Architecture

```typescript
// Following established enhanced service pattern
class BasicReportingService {
  static async getOrdersByStore(filters: ReportFilters): Promise<OrdersByStoreReport[]>
  static async getOrdersByCustomer(filters: ReportFilters): Promise<OrdersByCustomerReport[]>
  static async getOrdersByProduct(filters: ReportFilters): Promise<OrdersByProductReport[]>
  static async getOrdersByCategory(filters: ReportFilters): Promise<OrdersByCategoryReport[]>
  static async exportReport(reportType: string, format: 'csv' | 'excel' | 'pdf', filters: ReportFilters): Promise<Buffer>
}

class CategoryService {
  static async getCategories(): Promise<Category[]>
  static async createCategory(data: CategoryData): Promise<Category>
  static async updateCategory(id: number, data: CategoryData): Promise<Category>
  static async deleteCategory(id: number): Promise<void>
  static async getCategoryUsageStats(): Promise<CategoryUsageStats[]>
}

class EnhancedFilterService {
  static async getFilteredOrders(filters: AdvancedOrderFilters, pagination: PaginationParams): Promise<FilteredResult<Order>>
  static async getFilteredCustomers(filters: AdvancedCustomerFilters, pagination: PaginationParams): Promise<FilteredResult<Customer>>
  static async getFilteredStores(filters: AdvancedStoreFilters, pagination: PaginationParams): Promise<FilteredResult<Store>>
  static async getFilteredInvoices(filters: AdvancedInvoiceFilters, pagination: PaginationParams): Promise<FilteredResult<Invoice>>
}

class ExportService {
  static async exportOrders(filters: OrderFilters, format: ExportFormat): Promise<Buffer>
  static async exportCustomers(filters: CustomerFilters, format: ExportFormat): Promise<Buffer>
  static async exportStores(filters: StoreFilters, format: ExportFormat): Promise<Buffer>
  static async exportInvoices(filters: InvoiceFilters, format: ExportFormat): Promise<Buffer>
}

class ExpenseService {
  static async createExpense(data: ExpenseData): Promise<Expense>
  static async getExpenseReports(filters: ExpenseFilters): Promise<ExpenseReport[]>
  static async processRecurringExpenses(): Promise<void>
  static async importExpensesFromCSV(file: Buffer): Promise<ImportResult>
}

class PaymentService {
  static async recordPayment(data: PaymentData): Promise<PaymentRecord>
  static async reconcilePayments(bankData: BankStatement[]): Promise<ReconciliationResult>
  static async getPaymentReports(filters: PaymentFilters): Promise<PaymentReport[]>
}

// Analytics Service (Phase 5)
class AnalyticsService {
  static async getDashboardMetrics(filters: DateRangeFilter): Promise<DashboardMetrics>
  static async getTrendAnalysis(params: TrendParams): Promise<TrendAnalysis>
  static async getInsights(filters: InsightFilters): Promise<BusinessInsights>
}
```

### 4.3 Component Architecture

**Table-Based Report Layout:**
```typescript
// Report page structure
<ReportLayout>
  <ReportHeader title="Orders by Store" />

  <FilterSection className="mb-6">
    <DateRangePicker value={dateRange} onChange={setDateRange} />
    <StoreFilter value={selectedStores} onChange={setSelectedStores} />
    <StatusFilter value={selectedStatuses} onChange={setSelectedStatuses} />
    <ExportButton onExport={handleExport} />
  </FilterSection>

  <ReportTable
    data={reportData}
    columns={[
      { key: 'storeCode', label: 'Store Code', sortable: true },
      { key: 'storeName', label: 'Store Name', sortable: true },
      { key: 'totalOrders', label: 'Total Orders', sortable: true, format: 'number' },
      { key: 'totalRevenue', label: 'Total Revenue', sortable: true, format: 'currency' },
      { key: 'averageOrderValue', label: 'Avg Order Value', sortable: true, format: 'currency' }
    ]}
    pagination={pagination}
    onSort={handleSort}
    onPageChange={handlePageChange}
    className="mobile-responsive"
  />
</ReportLayout>
```

**Enhanced Filter Components:**
```typescript
// Universal filter component
<AdvancedFilter>
  <FilterGroup title="Date Range">
    <DateRangePicker
      value={filters.dateRange}
      onChange={(range) => updateFilter('dateRange', range)}
      presets={['today', 'week', 'month', 'quarter', 'year']}
    />
  </FilterGroup>

  <FilterGroup title="Status">
    <MultiSelect
      options={statusOptions}
      value={filters.statuses}
      onChange={(statuses) => updateFilter('statuses', statuses)}
      placeholder="Select statuses..."
    />
  </FilterGroup>

  <FilterGroup title="Search">
    <SearchInput
      value={filters.search}
      onChange={(search) => updateFilter('search', search)}
      placeholder="Search orders, customers, products..."
      debounceMs={300}
    />
  </FilterGroup>

  <FilterActions>
    <Button variant="outline" onClick={clearAllFilters}>Clear All</Button>
    <Button onClick={applyFilters}>Apply Filters</Button>
    <ExportButton filters={filters} />
  </FilterActions>
</AdvancedFilter>
```

**Mobile-First Form Patterns:**
```typescript
// Expense form following established patterns
<Form {...form}>
  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
    <FormField name="categoryId" />
    <FormField name="amount" />
    <FormField name="expenseDate" />
    <FormField name="paymentMethod" />
  </div>

  <div className="grid grid-cols-1 gap-4">
    <FormField name="description" />
    <FormField name="vendor" />
    <FormField name="notes" />
  </div>

  <ReceiptUpload onFilesChange={handleReceiptUpload} />

  <div className="flex flex-col sm:flex-row gap-3 sm:gap-4">
    <Button type="submit" className="flex-1 min-h-[48px]">Save Expense</Button>
    <Button type="button" variant="outline" className="flex-1 min-h-[48px]">Cancel</Button>
  </div>
</Form>
```

---

## 5. Implementation Phases

### Phase 1: Basic Reporting & Enhanced Filtering (Weeks 1-4)
**Priority:** High
**Dependencies:** None

**Deliverables:**
- Database migration to add `category` field to orders table
- `/reports/orders-by-store` - Order totals grouped by store with category filtering
- `/reports/orders-by-customer` - Order totals grouped by customer with category breakdown
- `/reports/orders-by-product` - Order totals grouped by product name with detailed analytics
- `/reports/orders-by-category` - Order totals grouped by category with product breakdown
- Enhanced filter components for all existing list pages with category support
- Category management interface for creating and managing order categories
- Export functionality integrated into all filtered views and reports
- Filter persistence and advanced search capabilities

**Technical Tasks:**
1. Database migration: Add `category` field to orders table with index
2. Update Order model and TypeScript interfaces to include category
3. Create category management API endpoints and service
4. Build category selection components for order forms
5. Create basic report API endpoints for all aggregation types (store, customer, product, category)
6. Build responsive table-based report pages with category filtering
7. Upgrade existing filter components with category support and persistence
8. Implement universal export functionality (CSV, Excel, PDF) for all reports
9. Add category filtering to all existing list pages (Orders, Customers, Stores, Invoices)
10. Create reusable filter and export components with category support

### Phase 2: Enhanced Invoicing (Weeks 3-5)
**Priority:** Medium
**Dependencies:** Phase 1 (partial)

**Deliverables:**
- Invoice template system
- Bulk invoice operations
- Enhanced PDF generation
- Invoice filtering and export integration

**Technical Tasks:**
1. Extend existing invoice service
2. Create template management interface
3. Implement bulk operations API
4. Enhance PDF generation with templates
5. Integrate enhanced filtering into invoice pages

### Phase 3: Expense Tracking (Weeks 5-9)
**Priority:** High
**Dependencies:** Phase 1

**Deliverables:**
- Complete expense management module
- Receipt upload and processing
- Expense reporting with filtering and export
- Mobile-optimized expense entry

**Technical Tasks:**
1. Database schema migration for expenses
2. Expense service implementation
3. Mobile-first expense forms
4. Receipt upload and storage
5. Expense reporting integration
6. Recurring expense automation

### Phase 4: Payment Tracking (Weeks 8-11)
**Priority:** Medium
**Dependencies:** Phase 2, Phase 3

**Deliverables:**
- Enhanced payment tracking system
- Bank reconciliation tools
- Payment reporting with filtering
- Reconciliation interface

**Technical Tasks:**
1. Extend payment schema and services
2. Build reconciliation algorithms
3. Create payment management interface
4. Implement bank statement import
5. Add payment reporting capabilities

### Phase 5: Analytics & Visualization (Weeks 10-13)
**Priority:** Low
**Dependencies:** All previous phases

**Deliverables:**
- Advanced analytics dashboard with charts
- Data visualization components
- Trend analysis and insights
- Interactive reporting features

**Technical Tasks:**
1. Set up chart library (Chart.js or Recharts)
2. Create dashboard layout components
3. Implement advanced analytics calculations
4. Build interactive chart components
5. Add trend analysis features

### Phase 6: Integration & Testing (Weeks 12-14)
**Priority:** High
**Dependencies:** All previous phases

**Deliverables:**
- Complete system integration
- Comprehensive testing
- Performance optimization
- Documentation and training

**Technical Tasks:**
1. End-to-end integration testing
2. Performance optimization
3. Mobile responsiveness testing
4. User acceptance testing
5. Documentation completion

---

## 6. Technical Considerations

### 6.1 Database Migration Strategy

**Incremental Migrations:**
```sql
-- Migration 001: Expense categories
CREATE TABLE expense_categories (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  name TEXT UNIQUE NOT NULL,
  description TEXT,
  parent_category_id INTEGER,
  color TEXT,
  is_active BOOLEAN DEFAULT TRUE,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- Migration 002: Expenses
CREATE TABLE expenses (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  expense_number TEXT UNIQUE NOT NULL,
  category_id INTEGER NOT NULL,
  amount REAL NOT NULL,
  currency TEXT DEFAULT 'PHP',
  description TEXT NOT NULL,
  expense_date DATETIME NOT NULL,
  -- ... additional fields
  FOREIGN KEY (category_id) REFERENCES expense_categories(id)
);
```

### 6.2 Performance Optimization

**Database Indexing:**
```sql
-- Critical indexes for reporting queries
CREATE INDEX idx_orders_created_at ON orders(created_at);
CREATE INDEX idx_orders_customer_date ON orders(customer_id, created_at);
CREATE INDEX idx_invoices_status_date ON invoices(status, issue_date);
CREATE INDEX idx_expenses_category_date ON expenses(category_id, expense_date);
CREATE INDEX idx_payments_date_status ON payment_records(payment_date, status);
```

**Query Optimization:**
- Implement database query caching for dashboard metrics
- Use aggregation queries for reporting
- Implement pagination for large datasets
- Add database connection pooling

### 6.3 File Storage Strategy

**Receipt and Document Storage:**
```typescript
// File upload configuration
const UPLOAD_CONFIG = {
  maxFileSize: 10 * 1024 * 1024, // 10MB
  allowedTypes: ['image/jpeg', 'image/png', 'application/pdf'],
  storageLocation: process.env.NODE_ENV === 'production' 
    ? '/var/uploads/receipts' 
    : './uploads/receipts'
}

// File processing pipeline
class FileProcessor {
  static async processReceipt(file: File): Promise<ProcessedReceipt> {
    // 1. Validate file type and size
    // 2. Generate unique filename
    // 3. Optimize image (compress, resize)
    // 4. Extract text using OCR (optional)
    // 5. Store file and return metadata
  }
}
```

### 6.4 Security Considerations

**Data Protection:**
- Implement role-based access control for sensitive financial data
- Add audit logging for all financial transactions
- Encrypt sensitive data at rest
- Implement secure file upload validation
- Add rate limiting for API endpoints

**Access Control:**
```typescript
// Role-based permissions
enum Permission {
  VIEW_REPORTS = 'view_reports',
  MANAGE_EXPENSES = 'manage_expenses',
  APPROVE_EXPENSES = 'approve_expenses',
  RECONCILE_PAYMENTS = 'reconcile_payments',
  EXPORT_DATA = 'export_data'
}

// Middleware for protected routes
const requirePermission = (permission: Permission) => {
  return (req: NextRequest, res: NextResponse, next: NextFunction) => {
    // Check user permissions
    // Allow or deny access
  }
}
```

---

## 7. Testing Strategy

### 7.1 Unit Testing
- Service layer testing with Jest
- Component testing with React Testing Library
- Database query testing with test database
- API endpoint testing with supertest

### 7.2 Integration Testing
- End-to-end workflow testing
- Database migration testing
- File upload and processing testing
- Report generation testing

### 7.3 Performance Testing
- Dashboard load time optimization
- Large dataset handling
- Concurrent user testing
- Mobile performance testing

### 7.4 User Acceptance Testing
- Mobile usability testing
- Workflow validation
- Accessibility compliance testing
- Cross-browser compatibility

---

## 8. Deployment & Maintenance

### 8.1 Deployment Strategy
- Staged rollout with feature flags
- Database migration automation
- Backup and rollback procedures
- Environment-specific configuration

### 8.2 Monitoring & Maintenance
- Application performance monitoring
- Error tracking and alerting
- Database performance monitoring
- User analytics and feedback collection

### 8.3 Documentation
- API documentation with examples
- User guides and tutorials
- Developer documentation
- Deployment and maintenance guides

---

## 9. Success Metrics

### 9.1 Technical Metrics
- Dashboard load time < 2 seconds
- Mobile responsiveness score > 95%
- API response time < 500ms
- Test coverage > 80%

### 9.2 Business Metrics
- Reduced manual data entry time by 60%
- Improved financial reporting accuracy
- Faster invoice processing
- Enhanced expense tracking compliance

### 9.3 User Experience Metrics
- Mobile usability score > 4.5/5
- Feature adoption rate > 70%
- User satisfaction score > 4.0/5
- Support ticket reduction by 40%

---

## 10. Conclusion

This implementation roadmap provides a comprehensive plan for enhancing PasaBuy Pal with advanced reporting, invoicing, expense tracking, and payment management capabilities. The phased approach ensures manageable development cycles while maintaining system stability and user experience quality.

The focus on internal tracking and business intelligence aligns with the established PasaBuy Pal business model while providing the foundation for future enhancements and integrations.

**Next Steps:**
1. Stakeholder review and approval
2. Development team assignment
3. Phase 1 implementation kickoff
4. Regular progress reviews and adjustments

---

## Appendix A: Code Examples

### A.1 Basic Report Page Implementation

```typescript
// src/app/reports/orders-by-store/page.tsx
'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { DateRangePicker } from '@/components/ui/date-range-picker'
import { StoreFilter } from '@/components/filters/store-filter'
import { StatusFilter } from '@/components/filters/status-filter'
import { ExportButton } from '@/components/ui/export-button'
import { ReportTable } from '@/components/reports/report-table'
import { LoadingSpinner } from '@/components/ui/loading-spinner'

interface OrdersByStoreReport {
  storeCode: string
  storeName: string
  totalOrders: number
  totalRevenue: number
  averageOrderValue: number
  orderCount: {
    pending: number
    bought: number
    packed: number
    delivered: number
  }
}

interface ReportFilters {
  dateRange: { from: Date; to: Date }
  storeIds: number[]
  statuses: string[]
}

export default function OrdersByStoreReportPage() {
  const [reportData, setReportData] = useState<OrdersByStoreReport[]>([])
  const [filters, setFilters] = useState<ReportFilters>({
    dateRange: {
      from: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // 30 days ago
      to: new Date()
    },
    storeIds: [],
    statuses: []
  })
  const [isLoading, setIsLoading] = useState(true)
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 50,
    total: 0
  })

  useEffect(() => {
    fetchReportData()
  }, [filters, pagination.page])

  const fetchReportData = async () => {
    setIsLoading(true)
    try {
      const queryParams = new URLSearchParams({
        from: filters.dateRange.from.toISOString(),
        to: filters.dateRange.to.toISOString(),
        page: pagination.page.toString(),
        limit: pagination.limit.toString()
      })

      if (filters.storeIds.length > 0) {
        queryParams.append('storeIds', filters.storeIds.join(','))
      }
      if (filters.statuses.length > 0) {
        queryParams.append('statuses', filters.statuses.join(','))
      }

      const response = await fetch(`/api/reports/orders-by-store?${queryParams}`)
      const data = await response.json()

      setReportData(data.results)
      setPagination(prev => ({ ...prev, total: data.total }))
    } catch (error) {
      console.error('Error fetching report data:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const handleExport = async (format: 'csv' | 'excel' | 'pdf') => {
    try {
      const response = await fetch('/api/reports/export', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          reportType: 'orders-by-store',
          format,
          filters
        })
      })

      if (response.ok) {
        const blob = await response.blob()
        const url = window.URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.href = url
        a.download = `orders-by-store-${new Date().toISOString().split('T')[0]}.${format}`
        document.body.appendChild(a)
        a.click()
        window.URL.revokeObjectURL(url)
        document.body.removeChild(a)
      }
    } catch (error) {
      console.error('Error exporting report:', error)
    }
  }

  const columns = [
    { key: 'storeCode', label: 'Store Code', sortable: true },
    { key: 'storeName', label: 'Store Name', sortable: true },
    { key: 'totalOrders', label: 'Total Orders', sortable: true, format: 'number' },
    { key: 'totalRevenue', label: 'Total Revenue', sortable: true, format: 'currency' },
    { key: 'averageOrderValue', label: 'Avg Order Value', sortable: true, format: 'currency' },
    { key: 'orderCount.pending', label: 'Pending', sortable: true, format: 'number' },
    { key: 'orderCount.bought', label: 'Bought', sortable: true, format: 'number' },
    { key: 'orderCount.packed', label: 'Packed', sortable: true, format: 'number' },
    { key: 'orderCount.delivered', label: 'Delivered', sortable: true, format: 'number' }
  ]

  return (
    <div className="space-y-6">
      {/* Report Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-2xl font-bold">Orders by Store Report</h1>
          <p className="text-muted-foreground">Order totals and performance by store</p>
        </div>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle>Filters</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <DateRangePicker
              value={filters.dateRange}
              onChange={(dateRange) => setFilters(prev => ({ ...prev, dateRange }))}
            />
            <StoreFilter
              value={filters.storeIds}
              onChange={(storeIds) => setFilters(prev => ({ ...prev, storeIds }))}
            />
            <StatusFilter
              value={filters.statuses}
              onChange={(statuses) => setFilters(prev => ({ ...prev, statuses }))}
            />
            <div className="flex gap-2">
              <Button variant="outline" onClick={() => setFilters({
                dateRange: { from: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), to: new Date() },
                storeIds: [],
                statuses: []
              })}>
                Clear Filters
              </Button>
              <ExportButton onExport={handleExport} />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Report Table */}
      <Card>
        <CardContent className="p-0">
          {isLoading ? (
            <div className="flex justify-center items-center h-64">
              <LoadingSpinner />
            </div>
          ) : (
            <ReportTable
              data={reportData}
              columns={columns}
              pagination={pagination}
              onPageChange={(page) => setPagination(prev => ({ ...prev, page }))}
              className="mobile-responsive"
            />
          )}
        </CardContent>
      </Card>
    </div>
  )
}
```

### A.2 Enhanced Filter Component Implementation

```typescript
// src/components/filters/advanced-filter.tsx
'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { DateRangePicker } from '@/components/ui/date-range-picker'
import { MultiSelect } from '@/components/ui/multi-select'
import { Badge } from '@/components/ui/badge'
import { LuFilter, LuX, LuDownload } from 'react-icons/lu'

interface FilterConfig {
  key: string
  label: string
  type: 'text' | 'select' | 'multiselect' | 'daterange' | 'number'
  options?: Array<{ value: string; label: string }>
  placeholder?: string
}

interface AdvancedFilterProps {
  filters: Record<string, any>
  onFiltersChange: (filters: Record<string, any>) => void
  filterConfig: FilterConfig[]
  onExport?: (format: 'csv' | 'excel' | 'pdf') => void
  persistKey?: string // For localStorage persistence
}

export function AdvancedFilter({
  filters,
  onFiltersChange,
  filterConfig,
  onExport,
  persistKey
}: AdvancedFilterProps) {
  const [isExpanded, setIsExpanded] = useState(false)
  const [localFilters, setLocalFilters] = useState(filters)

  // Load persisted filters on mount
  useEffect(() => {
    if (persistKey) {
      const saved = localStorage.getItem(`filters_${persistKey}`)
      if (saved) {
        try {
          const parsedFilters = JSON.parse(saved)
          setLocalFilters(parsedFilters)
          onFiltersChange(parsedFilters)
        } catch (error) {
          console.error('Error loading persisted filters:', error)
        }
      }
    }
  }, [persistKey])

  // Persist filters when they change
  useEffect(() => {
    if (persistKey) {
      localStorage.setItem(`filters_${persistKey}`, JSON.stringify(localFilters))
    }
  }, [localFilters, persistKey])

  const updateFilter = (key: string, value: any) => {
    const newFilters = { ...localFilters, [key]: value }
    setLocalFilters(newFilters)
  }

  const applyFilters = () => {
    onFiltersChange(localFilters)
  }

  const clearAllFilters = () => {
    const clearedFilters = filterConfig.reduce((acc, config) => {
      acc[config.key] = config.type === 'multiselect' ? [] : ''
      return acc
    }, {} as Record<string, any>)

    setLocalFilters(clearedFilters)
    onFiltersChange(clearedFilters)
  }

  const getActiveFilterCount = () => {
    return Object.values(localFilters).filter(value => {
      if (Array.isArray(value)) return value.length > 0
      if (typeof value === 'object' && value !== null) return Object.keys(value).length > 0
      return value !== '' && value !== null && value !== undefined
    }).length
  }

  const renderFilterField = (config: FilterConfig) => {
    const value = localFilters[config.key]

    switch (config.type) {
      case 'text':
        return (
          <Input
            placeholder={config.placeholder}
            value={value || ''}
            onChange={(e) => updateFilter(config.key, e.target.value)}
            className="min-h-[44px]"
          />
        )

      case 'daterange':
        return (
          <DateRangePicker
            value={value || { from: null, to: null }}
            onChange={(range) => updateFilter(config.key, range)}
            className="min-h-[44px]"
          />
        )

      case 'multiselect':
        return (
          <MultiSelect
            options={config.options || []}
            value={value || []}
            onChange={(selected) => updateFilter(config.key, selected)}
            placeholder={config.placeholder}
            className="min-h-[44px]"
          />
        )

      case 'select':
        return (
          <select
            value={value || ''}
            onChange={(e) => updateFilter(config.key, e.target.value)}
            className="min-h-[44px] w-full rounded-md border border-input bg-background px-3 py-2"
          >
            <option value="">{config.placeholder || 'Select...'}</option>
            {config.options?.map(option => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
        )

      case 'number':
        return (
          <Input
            type="number"
            placeholder={config.placeholder}
            value={value || ''}
            onChange={(e) => updateFilter(config.key, parseFloat(e.target.value) || '')}
            className="min-h-[44px]"
          />
        )

      default:
        return null
    }
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <CardTitle className="flex items-center gap-2">
              <LuFilter className="h-4 w-4" />
              Filters
            </CardTitle>
            {getActiveFilterCount() > 0 && (
              <Badge variant="secondary">
                {getActiveFilterCount()} active
              </Badge>
            )}
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setIsExpanded(!isExpanded)}
          >
            {isExpanded ? 'Collapse' : 'Expand'}
          </Button>
        </div>
      </CardHeader>

      {isExpanded && (
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-4">
            {filterConfig.map(config => (
              <div key={config.key}>
                <label className="text-sm font-medium mb-2 block">
                  {config.label}
                </label>
                {renderFilterField(config)}
              </div>
            ))}
          </div>

          <div className="flex flex-col sm:flex-row gap-2 justify-between">
            <div className="flex gap-2">
              <Button onClick={applyFilters}>
                Apply Filters
              </Button>
              <Button variant="outline" onClick={clearAllFilters}>
                Clear All
              </Button>
            </div>

            {onExport && (
              <div className="flex gap-2">
                <Button variant="outline" size="sm" onClick={() => onExport('csv')}>
                  <LuDownload className="h-4 w-4 mr-1" />
                  CSV
                </Button>
                <Button variant="outline" size="sm" onClick={() => onExport('excel')}>
                  <LuDownload className="h-4 w-4 mr-1" />
                  Excel
                </Button>
                <Button variant="outline" size="sm" onClick={() => onExport('pdf')}>
                  <LuDownload className="h-4 w-4 mr-1" />
                  PDF
                </Button>
              </div>
            )}
          </div>
        </CardContent>
      )}
    </Card>
  )
}
```

### A.3 Basic Reporting Service Implementation

```typescript
// src/lib/basic-reporting-service.ts
import { prisma } from '@/lib/db'
import { Prisma } from '@prisma/client'

export interface ReportFilters {
  dateRange?: { from: Date; to: Date }
  storeIds?: number[]
  customerIds?: number[]
  statuses?: string[]
  categories?: string[]
  productNames?: string[]
}

export interface OrdersByStoreReport {
  storeCode: string
  storeName: string
  totalOrders: number
  totalRevenue: number
  averageOrderValue: number
  orderCount: {
    pending: number
    bought: number
    packed: number
    delivered: number
  }
}

export interface OrdersByCustomerReport {
  customerId: number
  customerName: string
  totalOrders: number
  totalSpent: number
  averageOrderValue: number
  lastOrderDate: Date
  orderStatusBreakdown: {
    pending: number
    bought: number
    packed: number
    delivered: number
  }
  topCategories: Array<{
    category: string
    orderCount: number
    totalSpent: number
  }>
}

export interface OrdersByProductReport {
  productName: string
  totalQuantity: number
  totalOrders: number
  totalRevenue: number
  averagePricePerUnit: number
  orderStatusBreakdown: {
    pending: number
    bought: number
    packed: number
    delivered: number
  }
  topStores: Array<{
    storeCode: string
    storeName: string
    quantity: number
    revenue: number
  }>
  topCustomers: Array<{
    customerId: number
    customerName: string
    quantity: number
    totalSpent: number
  }>
}

export interface OrdersByCategoryReport {
  category: string
  totalOrders: number
  totalQuantity: number
  totalRevenue: number
  averageOrderValue: number
  uniqueProducts: number
  orderStatusBreakdown: {
    pending: number
    bought: number
    packed: number
    delivered: number
  }
  topProducts: Array<{
    productName: string
    quantity: number
    revenue: number
  }>
  storeDistribution: Array<{
    storeCode: string
    storeName: string
    orderCount: number
    revenue: number
  }>
}

export class BasicReportingService {
  /**
   * Get orders aggregated by store
   */
  static async getOrdersByStore(filters: ReportFilters): Promise<OrdersByStoreReport[]> {
    const whereClause = this.buildOrderWhereClause(filters)

    const results = await prisma.$queryRaw<any[]>`
      SELECT
        sc.code as store_code,
        sc.name as store_name,
        COUNT(o.id) as total_orders,
        COALESCE(SUM(o.customer_price), 0) as total_revenue,
        COALESCE(AVG(o.customer_price), 0) as average_order_value,
        COUNT(CASE WHEN o.status = 'PENDING' THEN 1 END) as pending_count,
        COUNT(CASE WHEN o.status = 'BOUGHT' THEN 1 END) as bought_count,
        COUNT(CASE WHEN o.status = 'PACKED' THEN 1 END) as packed_count,
        COUNT(CASE WHEN o.status = 'DELIVERED' THEN 1 END) as delivered_count
      FROM orders o
      LEFT JOIN store_codes sc ON o.store_code_id = sc.id
      WHERE ${this.buildSqlWhereConditions(filters)}
      GROUP BY sc.id, sc.code, sc.name
      ORDER BY total_revenue DESC
    `

    return results.map(row => ({
      storeCode: row.store_code || 'Unknown',
      storeName: row.store_name || 'Unknown Store',
      totalOrders: parseInt(row.total_orders),
      totalRevenue: parseFloat(row.total_revenue),
      averageOrderValue: parseFloat(row.average_order_value),
      orderCount: {
        pending: parseInt(row.pending_count),
        bought: parseInt(row.bought_count),
        packed: parseInt(row.packed_count),
        delivered: parseInt(row.delivered_count)
      }
    }))
  }

  /**
   * Get orders aggregated by customer
   */
  static async getOrdersByCustomer(filters: ReportFilters): Promise<OrdersByCustomerReport[]> {
    const whereClause = this.buildOrderWhereClause(filters)

    const results = await prisma.$queryRaw<any[]>`
      SELECT
        c.id as customer_id,
        c.name as customer_name,
        COUNT(o.id) as total_orders,
        COALESCE(SUM(o.customer_price), 0) as total_spent,
        COALESCE(AVG(o.customer_price), 0) as average_order_value,
        MAX(o.created_at) as last_order_date,
        COUNT(CASE WHEN o.status = 'PENDING' THEN 1 END) as pending_count,
        COUNT(CASE WHEN o.status = 'BOUGHT' THEN 1 END) as bought_count,
        COUNT(CASE WHEN o.status = 'PACKED' THEN 1 END) as packed_count,
        COUNT(CASE WHEN o.status = 'DELIVERED' THEN 1 END) as delivered_count
      FROM orders o
      LEFT JOIN customers c ON o.customer_id = c.id
      WHERE ${this.buildSqlWhereConditions(filters)}
      GROUP BY c.id, c.name
      ORDER BY total_spent DESC
    `

    return results.map(row => ({
      customerId: parseInt(row.customer_id),
      customerName: row.customer_name || 'Unknown Customer',
      totalOrders: parseInt(row.total_orders),
      totalSpent: parseFloat(row.total_spent),
      averageOrderValue: parseFloat(row.average_order_value),
      lastOrderDate: new Date(row.last_order_date),
      orderStatusBreakdown: {
        pending: parseInt(row.pending_count),
        bought: parseInt(row.bought_count),
        packed: parseInt(row.packed_count),
        delivered: parseInt(row.delivered_count)
      }
    }))
  }

  /**
   * Get orders aggregated by product
   */
  static async getOrdersByProduct(filters: ReportFilters): Promise<OrdersByProductReport[]> {
    const whereClause = this.buildOrderWhereClause(filters)

    const results = await prisma.$queryRaw<any[]>`
      SELECT
        o.product_name,
        SUM(o.quantity) as total_quantity,
        COUNT(o.id) as total_orders,
        COALESCE(SUM(o.customer_price * o.quantity), 0) as total_revenue,
        COALESCE(AVG(o.customer_price), 0) as average_price_per_unit,
        COUNT(CASE WHEN o.status = 'PENDING' THEN 1 END) as pending_count,
        COUNT(CASE WHEN o.status = 'BOUGHT' THEN 1 END) as bought_count,
        COUNT(CASE WHEN o.status = 'PACKED' THEN 1 END) as packed_count,
        COUNT(CASE WHEN o.status = 'DELIVERED' THEN 1 END) as delivered_count
      FROM orders o
      WHERE ${this.buildSqlWhereConditions(filters)}
      GROUP BY o.product_name
      ORDER BY total_revenue DESC
    `

    // Get top stores and customers for each product (simplified for example)
    return results.map(row => ({
      productName: row.product_name,
      totalQuantity: parseInt(row.total_quantity),
      totalOrders: parseInt(row.total_orders),
      totalRevenue: parseFloat(row.total_revenue),
      averagePricePerUnit: parseFloat(row.average_price_per_unit),
      orderStatusBreakdown: {
        pending: parseInt(row.pending_count),
        bought: parseInt(row.bought_count),
        packed: parseInt(row.packed_count),
        delivered: parseInt(row.delivered_count)
      },
      topStores: [], // Would be populated with additional query
      topCustomers: [] // Would be populated with additional query
    }))
  }

  /**
   * Get orders aggregated by category
   */
  static async getOrdersByCategory(filters: ReportFilters): Promise<OrdersByCategoryReport[]> {
    const whereClause = this.buildOrderWhereClause(filters)

    const results = await prisma.$queryRaw<any[]>`
      SELECT
        COALESCE(o.category, 'Uncategorized') as category,
        COUNT(o.id) as total_orders,
        SUM(o.quantity) as total_quantity,
        COALESCE(SUM(o.customer_price * o.quantity), 0) as total_revenue,
        COALESCE(AVG(o.customer_price), 0) as average_order_value,
        COUNT(DISTINCT o.product_name) as unique_products,
        COUNT(CASE WHEN o.status = 'PENDING' THEN 1 END) as pending_count,
        COUNT(CASE WHEN o.status = 'BOUGHT' THEN 1 END) as bought_count,
        COUNT(CASE WHEN o.status = 'PACKED' THEN 1 END) as packed_count,
        COUNT(CASE WHEN o.status = 'DELIVERED' THEN 1 END) as delivered_count
      FROM orders o
      WHERE ${this.buildSqlWhereConditions(filters)}
      GROUP BY COALESCE(o.category, 'Uncategorized')
      ORDER BY total_revenue DESC
    `

    return results.map(row => ({
      category: row.category,
      totalOrders: parseInt(row.total_orders),
      totalQuantity: parseInt(row.total_quantity),
      totalRevenue: parseFloat(row.total_revenue),
      averageOrderValue: parseFloat(row.average_order_value),
      uniqueProducts: parseInt(row.unique_products),
      orderStatusBreakdown: {
        pending: parseInt(row.pending_count),
        bought: parseInt(row.bought_count),
        packed: parseInt(row.packed_count),
        delivered: parseInt(row.delivered_count)
      },
      topProducts: [], // Would be populated with additional query
      storeDistribution: [] // Would be populated with additional query
    }))
  }

  /**
   * Export report data
   */
  static async exportReport(
    reportType: 'orders-by-store' | 'orders-by-customer' | 'orders-by-product' | 'orders-by-category',
    format: 'csv' | 'excel' | 'pdf',
    filters: ReportFilters
  ): Promise<Buffer> {
    let data: any[]

    switch (reportType) {
      case 'orders-by-store':
        data = await this.getOrdersByStore(filters)
        break
      case 'orders-by-customer':
        data = await this.getOrdersByCustomer(filters)
        break
      case 'orders-by-product':
        data = await this.getOrdersByProduct(filters)
        break
      case 'orders-by-category':
        data = await this.getOrdersByCategory(filters)
        break
      default:
        throw new Error(`Unsupported report type: ${reportType}`)
    }

    // Use existing export utilities from enhanced services
    const { ExportService } = await import('@/lib/export-service')
    return ExportService.exportData(data, format, {
      filename: `${reportType}-${new Date().toISOString().split('T')[0]}`,
      title: reportType.replace('-', ' ').replace(/\b\w/g, l => l.toUpperCase())
    })
  }

  private static buildOrderWhereClause(filters: ReportFilters): Prisma.OrderWhereInput {
    const where: Prisma.OrderWhereInput = {}

    if (filters.dateRange) {
      where.createdAt = {
        gte: filters.dateRange.from,
        lte: filters.dateRange.to
      }
    }

    if (filters.storeIds?.length) {
      where.storeCodeId = { in: filters.storeIds }
    }

    if (filters.customerIds?.length) {
      where.customerId = { in: filters.customerIds }
    }

    if (filters.statuses?.length) {
      where.status = { in: filters.statuses }
    }

    if (filters.categories?.length) {
      where.category = { in: filters.categories }
    }

    if (filters.productNames?.length) {
      where.productName = { in: filters.productNames }
    }

    return where
  }

  private static buildSqlWhereConditions(filters: ReportFilters): string {
    const conditions: string[] = ['1=1'] // Always true base condition

    if (filters.dateRange) {
      conditions.push(`o.created_at >= '${filters.dateRange.from.toISOString()}'`)
      conditions.push(`o.created_at <= '${filters.dateRange.to.toISOString()}'`)
    }

    if (filters.storeIds?.length) {
      conditions.push(`o.store_code_id IN (${filters.storeIds.join(',')})`)
    }

    if (filters.customerIds?.length) {
      conditions.push(`o.customer_id IN (${filters.customerIds.join(',')})`)
    }

    if (filters.statuses?.length) {
      const statusList = filters.statuses.map(s => `'${s}'`).join(',')
      conditions.push(`o.status IN (${statusList})`)
    }

    if (filters.categories?.length) {
      const categoryList = filters.categories.map(c => `'${c}'`).join(',')
      conditions.push(`o.category IN (${categoryList})`)
    }

    if (filters.productNames?.length) {
      const productList = filters.productNames.map(p => `'${p.replace(/'/g, "''")}'`).join(',')
      conditions.push(`o.product_name IN (${productList})`)
    }

    return conditions.join(' AND ')
  }
}
```

### A.5 Category Service Implementation

```typescript
// src/lib/category-service.ts
import { prisma } from '@/lib/db'

export interface Category {
  id: number
  name: string
  description?: string
  color?: string
  isActive: boolean
  sortOrder: number
  createdAt: Date
  updatedAt: Date
}

export interface CategoryData {
  name: string
  description?: string
  color?: string
  sortOrder?: number
}

export interface CategoryUsageStats {
  categoryId: number
  categoryName: string
  orderCount: number
  totalRevenue: number
  lastUsed: Date
}

export class CategoryService {
  /**
   * Get all active categories
   */
  static async getCategories(): Promise<Category[]> {
    return await prisma.orderCategory.findMany({
      where: { isActive: true },
      orderBy: { sortOrder: 'asc' }
    })
  }

  /**
   * Create a new category
   */
  static async createCategory(data: CategoryData): Promise<Category> {
    // Validate category name uniqueness
    const existing = await prisma.orderCategory.findUnique({
      where: { name: data.name }
    })

    if (existing) {
      throw new Error('Category name already exists')
    }

    // Get next sort order if not provided
    const sortOrder = data.sortOrder ?? await this.getNextSortOrder()

    return await prisma.orderCategory.create({
      data: {
        name: data.name,
        description: data.description,
        color: data.color,
        sortOrder,
        isActive: true
      }
    })
  }

  /**
   * Update an existing category
   */
  static async updateCategory(id: number, data: CategoryData): Promise<Category> {
    // Check if category exists
    const existing = await prisma.orderCategory.findUnique({
      where: { id }
    })

    if (!existing) {
      throw new Error('Category not found')
    }

    // Check name uniqueness if name is being changed
    if (data.name && data.name !== existing.name) {
      const nameExists = await prisma.orderCategory.findUnique({
        where: { name: data.name }
      })

      if (nameExists) {
        throw new Error('Category name already exists')
      }
    }

    return await prisma.orderCategory.update({
      where: { id },
      data: {
        name: data.name,
        description: data.description,
        color: data.color,
        sortOrder: data.sortOrder,
        updatedAt: new Date()
      }
    })
  }

  /**
   * Delete a category (soft delete by setting isActive to false)
   */
  static async deleteCategory(id: number): Promise<void> {
    // Check if category is in use
    const orderCount = await prisma.order.count({
      where: { category: { equals: await this.getCategoryName(id) } }
    })

    if (orderCount > 0) {
      throw new Error('Cannot delete category that is in use by orders')
    }

    await prisma.orderCategory.update({
      where: { id },
      data: { isActive: false }
    })
  }

  /**
   * Get category usage statistics
   */
  static async getCategoryUsageStats(): Promise<CategoryUsageStats[]> {
    const results = await prisma.$queryRaw<any[]>`
      SELECT
        oc.id as category_id,
        oc.name as category_name,
        COUNT(o.id) as order_count,
        COALESCE(SUM(o.customer_price * o.quantity), 0) as total_revenue,
        MAX(o.created_at) as last_used
      FROM order_categories oc
      LEFT JOIN orders o ON o.category = oc.name
      WHERE oc.is_active = true
      GROUP BY oc.id, oc.name
      ORDER BY order_count DESC
    `

    return results.map(row => ({
      categoryId: parseInt(row.category_id),
      categoryName: row.category_name,
      orderCount: parseInt(row.order_count || 0),
      totalRevenue: parseFloat(row.total_revenue || 0),
      lastUsed: row.last_used ? new Date(row.last_used) : new Date()
    }))
  }

  private static async getNextSortOrder(): Promise<number> {
    const lastCategory = await prisma.orderCategory.findFirst({
      orderBy: { sortOrder: 'desc' }
    })

    return (lastCategory?.sortOrder || 0) + 10
  }

  private static async getCategoryName(id: number): Promise<string> {
    const category = await prisma.orderCategory.findUnique({
      where: { id },
      select: { name: true }
    })

    return category?.name || ''
  }
}
```

### A.4 Expense Service Implementation

```typescript
// src/lib/expense-service.ts
import { prisma } from '@/lib/db'
import { Prisma } from '@prisma/client'

export interface ExpenseData {
  categoryId: number
  amount: number
  description: string
  expenseDate: Date
  paymentMethod?: string
  vendor?: string
  storeCodeId?: number
  orderId?: number
  notes?: string
  tags: string[]
  receiptFiles?: File[]
}

export interface ExpenseFilters {
  categoryIds?: number[]
  dateRange?: { from: Date; to: Date }
  amountRange?: { min: number; max: number }
  storeCodeIds?: number[]
  status?: string[]
  searchTerm?: string
}

export class ExpenseService {
  /**
   * Create a new expense record
   */
  static async createExpense(data: ExpenseData): Promise<any> {
    const expenseNumber = await this.generateExpenseNumber()

    return await prisma.expense.create({
      data: {
        expenseNumber,
        categoryId: data.categoryId,
        amount: data.amount,
        description: data.description,
        expenseDate: data.expenseDate,
        paymentMethod: data.paymentMethod,
        vendor: data.vendor,
        storeCodeId: data.storeCodeId,
        orderId: data.orderId,
        notes: data.notes,
        tags: data.tags
      },
      include: {
        category: true,
        storeCode: true,
        order: true
      }
    })
  }

  /**
   * Get expense analytics
   */
  static async getExpenseAnalytics(filters: ExpenseFilters): Promise<any> {
    const whereClause = this.buildWhereClause(filters)

    const [
      totalExpenses,
      expensesByCategory,
      monthlyTrend,
      topVendors
    ] = await Promise.all([
      // Total expenses
      prisma.expense.aggregate({
        where: whereClause,
        _sum: { amount: true },
        _count: true
      }),

      // Expenses by category
      prisma.expense.groupBy({
        by: ['categoryId'],
        where: whereClause,
        _sum: { amount: true },
        _count: true
      }),

      // Monthly trend
      prisma.$queryRaw`
        SELECT
          DATE_TRUNC('month', expense_date) as month,
          SUM(amount) as total_amount,
          COUNT(*) as expense_count
        FROM expenses
        WHERE expense_date >= ${filters.dateRange?.from || new Date(Date.now() - 365 * 24 * 60 * 60 * 1000)}
        AND expense_date <= ${filters.dateRange?.to || new Date()}
        GROUP BY DATE_TRUNC('month', expense_date)
        ORDER BY month
      `,

      // Top vendors
      prisma.expense.groupBy({
        by: ['vendor'],
        where: {
          ...whereClause,
          vendor: { not: null }
        },
        _sum: { amount: true },
        _count: true,
        orderBy: { _sum: { amount: 'desc' } },
        take: 10
      })
    ])

    return {
      totalAmount: totalExpenses._sum.amount || 0,
      totalCount: totalExpenses._count,
      expensesByCategory,
      monthlyTrend,
      topVendors
    }
  }

  /**
   * Import expenses from CSV
   */
  static async importExpensesFromCSV(fileBuffer: Buffer): Promise<any> {
    // Implementation for CSV parsing and bulk import
    // Following established patterns from enhanced-store-service.ts
    const result = {
      totalRows: 0,
      successfulRows: 0,
      failedRows: 0,
      errors: [] as any[]
    }

    // Parse CSV and validate data
    // Bulk create expenses with transaction
    // Return import results

    return result
  }

  private static async generateExpenseNumber(): Promise<string> {
    const count = await prisma.expense.count()
    return `EXP-${String(count + 1).padStart(6, '0')}`
  }

  private static buildWhereClause(filters: ExpenseFilters): Prisma.ExpenseWhereInput {
    const where: Prisma.ExpenseWhereInput = {}

    if (filters.categoryIds?.length) {
      where.categoryId = { in: filters.categoryIds }
    }

    if (filters.dateRange) {
      where.expenseDate = {
        gte: filters.dateRange.from,
        lte: filters.dateRange.to
      }
    }

    if (filters.amountRange) {
      where.amount = {
        gte: filters.amountRange.min,
        lte: filters.amountRange.max
      }
    }

    if (filters.searchTerm) {
      where.OR = [
        { description: { contains: filters.searchTerm } },
        { vendor: { contains: filters.searchTerm } },
        { notes: { contains: filters.searchTerm } }
      ]
    }

    return where
  }
}
```

### A.3 Mobile-First Expense Form

```typescript
// src/components/forms/expense-form.tsx
'use client'

import { useState } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { Button } from '@/components/ui/button'
import { Card } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Combobox } from '@/components/ui/combobox'
import { Form, FormControl, FormField, FormItem, FormMessage } from '@/components/ui/form'
import { ReceiptUpload } from '@/components/forms/receipt-upload'
import { TagInput } from '@/components/ui/tag-input'

const expenseSchema = z.object({
  categoryId: z.number().min(1, 'Category is required'),
  amount: z.number().min(0.01, 'Amount must be greater than 0'),
  description: z.string().min(1, 'Description is required'),
  expenseDate: z.string().min(1, 'Date is required'),
  paymentMethod: z.string().optional(),
  vendor: z.string().optional(),
  storeCodeId: z.number().optional(),
  notes: z.string().optional(),
  tags: z.array(z.string()).default([])
})

type ExpenseFormData = z.infer<typeof expenseSchema>

interface ExpenseFormProps {
  onSubmit: (data: ExpenseFormData & { receiptFiles: File[] }) => Promise<void>
  onCancel: () => void
  categories: Array<{ id: number; name: string }>
  stores: Array<{ id: number; code: string; name: string }>
  isLoading?: boolean
}

export function ExpenseForm({ onSubmit, onCancel, categories, stores, isLoading }: ExpenseFormProps) {
  const [receiptFiles, setReceiptFiles] = useState<File[]>([])

  const form = useForm<ExpenseFormData>({
    resolver: zodResolver(expenseSchema),
    defaultValues: {
      expenseDate: new Date().toISOString().split('T')[0],
      tags: []
    }
  })

  const handleSubmit = async (data: ExpenseFormData) => {
    await onSubmit({ ...data, receiptFiles })
  }

  const categoryOptions = categories.map(cat => ({
    value: cat.id.toString(),
    label: cat.name
  }))

  const storeOptions = stores.map(store => ({
    value: store.id.toString(),
    label: `${store.code} - ${store.name || 'Unnamed Store'}`
  }))

  const paymentMethodOptions = [
    { value: 'cash', label: 'Cash' },
    { value: 'bank_transfer', label: 'Bank Transfer' },
    { value: 'credit_card', label: 'Credit Card' },
    { value: 'gcash', label: 'GCash' },
    { value: 'paymaya', label: 'PayMaya' }
  ]

  return (
    <Card className="p-4">
      <Form {...form}>
        <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
          {/* Basic Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <FormField
              control={form.control}
              name="categoryId"
              render={({ field }) => (
                <FormItem>
                  <label className="text-sm font-medium">Category *</label>
                  <FormControl>
                    <Combobox
                      options={categoryOptions}
                      value={field.value?.toString() || ""}
                      onValueChange={(value) => field.onChange(parseInt(value))}
                      placeholder="Select category..."
                      className="min-h-[44px] text-base"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="amount"
              render={({ field }) => (
                <FormItem>
                  <label className="text-sm font-medium">Amount *</label>
                  <FormControl>
                    <div className="relative">
                      <span className="absolute left-3 top-1/2 -translate-y-1/2 text-muted-foreground text-base">₱</span>
                      <Input
                        type="number"
                        step="0.01"
                        min="0"
                        className="min-h-[44px] text-base pl-8"
                        {...field}
                        onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                      />
                    </div>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="expenseDate"
              render={({ field }) => (
                <FormItem>
                  <label className="text-sm font-medium">Date *</label>
                  <FormControl>
                    <Input
                      type="date"
                      className="min-h-[44px] text-base"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="paymentMethod"
              render={({ field }) => (
                <FormItem>
                  <label className="text-sm font-medium">Payment Method</label>
                  <FormControl>
                    <Combobox
                      options={paymentMethodOptions}
                      value={field.value || ""}
                      onValueChange={field.onChange}
                      placeholder="Select payment method..."
                      className="min-h-[44px] text-base"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          {/* Description and Details */}
          <div className="grid grid-cols-1 gap-4">
            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <label className="text-sm font-medium">Description *</label>
                  <FormControl>
                    <Input
                      placeholder="What was this expense for?"
                      className="min-h-[44px] text-base"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="vendor"
              render={({ field }) => (
                <FormItem>
                  <label className="text-sm font-medium">Vendor/Supplier</label>
                  <FormControl>
                    <Input
                      placeholder="Who did you pay?"
                      className="min-h-[44px] text-base"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="storeCodeId"
              render={({ field }) => (
                <FormItem>
                  <label className="text-sm font-medium">Related Store</label>
                  <FormControl>
                    <Combobox
                      options={storeOptions}
                      value={field.value?.toString() || ""}
                      onValueChange={(value) => field.onChange(value ? parseInt(value) : undefined)}
                      placeholder="Select store (optional)..."
                      className="min-h-[44px] text-base"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="tags"
              render={({ field }) => (
                <FormItem>
                  <label className="text-sm font-medium">Tags</label>
                  <FormControl>
                    <TagInput
                      value={field.value}
                      onChange={field.onChange}
                      placeholder="Add tags for better organization..."
                      className="min-h-[44px]"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="notes"
              render={({ field }) => (
                <FormItem>
                  <label className="text-sm font-medium">Notes</label>
                  <FormControl>
                    <Textarea
                      placeholder="Additional notes or details..."
                      className="min-h-[80px] text-base"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          {/* Receipt Upload */}
          <div>
            <label className="text-sm font-medium mb-2 block">Receipts & Documents</label>
            <ReceiptUpload
              files={receiptFiles}
              onFilesChange={setReceiptFiles}
              maxFiles={5}
              maxSize={10 * 1024 * 1024} // 10MB
            />
          </div>

          {/* Form Actions */}
          <div className="flex flex-col sm:flex-row gap-3 sm:gap-4">
            <Button
              type="submit"
              disabled={isLoading}
              className="flex-1 min-h-[48px] text-base font-medium order-1 sm:order-2"
            >
              {isLoading ? 'Saving...' : 'Save Expense'}
            </Button>
            <Button
              type="button"
              variant="outline"
              onClick={onCancel}
              disabled={isLoading}
              className="flex-1 min-h-[48px] text-base font-medium order-2 sm:order-1"
            >
              Cancel
            </Button>
          </div>
        </form>
      </Form>
    </Card>
  )
}
```

---

## Appendix B: Database Migration Scripts

### B.1 Order Category Enhancement Schema

```sql
-- Migration: 001_add_category_to_orders.sql
-- Add category field to existing orders table
ALTER TABLE orders ADD COLUMN category TEXT;

-- Create index for category filtering performance
CREATE INDEX idx_orders_category ON orders(category);

-- Create categories lookup table for standardization
CREATE TABLE order_categories (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  name TEXT UNIQUE NOT NULL,
  description TEXT,
  color TEXT, -- For UI visualization
  is_active BOOLEAN DEFAULT TRUE,
  sort_order INTEGER DEFAULT 0,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- Insert default categories
INSERT INTO order_categories (name, description, color, sort_order) VALUES
('Electronics', 'Electronic devices and gadgets', '#3B82F6', 1),
('Clothing', 'Apparel and fashion items', '#10B981', 2),
('Food & Beverages', 'Food items and drinks', '#F59E0B', 3),
('Books & Media', 'Books, magazines, and media content', '#8B5CF6', 4),
('Home & Garden', 'Home improvement and gardening items', '#06B6D4', 5),
('Health & Beauty', 'Health and beauty products', '#EF4444', 6),
('Sports & Recreation', 'Sports equipment and recreational items', '#84CC16', 7),
('Automotive', 'Car parts and automotive accessories', '#6B7280', 8),
('Office Supplies', 'Office and business supplies', '#F97316', 9),
('Toys & Games', 'Toys and gaming products', '#EC4899', 10),
('Other', 'Miscellaneous items', '#64748B', 99);

-- Create index for category lookup performance
CREATE INDEX idx_order_categories_name ON order_categories(name);
CREATE INDEX idx_order_categories_active ON order_categories(is_active);
CREATE INDEX idx_order_categories_sort_order ON order_categories(sort_order);
```

### B.2 Expense Management Schema

```sql
-- Migration: 002_create_expense_categories.sql
CREATE TABLE expense_categories (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  name TEXT UNIQUE NOT NULL,
  description TEXT,
  parent_category_id INTEGER,
  color TEXT,
  is_active BOOLEAN DEFAULT TRUE,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (parent_category_id) REFERENCES expense_categories(id)
);

-- Insert default categories
INSERT INTO expense_categories (name, description, color) VALUES
('Office Supplies', 'Stationery, equipment, and office materials', '#3B82F6'),
('Transportation', 'Travel, fuel, and delivery costs', '#10B981'),
('Marketing', 'Advertising and promotional expenses', '#F59E0B'),
('Utilities', 'Internet, electricity, and other utilities', '#EF4444'),
('Professional Services', 'Legal, accounting, and consulting fees', '#8B5CF6'),
('Equipment', 'Hardware, software, and tools', '#06B6D4'),
('Miscellaneous', 'Other business expenses', '#6B7280');

-- Migration: 002_create_expenses.sql
CREATE TABLE expenses (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  expense_number TEXT UNIQUE NOT NULL,
  category_id INTEGER NOT NULL,
  amount REAL NOT NULL,
  currency TEXT DEFAULT 'PHP',
  description TEXT NOT NULL,
  expense_date DATETIME NOT NULL,
  payment_method TEXT,
  vendor TEXT,
  receipt_url TEXT,
  notes TEXT,
  tags TEXT, -- JSON array as text

  -- Business context
  store_code_id INTEGER,
  order_id INTEGER,
  customer_id INTEGER,

  -- Approval workflow
  status TEXT DEFAULT 'PENDING',
  approved_by TEXT,
  approved_at DATETIME,
  rejected_reason TEXT,

  -- Financial tracking
  tax_amount REAL DEFAULT 0.00,
  tax_rate REAL DEFAULT 0.00,
  is_recurring BOOLEAN DEFAULT FALSE,
  recurring_frequency TEXT,
  next_recurring_date DATETIME,

  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,

  FOREIGN KEY (category_id) REFERENCES expense_categories(id),
  FOREIGN KEY (store_code_id) REFERENCES store_codes(id),
  FOREIGN KEY (order_id) REFERENCES orders(id),
  FOREIGN KEY (customer_id) REFERENCES customers(id)
);

-- Create indexes for performance
CREATE INDEX idx_expenses_category_id ON expenses(category_id);
CREATE INDEX idx_expenses_expense_date ON expenses(expense_date);
CREATE INDEX idx_expenses_status ON expenses(status);
CREATE INDEX idx_expenses_store_code_id ON expenses(store_code_id);
CREATE INDEX idx_expenses_amount ON expenses(amount);
CREATE INDEX idx_expenses_created_at ON expenses(created_at);

-- Migration: 003_create_expense_attachments.sql
CREATE TABLE expense_attachments (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  expense_id INTEGER NOT NULL,
  filename TEXT NOT NULL,
  original_name TEXT NOT NULL,
  mime_type TEXT NOT NULL,
  file_size INTEGER NOT NULL,
  uploaded_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (expense_id) REFERENCES expenses(id) ON DELETE CASCADE
);

CREATE INDEX idx_expense_attachments_expense_id ON expense_attachments(expense_id);
```

### B.2 Enhanced Payment Tracking Schema

```sql
-- Migration: 004_create_payment_records.sql
CREATE TABLE payment_records (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  payment_number TEXT UNIQUE NOT NULL,

  -- Payment details
  amount REAL NOT NULL,
  currency TEXT DEFAULT 'PHP',
  payment_method TEXT NOT NULL,
  payment_date DATETIME NOT NULL,

  -- References
  invoice_id INTEGER,
  customer_id INTEGER NOT NULL,
  order_id INTEGER,

  -- Payment tracking
  reference TEXT,
  status TEXT DEFAULT 'COMPLETED',
  notes TEXT,

  -- Reconciliation
  is_reconciled BOOLEAN DEFAULT FALSE,
  reconciled_at DATETIME,
  reconciled_by TEXT,
  bank_statement_ref TEXT,

  -- Fees and adjustments
  processing_fee REAL DEFAULT 0.00,
  adjustment_amount REAL DEFAULT 0.00,
  adjustment_reason TEXT,

  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,

  FOREIGN KEY (invoice_id) REFERENCES invoices(id),
  FOREIGN KEY (customer_id) REFERENCES customers(id),
  FOREIGN KEY (order_id) REFERENCES orders(id)
);

-- Create indexes
CREATE INDEX idx_payment_records_payment_date ON payment_records(payment_date);
CREATE INDEX idx_payment_records_customer_id ON payment_records(customer_id);
CREATE INDEX idx_payment_records_invoice_id ON payment_records(invoice_id);
CREATE INDEX idx_payment_records_status ON payment_records(status);
CREATE INDEX idx_payment_records_is_reconciled ON payment_records(is_reconciled);

-- Migration: 005_create_bank_reconciliation.sql
CREATE TABLE bank_reconciliation (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  reconciliation_date DATETIME NOT NULL,
  bank_account TEXT NOT NULL,
  statement_balance REAL NOT NULL,
  system_balance REAL NOT NULL,
  difference REAL NOT NULL,
  status TEXT DEFAULT 'PENDING',
  notes TEXT,
  reconciled_by TEXT,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX idx_bank_reconciliation_date ON bank_reconciliation(reconciliation_date);
CREATE INDEX idx_bank_reconciliation_status ON bank_reconciliation(status);
```

---

*This comprehensive document serves as both a technical specification and project management guide for implementing business intelligence and tracking features in the PasaBuy Pal system.*

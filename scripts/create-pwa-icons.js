const fs = require('fs')
const path = require('path')

// Create icons directory if it doesn't exist
const iconsDir = path.join(process.cwd(), 'public', 'icons')
if (!fs.existsSync(iconsDir)) {
  fs.mkdirSync(iconsDir, { recursive: true })
}

// Icon sizes required by the manifest.json
const iconSizes = [72, 96, 128, 144, 152, 192, 384, 512]

// App branding
const appName = 'Pasabuy'
const brandColor = '#2563eb' // Blue color
const textColor = '#ffffff'

function createSVGIcon(size, text) {
  const fontSize = size < 128 ? Math.floor(size * 0.15) : Math.floor(size * 0.12)
  const subFontSize = Math.floor(fontSize * 0.6)
  
  return `<?xml version="1.0" encoding="UTF-8"?>
<svg width="${size}" height="${size}" xmlns="http://www.w3.org/2000/svg">
  <!-- Background -->
  <rect width="${size}" height="${size}" fill="${brandColor}" rx="${size * 0.1}"/>
  
  <!-- Shopping bag icon -->
  <g transform="translate(${size * 0.25}, ${size * 0.2})">
    <rect x="0" y="${size * 0.15}" width="${size * 0.5}" height="${size * 0.45}" 
          fill="none" stroke="${textColor}" stroke-width="${size * 0.02}" rx="${size * 0.02}"/>
    <path d="M ${size * 0.125} ${size * 0.15} 
             C ${size * 0.125} ${size * 0.08} ${size * 0.2} ${size * 0.05} ${size * 0.25} ${size * 0.05}
             C ${size * 0.3} ${size * 0.05} ${size * 0.375} ${size * 0.08} ${size * 0.375} ${size * 0.15}"
          fill="none" stroke="${textColor}" stroke-width="${size * 0.02}"/>
  </g>
  
  <!-- App name -->
  <text x="${size * 0.5}" y="${size * 0.8}" 
        font-family="Arial, sans-serif" 
        font-size="${fontSize}" 
        font-weight="bold" 
        text-anchor="middle" 
        fill="${textColor}">${text}</text>
  
  <!-- Subtitle for larger icons -->
  ${size >= 144 ? `<text x="${size * 0.5}" y="${size * 0.9}" 
        font-family="Arial, sans-serif" 
        font-size="${subFontSize}" 
        text-anchor="middle" 
        fill="${textColor}" 
        opacity="0.9">PAL</text>` : ''}
</svg>`
}

function createIcon(size) {
  const filename = `icon-${size}x${size}.png`
  const filePath = path.join(iconsDir, filename)
  
  // Skip if file already exists
  if (fs.existsSync(filePath)) {
    console.log(`✅ ${filename} already exists, skipping...`)
    return
  }

  try {
    const svgContent = createSVGIcon(size, appName)
    
    // For now, save as SVG with PNG extension
    // In a production environment, you'd want to convert SVG to actual PNG
    // using a library like sharp or puppeteer
    fs.writeFileSync(filePath, svgContent)
    
    console.log(`✅ Created: ${filename} (${size}x${size})`)
  } catch (error) {
    console.error(`❌ Error creating ${filename}:`, error.message)
  }
}

function createAllIcons() {
  console.log('🎨 Creating PWA icons for Pasabuy Pal...')
  console.log(`📁 Saving to: ${iconsDir}`)
  console.log(`🎯 Brand color: ${brandColor}`)

  try {
    for (const size of iconSizes) {
      createIcon(size)
    }

    console.log('\n🎉 All PWA icons created successfully!')
    console.log(`📊 Total icons: ${iconSizes.length}`)
    console.log('\n💡 Note: These are SVG icons saved with .png extension.')
    console.log('   For production, consider converting to actual PNG format.')
    console.log('\n🔧 To convert to real PNG files, you can use:')
    console.log('   - Sharp library (npm install sharp)')
    console.log('   - Online SVG to PNG converters')
    console.log('   - Design tools like Figma or Sketch')
  } catch (error) {
    console.error('❌ Error creating icons:', error)
    process.exit(1)
  }
}

// Run the creation
createAllIcons()

// Predefined usage units for items
export const PREDEFINED_USAGE_UNITS = [
  'pcs',
  'kg',
  'grams',
  'liters',
  'ml',
  'set',
  'pair',
  'box',
  'bottle',
  'pack',
  'meter',
  'cm',
  'inch',
  'feet',
  'dozen',
  'roll',
  'sheet',
  'bag',
  'can',
  'jar'
] as const

export type PredefinedUsageUnit = typeof PREDEFINED_USAGE_UNITS[number]

// Helper function to get usage unit options for combobox
export function getUsageUnitOptions(): Array<{ value: string; label: string }> {
  return PREDEFINED_USAGE_UNITS.map(unit => ({
    value: unit,
    label: unit
  }))
}

// Helper function to check if a usage unit is predefined
export function isPredefinedUsageUnit(unit: string): unit is PredefinedUsageUnit {
  return PREDEFINED_USAGE_UNITS.includes(unit as PredefinedUsageUnit)
}

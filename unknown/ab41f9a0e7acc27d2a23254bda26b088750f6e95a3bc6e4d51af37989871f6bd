import { create } from 'zustand'
import { devtools } from 'zustand/middleware'

// Types
export interface StoreCode {
  id: number
  code: string
  name: string | null
  _count?: {
    orders: number
  }
}

export interface Customer {
  id: number
  name: string
  _count?: {
    orders: number
    toBuy: number
    toPack: number
  }
}

export interface Order {
  id: number
  productName: string
  quantity: number
  usageUnit?: string | null
  comment?: string | null
  imageFilename: string | null
  storePrice: number
  pasabuyFee: number
  customerPrice: number
  isBought: boolean
  packingStatus: string
  storeCode?: StoreCode | null
  storeCodeId: number | null
  customer?: Customer | null
  customerId: number | null
  createdAt: string
  updatedAt: string
}

export type InvoiceStatus = 'DRAFT' | 'SENT' | 'PAID' | 'OVERDUE' | 'CANCELLED'

export interface Invoice {
  id: number
  invoiceNumber: string
  customer?: Customer | null
  customerId: number
  status: InvoiceStatus
  subtotal: number
  total: number
  dueDate?: string | null
  paidDate?: string | null
  notes?: string | null
  invoiceItems?: InvoiceItem[]
  createdAt: string
  updatedAt: string
}

export interface InvoiceItem {
  id: number
  invoiceId: number
  order?: Order | null
  orderId: number
  quantity: number
  unitPrice: number
  totalPrice: number
  createdAt: string
}

// Store State
interface AppState {
  // Data
  storeCodes: StoreCode[]
  customers: Customer[]
  orders: Order[]
  invoices: Invoice[]

  // Loading states
  isLoading: boolean
  isLoadingStoreCodes: boolean
  isLoadingCustomers: boolean
  isLoadingOrders: boolean
  isLoadingInvoices: boolean

  // Actions
  setStoreCodes: (storeCodes: StoreCode[]) => void
  setCustomers: (customers: Customer[]) => void
  setOrders: (orders: Order[]) => void
  setInvoices: (invoices: Invoice[]) => void
  addStoreCode: (storeCode: StoreCode) => void
  updateStoreCode: (storeCode: StoreCode) => void
  removeStoreCode: (id: number) => void
  addCustomer: (customer: Customer) => void
  addOrder: (order: Order) => void
  updateOrder: (id: number, updates: Partial<Order>) => void
  removeOrder: (id: number) => void
  addInvoice: (invoice: Invoice) => void
  updateInvoice: (id: number, updates: Partial<Invoice>) => void
  removeInvoice: (id: number) => void
  setLoading: (loading: boolean) => void
  setLoadingStoreCodes: (loading: boolean) => void
  setLoadingCustomers: (loading: boolean) => void
  setLoadingOrders: (loading: boolean) => void
  setLoadingInvoices: (loading: boolean) => void

  // Computed getters
  getOrdersByStoreCode: (storeCodeId: number | null) => Order[]
  getOrdersByCustomer: (customerId: number | null) => Order[]
  getUnboughtOrders: () => Order[]
  getOrdersToPackByCustomer: (customerId: number | null) => Order[]
  getInvoicesByCustomer: (customerId: number | null) => Invoice[]
  getInvoicesByStatus: (status: InvoiceStatus) => Invoice[]
}

export const useAppStore = create<AppState>()(
  devtools(
    (set, get) => ({
      // Initial state
      storeCodes: [],
      customers: [],
      orders: [],
      invoices: [],
      isLoading: false,
      isLoadingStoreCodes: false,
      isLoadingCustomers: false,
      isLoadingOrders: false,
      isLoadingInvoices: false,

      // Actions
      setStoreCodes: (storeCodes) => set({ storeCodes }),
      setCustomers: (customers) => set({ customers }),
      setOrders: (orders) => set({ orders }),
      setInvoices: (invoices) => set({ invoices }),

      addStoreCode: (storeCode) =>
        set((state) => ({ storeCodes: [...state.storeCodes, storeCode] })),

      updateStoreCode: (storeCode) =>
        set((state) => ({
          storeCodes: state.storeCodes.map((sc) =>
            sc.id === storeCode.id ? storeCode : sc
          ),
        })),

      removeStoreCode: (id) =>
        set((state) => ({
          storeCodes: state.storeCodes.filter((sc) => sc.id !== id),
        })),

      addCustomer: (customer) =>
        set((state) => ({ customers: [...state.customers, customer] })),

      addOrder: (order) =>
        set((state) => ({ orders: [...(Array.isArray(state.orders) ? state.orders : []), order] })),

      updateOrder: (id, updates) =>
        set((state) => ({
          orders: (Array.isArray(state.orders) ? state.orders : []).map((order) =>
            order.id === id ? { ...order, ...updates } : order
          ),
        })),

      removeOrder: (id) =>
        set((state) => ({
          orders: (Array.isArray(state.orders) ? state.orders : []).filter((order) => order.id !== id),
        })),

      addInvoice: (invoice) =>
        set((state) => ({ invoices: [...(Array.isArray(state.invoices) ? state.invoices : []), invoice] })),

      updateInvoice: (id, updates) =>
        set((state) => ({
          invoices: (Array.isArray(state.invoices) ? state.invoices : []).map((invoice) =>
            invoice.id === id ? { ...invoice, ...updates } : invoice
          ),
        })),

      removeInvoice: (id) =>
        set((state) => ({
          invoices: (Array.isArray(state.invoices) ? state.invoices : []).filter((invoice) => invoice.id !== id),
        })),

      setLoading: (loading) => set({ isLoading: loading }),
      setLoadingStoreCodes: (loading) => set({ isLoadingStoreCodes: loading }),
      setLoadingCustomers: (loading) => set({ isLoadingCustomers: loading }),
      setLoadingOrders: (loading) => set({ isLoadingOrders: loading }),
      setLoadingInvoices: (loading) => set({ isLoadingInvoices: loading }),

      // Computed getters
      getOrdersByStoreCode: (storeCodeId) => {
        const { orders } = get()
        return (Array.isArray(orders) ? orders : []).filter((order) => order.storeCodeId === storeCodeId)
      },

      getOrdersByCustomer: (customerId) => {
        const { orders } = get()
        return (Array.isArray(orders) ? orders : []).filter((order) => order.customerId === customerId)
      },

      getUnboughtOrders: () => {
        const { orders } = get()
        return (Array.isArray(orders) ? orders : []).filter((order) => !order.isBought)
      },

      getOrdersToPackByCustomer: (customerId) => {
        const { orders } = get()
        return (Array.isArray(orders) ? orders : []).filter(
          (order) =>
            order.customerId === customerId &&
            order.isBought &&
            order.packingStatus === 'Not Packed'
        )
      },

      getInvoicesByCustomer: (customerId) => {
        const { invoices } = get()
        return (Array.isArray(invoices) ? invoices : []).filter((invoice) => invoice.customerId === customerId)
      },

      getInvoicesByStatus: (status) => {
        const { invoices } = get()
        return (Array.isArray(invoices) ? invoices : []).filter((invoice) => invoice.status === status)
      },
    }),
    {
      name: 'pasabuy-store',
    }
  )
)

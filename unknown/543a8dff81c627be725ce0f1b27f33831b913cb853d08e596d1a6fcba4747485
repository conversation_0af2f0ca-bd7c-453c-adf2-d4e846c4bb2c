import React from 'react'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { TextFilter as TextFilterType, TextOperator } from '@/lib/filter-types'

interface TextFilterProps {
  label: string
  value?: TextFilterType
  onChange: (value: TextFilterType | undefined) => void
  placeholder?: string
}

const operatorLabels: Record<TextOperator, string> = {
  contains: 'Contains',
  startsWith: 'Starts with',
  endsWith: 'Ends with',
  equals: 'Equals',
  notContains: 'Does not contain'
}

export function TextFilter({ label, value, onChange, placeholder }: TextFilterProps) {
  const handleValueChange = (newValue: string) => {
    if (!newValue.trim()) {
      onChange(undefined)
      return
    }

    onChange({
      value: newValue,
      operator: value?.operator || 'contains',
      caseSensitive: value?.caseSensitive || false
    })
  }

  const handleOperatorChange = (operator: TextOperator) => {
    if (!value?.value) return

    onChange({
      ...value,
      operator
    })
  }

  return (
    <div className="space-y-2">
      <Label className="text-xs font-medium">{label}</Label>

      <div className="space-y-2">
        <Input
          placeholder={placeholder || `Search ${label.toLowerCase()}...`}
          value={value?.value || ''}
          onChange={(e) => handleValueChange(e.target.value)}
          className="h-10 md:h-8 touch-target"
        />

        {value?.value && (
          <Select
            value={value.operator}
            onValueChange={handleOperatorChange}
          >
            <SelectTrigger className="h-10 md:h-8 touch-target">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              {Object.entries(operatorLabels).map(([op, label]) => (
                <SelectItem key={op} value={op}>
                  {label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        )}
      </div>
    </div>
  )
}

# 🛍️ Pasabuy Pal

A comprehensive web application for managing pasabuy (personal shopping) business operations. Built with Next.js, TypeScript, Prisma, and modern UI components.

## ✨ Features

### 🏪 Store Management
- Add and manage store codes (SM, Ayala, etc.)
- Store-specific buy lists
- Track orders by store location

### 👥 Customer Management
- Manage customer profiles
- Customer-specific order tracking
- Status indicators for each customer

### 📦 Order Management
- Add orders with images
- Price calculations with manual customer price entry
- Quantity tracking
- Status management (bought/not bought, packed/not packed)

### 📋 Buy Lists
- Overview of all orders to buy
- Store-specific shopping lists
- Real-time status updates
- Quick "Mark as Bought" functionality

### 📦 Packing Workflow
- Customer-specific packing lists
- Track packing status
- Bulk packing operations
- Visual progress indicators

### 📱 Progressive Web App (PWA)
- Mobile-responsive design
- Offline capabilities
- App-like experience on mobile devices

## 🚀 Tech Stack

- **Frontend**: Next.js 15, React, TypeScript
- **UI**: Tailwind CSS, shadcn/ui components
- **Database**: SQLite (development) / PostgreSQL (production)
- **ORM**: Prisma
- **State Management**: Zustand
- **Forms**: React Hook Form
- **Icons**: Lucide React
- **PWA**: next-pwa

## 🛠️ Installation

1. **Clone the repository**
   ```bash
   git clone https://github.com/YOUR_USERNAME/pasabuy-pal.git
   cd pasabuy-pal
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Set up environment variables**
   ```bash
   cp .env.example .env
   ```

   Update the `.env` file with your database URL:
   ```env
   DATABASE_URL="file:./dev.db"
   NEXT_PUBLIC_APP_URL="http://localhost:3000"
   ```

4. **Set up the database**
   ```bash
   npx prisma generate
   npx prisma migrate dev --name init
   ```

5. **Start the development server**
   ```bash
   npm run dev
   ```

6. **Open your browser**
   Navigate to [http://localhost:3000](http://localhost:3000)

## 📁 Project Structure

```
pasabuy-pal/
├── src/
│   ├── app/                    # Next.js app router
│   │   ├── api/               # API routes
│   │   ├── buy-list/          # Buy list pages
│   │   ├── orders/            # Order management
│   │   ├── packing/           # Packing workflow
│   │   ├── customers/         # Customer management
│   │   └── stores/            # Store management
│   ├── components/            # Reusable components
│   │   ├── forms/             # Form components
│   │   └── ui/                # UI components (shadcn/ui)
│   └── lib/                   # Utilities and configurations
├── prisma/                    # Database schema and migrations
├── public/                    # Static assets
└── uploads/                   # User uploaded files
```

## 🗄️ Database Schema

The application uses three main entities:

- **StoreCodes**: Store locations (SM, Ayala, etc.)
- **Customers**: People you're buying for
- **Orders**: Products with pricing and status tracking

## 🔧 API Endpoints

### Store Codes
- `GET /api/store-codes` - List all store codes
- `POST /api/store-codes` - Create new store code

### Customers
- `GET /api/customers` - List all customers
- `POST /api/customers` - Create new customer

### Orders
- `GET /api/orders` - List orders (with filtering)
- `POST /api/orders` - Create new order
- `PUT /api/orders/[id]` - Update order
- `DELETE /api/orders/[id]` - Delete order

### File Upload
- `POST /api/upload` - Upload order images

## 🚀 Deployment

### Vercel (Recommended)

1. **Push to GitHub**
2. **Connect to Vercel**
3. **Set environment variables**
4. **Deploy**

### Docker

```bash
# Build the image
docker build -t pasabuy-pal .

# Run with PostgreSQL
docker-compose up -d
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📝 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- Built with [Next.js](https://nextjs.org/)
- UI components from [shadcn/ui](https://ui.shadcn.com/)
- Icons from [Lucide](https://lucide.dev/)
- Database ORM by [Prisma](https://prisma.io/)

---

Made with ❤️ for the pasabuy community

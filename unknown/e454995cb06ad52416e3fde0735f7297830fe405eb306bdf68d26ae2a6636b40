import { useEffect, useCallback, useState } from 'react'

interface Customer {
  id: number
  name: string
  _count?: {
    orders: number
  }
}

interface StoreCode {
  id: number
  code: string
  name: string | null
  _count?: {
    orders: number
  }
}

interface FilterOptionsData {
  customers: Array<{ id: number; name: string; count: number }>
  storeCodes: Array<{ id: number; name: string; count: number }>
}

interface UseFilterOptionsReturn extends FilterOptionsData {
  isLoading: boolean
  error: string | null
  refetch: () => void
}

export function useFilterOptions(): UseFilterOptionsReturn {
  const [customers, setCustomers] = useState<Array<{ id: number; name: string; count: number }>>([])
  const [storeCodes, setStoreCodes] = useState<Array<{ id: number; name: string; count: number }>>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const fetchOptions = useCallback(async () => {
    setIsLoading(true)
    setError(null)

    try {
      const [customersResponse, storeCodesResponse] = await Promise.all([
        fetch('/api/customers'),
        fetch('/api/store-codes')
      ])

      if (!customersResponse.ok || !storeCodesResponse.ok) {
        throw new Error('Failed to fetch filter options')
      }

      const [customersData, storeCodesData] = await Promise.all([
        customersResponse.json() as Promise<Customer[]>,
        storeCodesResponse.json() as Promise<StoreCode[]>
      ])

      // Transform customers data
      const transformedCustomers = customersData.map(customer => ({
        id: customer.id,
        name: customer.name,
        count: customer._count?.orders || 0
      }))

      // Transform store codes data
      const transformedStoreCodes = storeCodesData.map(storeCode => ({
        id: storeCode.id,
        name: storeCode.name || storeCode.code, // Use name if available, otherwise code
        count: storeCode._count?.orders || 0
      }))

      setCustomers(transformedCustomers)
      setStoreCodes(transformedStoreCodes)

    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred')
    } finally {
      setIsLoading(false)
    }
  }, [])

  useEffect(() => {
    fetchOptions()
  }, [fetchOptions])

  return {
    customers,
    storeCodes,
    isLoading,
    error,
    refetch: fetchOptions
  }
}

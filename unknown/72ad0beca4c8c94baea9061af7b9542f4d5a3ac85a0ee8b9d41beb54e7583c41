import jsPDF from 'jspdf'
import autoTable from 'jspdf-autotable'
import { Invoice } from './store'

export function generateInvoicePDF(invoice: Invoice) {
  const doc = new jsPDF()

  // Company header
  doc.setFontSize(20)
  doc.setFont('helvetica', 'bold')
  doc.text('PasaBuy Pal', 20, 30)

  doc.setFontSize(10)
  doc.setFont('helvetica', 'normal')
  doc.text('Personal Shopping Service', 20, 38)

  // Invoice title and number
  doc.setFontSize(16)
  doc.setFont('helvetica', 'bold')
  doc.text('INVOICE', 150, 30)

  doc.setFontSize(12)
  doc.setFont('helvetica', 'normal')
  doc.text(`Invoice #: ${invoice.invoiceNumber}`, 150, 40)
  doc.text(`Date: ${formatDate(invoice.createdAt)}`, 150, 48)

  if (invoice.dueDate) {
    doc.text(`Due Date: ${formatDate(invoice.dueDate)}`, 150, 56)
  }

  // Bill to section
  doc.setFontSize(12)
  doc.setFont('helvetica', 'bold')
  doc.text('Bill To:', 20, 70)

  doc.setFont('helvetica', 'normal')
  doc.text(invoice.customer?.name || 'Unknown Customer', 20, 80)

  // Status
  doc.setFontSize(10)
  doc.setFont('helvetica', 'bold')
  const statusColor = getStatusColor(invoice.status)
  doc.setTextColor(statusColor.r, statusColor.g, statusColor.b)
  doc.text(`Status: ${getStatusLabel(invoice.status)}`, 150, 70)
  doc.setTextColor(0, 0, 0) // Reset to black

  // Invoice items table
  const tableData = invoice.invoiceItems?.map(order => [
    order.order?.productName || 'Unknown Order',
    order.quantity.toString(),
    formatCurrency(order.unitPrice),
    formatCurrency(order.totalPrice)
  ]) || []

  autoTable(doc, {
    startY: 100,
    head: [['Order', 'Qty', 'Unit Price', 'Total']],
    body: tableData,
    theme: 'grid',
    headStyles: {
      fillColor: [41, 128, 185],
      textColor: 255,
      fontStyle: 'bold'
    },
    styles: {
      fontSize: 10,
      cellPadding: 5
    },
    columnStyles: {
      1: { halign: 'center' },
      2: { halign: 'right' },
      3: { halign: 'right' }
    }
  })

  // Get the final Y position after the table
  const finalY = (doc as any).lastAutoTable.finalY || 150

  // Totals section
  const totalsStartY = finalY + 20
  const rightAlign = 150

  doc.setFontSize(10)
  doc.setFont('helvetica', 'normal')
  doc.text('Subtotal:', rightAlign, totalsStartY)
  doc.text(formatCurrency(invoice.subtotal), rightAlign + 30, totalsStartY)

  doc.setFontSize(12)
  doc.setFont('helvetica', 'bold')
  doc.text('Total:', rightAlign, totalsStartY + 10)
  doc.text(formatCurrency(invoice.total), rightAlign + 30, totalsStartY + 10)

  // Notes section
  if (invoice.notes) {
    doc.setFontSize(10)
    doc.setFont('helvetica', 'bold')
    doc.text('Notes:', 20, totalsStartY + 30)

    doc.setFont('helvetica', 'normal')
    const splitNotes = doc.splitTextToSize(invoice.notes, 170)
    doc.text(splitNotes, 20, totalsStartY + 40)
  }

  // Footer
  const pageHeight = doc.internal.pageSize.height
  doc.setFontSize(8)
  doc.setFont('helvetica', 'italic')
  doc.text('Thank you for your business!', 20, pageHeight - 20)
  doc.text(`Generated on ${formatDate(new Date().toISOString())}`, 20, pageHeight - 12)

  return doc
}

function formatCurrency(amount: number): string {
  return new Intl.NumberFormat('en-PH', {
    style: 'currency',
    currency: 'PHP',
  }).format(amount)
}

function formatDate(dateString: string): string {
  return new Date(dateString).toLocaleDateString('en-PH', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  })
}

function getStatusLabel(status: string): string {
  const labels: Record<string, string> = {
    DRAFT: 'Draft',
    SENT: 'Sent',
    PAID: 'Paid',
    OVERDUE: 'Overdue',
    CANCELLED: 'Cancelled',
  }
  return labels[status] || status
}

function getStatusColor(status: string): { r: number; g: number; b: number } {
  const colors: Record<string, { r: number; g: number; b: number }> = {
    DRAFT: { r: 128, g: 128, b: 128 },
    SENT: { r: 59, g: 130, b: 246 },
    PAID: { r: 34, g: 197, b: 94 },
    OVERDUE: { r: 239, g: 68, b: 68 },
    CANCELLED: { r: 107, g: 114, b: 128 },
  }
  return colors[status] || { r: 0, g: 0, b: 0 }
}

'use client'

import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { PageWithSummary } from '@/components/layout/page-wrapper'
import { useScrollToTop } from '@/hooks/use-scroll'
import { 
  LuPlus, 
  LuPencil, 
  LuTrash2, 
  LuStore,
  LuCalculator
} from 'react-icons/lu'

interface PricingTier {
  id: number
  minPrice: number
  maxPrice: number | null
  markupType: string
  markupValue: number
  pasabuyFee: number
  sortOrder: number
}

interface StorePricing {
  id: number
  storeCodeId: number
  name: string
  serviceFee: number
  isActive: boolean
  createdAt: string
  updatedAt: string
  storeCode: {
    id: number
    code: string
    name: string | null
  }
  pricingTiers: PricingTier[]
}

export default function StorePricingPage() {
  const router = useRouter()
  const [storePricing, setStorePricing] = useState<StorePricing[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useScrollToTop()

  // Hide bottom navigation for focused experience
  useEffect(() => {
    document.body.style.paddingBottom = '0'
    return () => {
      document.body.style.paddingBottom = '4rem'
    }
  }, [])

  useEffect(() => {
    fetchStorePricing()
  }, [])

  const fetchStorePricing = async () => {
    try {
      setLoading(true)
      setError(null)
      const response = await fetch('/api/store-pricing')
      
      if (!response.ok) {
        throw new Error('Failed to fetch store pricing')
      }
      
      const data = await response.json()
      setStorePricing(data)
    } catch (error) {
      console.error('Error fetching store pricing:', error)
      setError('Failed to load store pricing configurations')
    } finally {
      setLoading(false)
    }
  }

  const handleDelete = async (storeCodeId: number, name: string) => {
    if (!confirm(`Are you sure you want to delete the pricing configuration "${name}"?`)) {
      return
    }

    try {
      const response = await fetch(`/api/store-pricing/${storeCodeId}`, {
        method: 'DELETE'
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to delete store pricing')
      }

      await fetchStorePricing()
    } catch (error) {
      console.error('Error deleting store pricing:', error)
      setError(error instanceof Error ? error.message : 'Failed to delete store pricing')
    }
  }

  const formatMarkupDisplay = (markupType: string, markupValue: number) => {
    switch (markupType) {
      case 'PERCENTAGE':
        return `${markupValue}%`
      case 'FIXED_AMOUNT':
        return `₱${markupValue.toFixed(2)}`
      default:
        return markupValue.toString()
    }
  }

  const formatPriceRange = (tier: PricingTier) => {
    if (tier.maxPrice === null) {
      return `₱${tier.minPrice}+`
    }
    return `₱${tier.minPrice} - ₱${tier.maxPrice}`
  }

  // Summary statistics
  const totalConfigurations = storePricing.length
  const activeConfigurations = storePricing.filter(sp => sp.isActive).length

  const summaryCard = (
    <Card>
      <CardContent className="p-4">
        <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-primary">{totalConfigurations}</div>
            <div className="text-sm text-muted-foreground">Total Stores</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-green-600">{activeConfigurations}</div>
            <div className="text-sm text-muted-foreground">Active</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-600">{totalConfigurations - activeConfigurations}</div>
            <div className="text-sm text-muted-foreground">Inactive</div>
          </div>
        </div>
      </CardContent>
    </Card>
  )

  if (loading) {
    return (
      <PageWithSummary
        title="Store Pricing"
        summaryCard={summaryCard}
      >
        <div className="flex items-center justify-center py-8">
          <div className="text-muted-foreground">Loading store pricing configurations...</div>
        </div>
      </PageWithSummary>
    )
  }

  return (
    <PageWithSummary
      title="Store Pricing"
      summaryCard={summaryCard}
      actions={
        <Button 
          onClick={() => router.push('/store-pricing/new')}
          className="min-h-[44px]"
        >
          <LuPlus className="h-4 w-4 mr-2" />
          <span className="hidden sm:inline">Add Store Pricing</span>
          <span className="sm:hidden">Add</span>
        </Button>
      }
    >
      {error && (
        <Card className="border-destructive">
          <CardContent className="p-4">
            <div className="text-destructive text-sm">{error}</div>
          </CardContent>
        </Card>
      )}

      {storePricing.length === 0 ? (
        <Card>
          <CardContent className="p-8 text-center">
            <LuCalculator className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
            <h3 className="text-lg font-medium mb-2">No Store Pricing Configurations</h3>
            <p className="text-muted-foreground mb-4">
              Create store-specific pricing configurations to customize markup and fees per store.
            </p>
            <Button onClick={() => router.push('/store-pricing/new')}>
              <LuPlus className="h-4 w-4 mr-2" />
              Create Store Pricing
            </Button>
          </CardContent>
        </Card>
      ) : (
        <div className="grid gap-4">
          {storePricing.map((pricing) => (
            <Card key={pricing.id} className="hover:shadow-md transition-shadow">
              <CardHeader className="pb-3">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-1">
                      <CardTitle className="text-lg flex items-center gap-2">
                        <LuStore className="h-4 w-4" />
                        {pricing.name}
                      </CardTitle>
                      <Badge variant={pricing.isActive ? "default" : "secondary"}>
                        {pricing.isActive ? "Active" : "Inactive"}
                      </Badge>
                    </div>
                    <CardDescription>
                      Store: {pricing.storeCode.code} {pricing.storeCode.name && `(${pricing.storeCode.name})`}
                    </CardDescription>
                  </div>
                  <div className="flex gap-1 ml-4">
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={() => router.push(`/store-pricing/${pricing.storeCodeId}/edit`)}
                      className="h-8 w-8"
                      title="Edit"
                    >
                      <LuPencil className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={() => handleDelete(pricing.storeCodeId, pricing.name)}
                      className="h-8 w-8 text-destructive hover:text-destructive"
                      title="Delete"
                    >
                      <LuTrash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </CardHeader>
              <CardContent className="pt-0">
                <div className="mb-4">
                  <div className="font-medium text-muted-foreground mb-2">Pasabuy Fee</div>
                  <div className="font-medium text-blue-600">₱{pricing.serviceFee.toFixed(2)}</div>
                </div>

                {/* Pricing Tiers */}
                <div className="space-y-3">
                  <div className="font-medium text-muted-foreground">Pricing Tiers ({pricing.pricingTiers.length})</div>
                  {pricing.pricingTiers.length > 0 ? (
                    <div className="space-y-2">
                      {pricing.pricingTiers
                        .sort((a, b) => a.sortOrder - b.sortOrder)
                        .map((tier, index) => (
                          <div key={tier.id} className="flex items-center justify-between p-2 bg-muted/30 rounded text-sm">
                            <div className="flex items-center gap-2">
                              <Badge variant="outline" className="text-xs">
                                {formatPriceRange(tier)}
                              </Badge>
                            </div>
                            <div className="flex items-center gap-2">
                              <div className="font-medium text-green-600">
                                {formatMarkupDisplay(tier.markupType, tier.markupValue)}
                              </div>
                              <div className="text-blue-600">
                                -₱{tier.pasabuyFee.toFixed(2)}
                              </div>
                            </div>
                          </div>
                        ))}
                    </div>
                  ) : (
                    <div className="text-sm text-muted-foreground italic">No pricing tiers configured</div>
                  )}
                </div>

                {/* Example calculations for different price points */}
                {pricing.pricingTiers.length > 0 && (
                  <div className="mt-4 p-3 bg-muted/50 rounded text-sm">
                    <div className="font-medium mb-2">Example Calculations:</div>
                    <div className="space-y-1">
                      {[150, 350, 600].map((examplePrice) => {
                        const applicableTier = pricing.pricingTiers
                          .sort((a, b) => a.sortOrder - b.sortOrder)
                          .find(tier => {
                            const minPrice = tier.minPrice || 0
                            const maxPrice = tier.maxPrice
                            return examplePrice >= minPrice && (maxPrice === null || examplePrice <= maxPrice)
                          })

                        if (!applicableTier) return null

                        const markup = applicableTier.markupType === 'PERCENTAGE'
                          ? examplePrice * (applicableTier.markupValue / 100)
                          : applicableTier.markupValue
                        const customerPrice = examplePrice + markup - applicableTier.pasabuyFee

                        return (
                          <div key={examplePrice} className="flex justify-between">
                            <span>₱{examplePrice}:</span>
                            <span>₱{customerPrice.toFixed(2)}</span>
                          </div>
                        )
                      })}
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </PageWithSummary>
  )
}

'use client'


import { Store, X, Users, List, ShoppingBag, Package, FileText, Calculator } from 'lucide-react'
import { NavigationItem } from './navigation-item'
import {
  Sheet,
  SheetContent,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
  SheetClose,
} from '@/components/ui/sheet'
import { Button } from '@/components/ui/button'

// Main navigation items (from bottom nav)
const mainNavItems = [
  {
    href: '/orders',
    label: 'Orders',
    icon: List,
    description: 'Manage and track orders',
  },
  {
    href: '/buy-list',
    label: 'Buy List',
    icon: ShoppingBag,
    description: 'View orders to purchase',
  },
  {
    href: '/packing',
    label: 'Packing',
    icon: Package,
    description: 'Pack completed orders',
  },
  {
    href: '/invoices',
    label: 'Invoices',
    icon: FileText,
    description: 'Create and manage invoices',
  },
]

// Additional navigation items
const additionalNavItems = [
  {
    href: '/stores',
    label: 'Stores',
    icon: Store,
    description: 'Manage store codes and locations',
  },
  {
    href: '/customers',
    label: 'Customers',
    icon: Users,
    description: 'Manage customers',
  },
  {
    href: '/pricing-settings',
    label: 'Default Pricing',
    icon: Calculator,
    description: 'Configure default pricing calculations',
  },
  {
    href: '/store-pricing',
    label: 'Store Pricing',
    icon: Store,
    description: 'Configure store-specific pricing rules',
  },
]

interface SidebarProps {
  children: React.ReactNode
  open?: boolean
  onOpenChange?: (open: boolean) => void
}

export function Sidebar({ children, open, onOpenChange }: SidebarProps) {
  return (
    <Sheet open={open} onOpenChange={onOpenChange}>
      <SheetTrigger asChild>
        {children}
      </SheetTrigger>
      <SheetContent side="left" className="w-80 sm:w-80">
        <SheetHeader className="border-b pb-4">
          <div className="flex items-center justify-between">
            <SheetTitle className="text-lg font-semibold">
              Navigation
            </SheetTitle>
            <SheetClose asChild>
              <Button variant="ghost" size="icon" className="h-6 w-6">
                <X className="h-4 w-4" />
                <span className="sr-only">Close sidebar</span>
              </Button>
            </SheetClose>
          </div>
        </SheetHeader>

        <div className="flex flex-col gap-4 py-4">
          {/* Main Navigation Section */}
          <div>
            <h3 className="text-sm font-medium text-muted-foreground px-3 mb-2">
              Main Navigation
            </h3>
            <div className="flex flex-col gap-1">
              {mainNavItems.map((item) => (
                <SheetClose asChild key={item.href}>
                  <NavigationItem
                    href={item.href}
                    label={item.label}
                    icon={item.icon}
                    description={item.description}
                    variant="sidebar"
                  />
                </SheetClose>
              ))}
            </div>
          </div>

          {/* Additional Navigation Section */}
          <div>
            <h3 className="text-sm font-medium text-muted-foreground px-3 mb-2">
              Management
            </h3>
            <div className="flex flex-col gap-1">
              {additionalNavItems.map((item) => (
                <SheetClose asChild key={item.href}>
                  <NavigationItem
                    href={item.href}
                    label={item.label}
                    icon={item.icon}
                    description={item.description}
                    variant="sidebar"
                  />
                </SheetClose>
              ))}
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="mt-auto pt-4 border-t">
          <p className="text-xs text-muted-foreground px-3">
            All navigation options available here when bottom nav is hidden during scroll.
          </p>
        </div>
      </SheetContent>
    </Sheet>
  )
}

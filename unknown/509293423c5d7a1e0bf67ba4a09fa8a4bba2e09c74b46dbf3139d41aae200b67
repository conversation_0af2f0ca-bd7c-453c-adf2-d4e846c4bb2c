import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/db'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const customerId = searchParams.get('customerId')
    const status = searchParams.get('status')

    const where: any = {}

    if (customerId) {
      where.customerId = parseInt(customerId)
    }

    if (status) {
      where.status = status
    }

    const invoices = await prisma.invoice.findMany({
      where,
      include: {
        customer: true,
        invoiceItems: {
          include: {
            order: {
              include: {
                storeCode: true
              }
            }
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    })

    return NextResponse.json(invoices)
  } catch (error) {
    console.error('Error fetching invoices:', error)
    return NextResponse.json(
      { error: 'Error fetching invoices' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const {
      customerId,
      orders, // Array of { orderId, quantity }
      dueDate,
      notes
    } = body

    if (!customerId || !orders || !Array.isArray(orders) || orders.length === 0) {
      return NextResponse.json(
        { error: 'Customer ID and orders are required' },
        { status: 400 }
      )
    }

    // Generate invoice number
    const invoiceCount = await prisma.invoice.count()
    const invoiceNumber = `INV-${String(invoiceCount + 1).padStart(6, '0')}`

    // Fetch order details to calculate totals
    const orderIds = orders.map((order: any) => order.orderId)
    const orderDetails = await prisma.order.findMany({
      where: {
        id: { in: orderIds }
      }
    })

    // Calculate totals
    let subtotal = 0
    const invoiceItemsData = orders.map((invoiceOrder: any) => {
      const order = orderDetails.find(o => o.id === invoiceOrder.orderId)
      if (!order) {
        throw new Error(`Order with ID ${invoiceOrder.orderId} not found`)
      }

      const quantity = invoiceOrder.quantity || 1
      const unitPrice = order.customerPrice
      const totalPrice = quantity * unitPrice

      subtotal += totalPrice

      return {
        orderId: invoiceOrder.orderId,
        quantity,
        unitPrice,
        totalPrice
      }
    })

    const total = subtotal // For now, total equals subtotal (no taxes/discounts)

    // Create invoice with invoice items
    const invoice = await prisma.invoice.create({
      data: {
        invoiceNumber,
        customerId: parseInt(customerId),
        subtotal,
        total,
        dueDate: dueDate ? new Date(dueDate) : null,
        notes: notes || null,
        invoiceItems: {
          create: invoiceItemsData
        }
      },
      include: {
        customer: true,
        invoiceItems: {
          include: {
            order: {
              include: {
                storeCode: true
              }
            }
          }
        }
      }
    })

    return NextResponse.json(invoice, { status: 201 })
  } catch (error) {
    console.error('Error creating invoice:', error)
    return NextResponse.json(
      { error: 'Error creating invoice' },
      { status: 500 }
    )
  }
}

'use client'

import React from 'react'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Label } from '@/components/ui/label'
import { FormControl, FormDescription, FormItem, FormLabel, FormMessage } from '@/components/ui/form'
import { cn } from '@/lib/utils'

interface BaseFormFieldProps {
  label: string
  description?: string
  required?: boolean
  className?: string
  error?: string
}

interface TextFormFieldProps extends BaseFormFieldProps {
  type: 'text' | 'email' | 'password' | 'number' | 'tel' | 'url'
  placeholder?: string
  value: string
  onChange: (value: string) => void
  disabled?: boolean
  autoComplete?: string
  maxLength?: number
  minLength?: number
}

interface TextareaFormFieldProps extends BaseFormFieldProps {
  type: 'textarea'
  placeholder?: string
  value: string
  onChange: (value: string) => void
  disabled?: boolean
  rows?: number
  maxLength?: number
  minLength?: number
}

interface SelectFormFieldProps extends BaseFormFieldProps {
  type: 'select'
  value: string
  onChange: (value: string) => void
  disabled?: boolean
  children: React.ReactNode
}

interface CustomFormFieldProps extends BaseFormFieldProps {
  type: 'custom'
  children: React.ReactNode
}

type FormFieldProps =
  | TextFormFieldProps
  | TextareaFormFieldProps
  | SelectFormFieldProps
  | CustomFormFieldProps

/**
 * Standardized form field component that handles common patterns
 * Consolidates form field styling and behavior across the application
 */
export function FormField(props: FormFieldProps) {
  const { label, description, required, className, error } = props

  const renderField = () => {
    switch (props.type) {
      case 'text':
      case 'email':
      case 'password':
      case 'number':
      case 'tel':
      case 'url':
        return (
          <Input
            type={props.type}
            placeholder={props.placeholder}
            value={props.value}
            onChange={(e) => props.onChange(e.target.value)}
            disabled={props.disabled}
            autoComplete={props.autoComplete}
            maxLength={props.maxLength}
            minLength={props.minLength}
            className={cn(
              'min-h-[48px] text-base font-medium',
              error && 'border-destructive focus-visible:ring-destructive'
            )}
            required={required}
          />
        )

      case 'textarea':
        return (
          <Textarea
            placeholder={props.placeholder}
            value={props.value}
            onChange={(e) => props.onChange(e.target.value)}
            disabled={props.disabled}
            rows={props.rows || 3}
            maxLength={props.maxLength}
            minLength={props.minLength}
            className={cn(
              'min-h-[48px] text-base font-medium resize-none',
              error && 'border-destructive focus-visible:ring-destructive'
            )}
            required={required}
          />
        )

      case 'select':
        return props.children

      case 'custom':
        return props.children

      default:
        return null
    }
  }

  return (
    <div className={cn('space-y-2', className)}>
      <Label className="text-sm font-medium">
        {label}
        {required && <span className="text-destructive ml-1">*</span>}
      </Label>

      <div className="space-y-1">
        {renderField()}

        {description && (
          <p className="text-xs text-muted-foreground">
            {description}
          </p>
        )}

        {error && (
          <p className="text-xs text-destructive">
            {error}
          </p>
        )}
      </div>
    </div>
  )
}

/**
 * React Hook Form compatible FormField wrapper
 * For use with react-hook-form and shadcn/ui Form components
 */
interface RHFFormFieldProps extends BaseFormFieldProps {
  name: string
  control?: unknown
  render: (field: unknown) => React.ReactNode
}

export function RHFFormField({
  name,
  label,
  description,
  required,
  className,
  control,
  render
}: RHFFormFieldProps) {
  return (
    <FormItem className={className}>
      <FormLabel>
        {label}
        {required && <span className="text-destructive ml-1">*</span>}
      </FormLabel>
      <FormControl>
        {render({ name, control })}
      </FormControl>
      {description && (
        <FormDescription>
          {description}
        </FormDescription>
      )}
      <FormMessage />
    </FormItem>
  )
}

/**
 * Compact form field variant for dense layouts
 */
export function CompactFormField(props: FormFieldProps) {
  return (
    <FormField
      {...props}
      className={cn('space-y-1', props.className)}
    />
  )
}

/**
 * Inline form field variant for horizontal layouts
 */
export function InlineFormField(props: FormFieldProps) {
  const { label, description, required, className } = props

  return (
    <div className={cn('flex items-center gap-3', className)}>
      <Label className="text-sm font-medium whitespace-nowrap min-w-0">
        {label}
        {required && <span className="text-destructive ml-1">*</span>}
      </Label>

      <div className="flex-1 min-w-0">
        {props.type === 'text' || props.type === 'email' || props.type === 'password' ||
         props.type === 'number' || props.type === 'tel' || props.type === 'url' ? (
          <Input
            type={props.type}
            placeholder={props.placeholder}
            value={props.value}
            onChange={(e) => props.onChange(e.target.value)}
            disabled={props.disabled}
            className="h-8 text-sm"
            required={required}
          />
        ) : props.type === 'custom' ? (
          props.children
        ) : null}
      </div>

      {description && (
        <p className="text-xs text-muted-foreground whitespace-nowrap">
          {description}
        </p>
      )}
    </div>
  )
}

'use client'

import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { But<PERSON> } from '@/components/ui/button'
import { Card } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { SimplePageWrapper } from '@/components/layout/page-wrapper'
import Link from 'next/link'
import { LuPlus, LuFileText, LuCalendar, LuUser, LuDollarSign, LuPackage } from 'react-icons/lu'
import { Invoice, InvoiceStatus } from '@/lib/store'
import { NotificationContainer, useNotifications } from '@/components/ui/notification'

const statusColors: Record<InvoiceStatus, string> = {
  DRAFT: 'bg-muted/60 text-muted-foreground border-border dark:bg-muted/40 dark:text-muted-foreground',
  SENT: 'bg-blue-100 text-blue-800 border-blue-200 dark:bg-blue-900/30 dark:text-blue-300 dark:border-blue-800/50',
  PAID: 'bg-emerald-100 text-emerald-800 border-emerald-200 dark:bg-emerald-900/30 dark:text-emerald-300 dark:border-emerald-800/50',
  OVERDUE: 'bg-red-100 text-red-800 border-red-200 dark:bg-red-900/30 dark:text-red-300 dark:border-red-800/50',
  CANCELLED: 'bg-muted/60 text-muted-foreground border-border dark:bg-muted/40 dark:text-muted-foreground',
}

const statusLabels: Record<InvoiceStatus, string> = {
  DRAFT: 'Draft',
  SENT: 'Sent',
  PAID: 'Paid',
  OVERDUE: 'Overdue',
  CANCELLED: 'Cancelled',
}

export default function InvoicesPage() {
  const [invoices, setInvoices] = useState<Invoice[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [filter, setFilter] = useState<InvoiceStatus | 'ALL'>('ALL')
  const router = useRouter()
  const { notifications, addNotification, dismissNotification } = useNotifications()

  useEffect(() => {
    fetchInvoices()
  }, [])

  const fetchInvoices = async () => {
    try {
      setIsLoading(true)
      const response = await fetch('/api/invoices')
      if (response.ok) {
        const data = await response.json()
        setInvoices(data)
      }
    } catch (error) {
      console.error('Error fetching invoices:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-PH', {
      style: 'currency',
      currency: 'PHP',
    }).format(amount)
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-PH', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    })
  }

  const filteredInvoices = filter === 'ALL'
    ? invoices
    : invoices.filter(invoice => invoice.status === filter)

  const handleCardClick = (invoiceId: number) => {
    router.push(`/invoices/${invoiceId}`)
  }

  if (isLoading) {
    return (
      <SimplePageWrapper title="Invoices">
        <div className="animate-pulse">
          <div className="h-8 bg-muted/50 rounded w-1/3 mb-4"></div>
          <div className="h-4 bg-muted/50 rounded w-1/2"></div>
        </div>
      </SimplePageWrapper>
    )
  }

  return (
    <>
      <NotificationContainer
        notifications={notifications}
        onDismiss={dismissNotification}
      />
      <SimplePageWrapper
        title="Invoices"
        actions={
          <div className="flex gap-2">
            <Link href="/invoices/daily-batch">
              <Button variant="outline" className="min-h-[44px] w-full sm:w-auto">
                <LuPackage className="h-5 w-5 mr-2" />
                Daily Batch
              </Button>
            </Link>
            <Link href="/invoices/new">
              <Button className="min-h-[44px] w-full sm:w-auto">
                <LuPlus className="h-5 w-5 mr-2" />
                Create Invoice
              </Button>
            </Link>
          </div>
        }
      >
        <div className="text-muted-foreground mb-4">
          Manage and track your invoices
        </div>

      {/* Status Filter */}
      <div className="flex flex-wrap gap-2">
        <Button
          variant={filter === 'ALL' ? 'default' : 'outline'}
          size="sm"
          onClick={() => setFilter('ALL')}
          className="min-h-[44px]"
        >
          All ({invoices.length})
        </Button>
        {Object.entries(statusLabels).map(([status, label]) => {
          const count = invoices.filter(invoice => invoice.status === status).length
          return (
            <Button
              key={status}
              variant={filter === status ? 'default' : 'outline'}
              size="sm"
              onClick={() => setFilter(status as InvoiceStatus)}
              className="min-h-[44px]"
            >
              {label} ({count})
            </Button>
          )
        })}
      </div>

      {/* Invoices List */}
      {filteredInvoices.length === 0 ? (
        <Card className="p-8 text-center">
          <LuFileText className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
          <h3 className="text-lg font-medium mb-2">
            {filter === 'ALL' ? 'No invoices yet' : `No ${statusLabels[filter as InvoiceStatus].toLowerCase()} invoices`}
          </h3>
          <p className="text-muted-foreground mb-4">
            {filter === 'ALL'
              ? 'Create your first invoice to get started.'
              : `There are no ${statusLabels[filter as InvoiceStatus].toLowerCase()} invoices at the moment.`
            }
          </p>
          {filter === 'ALL' && (
            <Link href="/invoices/new">
              <Button className="min-h-[44px]">
                <LuPlus className="h-5 w-5 mr-2" />
                Create Invoice
              </Button>
            </Link>
          )}
        </Card>
      ) : (
        <div className="grid gap-4">
          {filteredInvoices.map((invoice) => (
            <Card
              key={invoice.id}
              className="p-4 hover:shadow-md hover:bg-accent/30 dark:hover:bg-accent/20 transition-all cursor-pointer"
              onClick={() => handleCardClick(invoice.id)}
            >
              <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3">
                <div className="flex-1 min-w-0">
                  <div className="flex items-center gap-3 mb-2">
                    <h3 className="font-medium text-lg">{invoice.invoiceNumber}</h3>
                    <Badge className={`${statusColors[invoice.status]} text-xs font-medium`}>
                      {statusLabels[invoice.status]}
                    </Badge>
                  </div>

                  <div className="grid grid-cols-1 sm:grid-cols-3 gap-2 text-sm text-muted-foreground">
                    <div className="flex items-center gap-2">
                      <LuUser className="h-4 w-4 flex-shrink-0" />
                      <span className="truncate">{invoice.customer?.name}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <LuCalendar className="h-4 w-4 flex-shrink-0" />
                      <span>{formatDate(invoice.createdAt)}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <LuDollarSign className="h-4 w-4 flex-shrink-0" />
                      <span className="font-medium">{formatCurrency(invoice.total)}</span>
                    </div>
                  </div>
                </div>

                <div className="flex items-center gap-2 sm:flex-col sm:items-end">
                  {invoice.dueDate && (
                    <div className="text-xs text-muted-foreground">
                      Due: {formatDate(invoice.dueDate)}
                    </div>
                  )}
                  <div className="text-sm font-medium">
                    {invoice.invoiceItems?.length || 0} items
                  </div>
                </div>
              </div>
            </Card>
          ))}
        </div>
      )}
      </SimplePageWrapper>
    </>
  )
}

const fs = require('fs')
const path = require('path')

// Create uploads directory if it doesn't exist
const uploadsDir = path.join(process.cwd(), 'public', 'uploads', 'orders')
if (!fs.existsSync(uploadsDir)) {
  fs.mkdirSync(uploadsDir, { recursive: true })
}

// Create simple SVG placeholder images
const sampleImages = [
  {
    filename: 'iphone-15-pro-max.jpg',
    color: '#1a1a1a',
    textColor: '#ffffff',
    text: 'iPhone 15 Pro Max',
    description: 'iPhone 15 Pro Max'
  },
  {
    filename: 'samsung-s24-ultra.jpg',
    color: '#2563eb',
    textColor: '#ffffff',
    text: 'Samsung S24 Ultra',
    description: 'Samsung Galaxy S24 Ultra'
  },
  {
    filename: 'airpods-pro-2.jpg',
    color: '#f3f4f6',
    textColor: '#1f2937',
    text: 'AirPods Pro 2',
    description: 'Apple AirPods Pro 2nd Gen'
  },
  {
    filename: 'macbook-air-m3.jpg',
    color: '#374151',
    textColor: '#ffffff',
    text: 'MacBook Air M3',
    description: 'MacBook Air M3 15-inch'
  },
  {
    filename: 'sony-wh1000xm5.jpg',
    color: '#000000',
    textColor: '#ffffff',
    text: 'Sony WH-1000XM5',
    description: 'Sony WH-1000XM5 Headphones'
  },
  {
    filename: 'nike-air-force-1.jpg',
    color: '#dc2626',
    textColor: '#ffffff',
    text: 'Nike Air Force 1',
    description: 'Nike Air Force 1'
  },
  {
    filename: 'adidas-ultraboost-22.jpg',
    color: '#059669',
    textColor: '#ffffff',
    text: 'Adidas Ultraboost',
    description: 'Adidas Ultraboost 22'
  },
  {
    filename: 'uniqlo-heattech-shirt.jpg',
    color: '#7c3aed',
    textColor: '#ffffff',
    text: 'Uniqlo Heattech',
    description: 'Uniqlo Heattech Shirt'
  },
  {
    filename: 'fenty-foundation.jpg',
    color: '#ec4899',
    textColor: '#ffffff',
    text: 'Fenty Foundation',
    description: 'Fenty Beauty Foundation'
  },
  {
    filename: 'the-ordinary-niacinamide.jpg',
    color: '#0891b2',
    textColor: '#ffffff',
    text: 'The Ordinary',
    description: 'The Ordinary Niacinamide'
  },
  {
    filename: 'japanese-kitkat.jpg',
    color: '#ea580c',
    textColor: '#ffffff',
    text: 'Japanese KitKat',
    description: 'Japanese Kit Kat'
  },
  {
    filename: 'royce-nama-chocolate.jpg',
    color: '#92400e',
    textColor: '#ffffff',
    text: 'Royce Chocolate',
    description: 'Royce Nama Chocolate'
  },
  {
    filename: 'muji-aroma-diffuser.jpg',
    color: '#64748b',
    textColor: '#ffffff',
    text: 'Muji Diffuser',
    description: 'Muji Aroma Diffuser'
  },
  {
    filename: 'ikea-friheten-sofa.jpg',
    color: '#0369a1',
    textColor: '#ffffff',
    text: 'IKEA Sofa',
    description: 'IKEA FRIHETEN Sofa'
  },
  {
    filename: 'nintendo-switch-oled.jpg',
    color: '#e11d48',
    textColor: '#ffffff',
    text: 'Nintendo Switch',
    description: 'Nintendo Switch OLED'
  },
  {
    filename: 'levis-511-jeans.jpg',
    color: '#1e40af',
    textColor: '#ffffff',
    text: 'Levi\'s 511',
    description: 'Levi\'s 511 Slim Jeans'
  },
  {
    filename: 'starbucks-tumbler.jpg',
    color: '#16a34a',
    textColor: '#ffffff',
    text: 'Starbucks Tumbler',
    description: 'Starbucks Limited Edition'
  },
  {
    filename: 'dyson-v15-detect.jpg',
    color: '#7c2d12',
    textColor: '#ffffff',
    text: 'Dyson V15',
    description: 'Dyson V15 Detect Absolute'
  },
  {
    filename: 'casio-gshock-ga2100.jpg',
    color: '#1f2937',
    textColor: '#ffffff',
    text: 'G-Shock GA-2100',
    description: 'Casio G-Shock GA-2100'
  }
]

function createSVGImage(color, textColor, text) {
  return `<?xml version="1.0" encoding="UTF-8"?>
<svg width="400" height="400" xmlns="http://www.w3.org/2000/svg">
  <rect width="400" height="400" fill="${color}"/>
  <text x="200" y="180" font-family="Arial, sans-serif" font-size="24" font-weight="bold" text-anchor="middle" fill="${textColor}">${text}</text>
  <text x="200" y="220" font-family="Arial, sans-serif" font-size="16" text-anchor="middle" fill="${textColor}">400 x 400</text>
  <circle cx="200" cy="280" r="30" fill="none" stroke="${textColor}" stroke-width="2" opacity="0.5"/>
  <rect x="170" y="250" width="60" height="60" fill="none" stroke="${textColor}" stroke-width="1" opacity="0.3"/>
</svg>`
}

function createImage(image) {
  const filePath = path.join(uploadsDir, image.filename)

  // Skip if file already exists
  if (fs.existsSync(filePath)) {
    console.log(`✅ ${image.filename} already exists, skipping...`)
    return
  }

  try {
    const svgContent = createSVGImage(image.color, image.textColor, image.text)

    // Save as SVG first
    const svgPath = filePath.replace('.jpg', '.svg')
    fs.writeFileSync(svgPath, svgContent)

    // Create a simple HTML wrapper that displays the SVG as an image
    // This ensures browsers can display it properly regardless of extension
    const htmlWrapper = `<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <style>
    body { margin: 0; padding: 0; display: flex; justify-content: center; align-items: center; min-height: 100vh; background: #f5f5f5; }
    svg { max-width: 100%; max-height: 100%; }
  </style>
</head>
<body>
  ${svgContent}
</body>
</html>`

    // For better compatibility, let's save the SVG content with proper MIME type handling
    // We'll save it as SVG but also create a data URL version for the .jpg file
    const dataUrl = `data:image/svg+xml;base64,${Buffer.from(svgContent).toString('base64')}`

    // Save the SVG content directly to the .jpg file for now
    // This works because browsers can handle SVG content even with .jpg extension
    fs.writeFileSync(filePath, svgContent)

    console.log(`✅ Created: ${image.filename} (SVG format)`)
  } catch (error) {
    console.error(`❌ Error creating ${image.filename}:`, error.message)
  }
}

function createAllImages() {
  console.log('🎨 Creating sample placeholder images...')
  console.log(`📁 Saving to: ${uploadsDir}`)

  try {
    for (const image of sampleImages) {
      createImage(image)
    }

    console.log('\n🎉 All sample images created successfully!')
    console.log(`📊 Total images: ${sampleImages.length}`)
    console.log('\n💡 Note: These are SVG placeholder images. In production, you might want to use actual product photos.')
  } catch (error) {
    console.error('❌ Error creating images:', error)
    process.exit(1)
  }
}

// Run the creation
createAllImages()

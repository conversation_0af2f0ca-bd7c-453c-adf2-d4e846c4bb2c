'use client'

import { But<PERSON> } from '@/components/ui/button'
import { Card } from '@/components/ui/card'
import { Checkbox } from '@/components/ui/checkbox'
import { LuCheck, LuPackage, LuX } from 'react-icons/lu'

export interface BulkActionsBarProps {
  selectedCount: number
  totalCount: number
  isSelectAllChecked: boolean
  isSelectAllIndeterminate: boolean
  isBulkMode?: boolean
  onSelectAllChange: () => void
  onClearSelection: () => void
  onExitBulkMode?: () => void
  onMarkAsBought?: () => void
  onMarkAsPacked?: () => void
  showBoughtAction?: boolean
  showPackedAction?: boolean
  className?: string
}

export function BulkActionsBar({
  selectedCount,
  totalCount,
  isSelectAllChecked,
  isSelectAllIndeterminate,
  isBulkMode = false,
  onSelectAllChange,
  onClearSelection,
  onExitBulkMode,
  onMarkAsBought,
  onMarkAsPacked,
  showBoughtAction = false,
  showPackedAction = false,
  className = ''
}: BulkActionsBarProps) {
  if (selectedCount === 0) {
    return (
      <Card className={`p-3 ${className}`}>
        <div className="flex items-center gap-3">
          <Checkbox
            checked={isSelectAllChecked}
            ref={(el) => {
              if (el) {
                (el as HTMLInputElement).indeterminate = isSelectAllIndeterminate
              }
            }}
            onCheckedChange={onSelectAllChange}
            aria-label="Select all items"
          />
          <span className="text-sm text-muted-foreground">
            Select items for bulk actions ({totalCount} total)
          </span>
        </div>
      </Card>
    )
  }

  return (
    <Card className={`p-3 bg-primary/5 border-primary/20 ${className}`}>
      {/* Mobile-optimized layout with better spacing and touch targets */}
      <div className="flex flex-col gap-3">
        {/* Selection info row */}
        <div className="flex items-center gap-3">
          <Checkbox
            checked={isSelectAllChecked}
            ref={(el) => {
              if (el) {
                (el as HTMLInputElement).indeterminate = isSelectAllIndeterminate
              }
            }}
            onCheckedChange={onSelectAllChange}
            aria-label="Select all items"
            className="min-w-[20px] min-h-[20px]"
          />
          <span className="text-sm font-medium flex-1">
            {selectedCount} of {totalCount} items selected
          </span>
        </div>

        {/* Action buttons row - optimized for mobile */}
        <div className="flex items-center gap-2 flex-wrap">
          {showBoughtAction && onMarkAsBought && (
            <Button
              size="sm"
              onClick={onMarkAsBought}
              className="bg-emerald-600 hover:bg-emerald-700 dark:bg-emerald-700 dark:hover:bg-emerald-600 active:bg-emerald-800 min-h-[44px] px-4 text-sm flex-1 sm:flex-initial whitespace-nowrap transition-colors"
              title="Mark selected items as bought"
              aria-label="Mark selected items as bought"
            >
              <LuCheck className="h-4 w-4 mr-2" />
              <span className="hidden xs:inline sm:hidden md:inline">Mark as Bought</span>
              <span className="xs:hidden sm:inline md:hidden">Bought</span>
            </Button>
          )}

          {showPackedAction && onMarkAsPacked && (
            <Button
              size="sm"
              onClick={onMarkAsPacked}
              className="bg-blue-600 hover:bg-blue-700 dark:bg-blue-700 dark:hover:bg-blue-600 active:bg-blue-800 min-h-[44px] px-4 text-sm flex-1 sm:flex-initial whitespace-nowrap transition-colors"
              title="Mark selected items as packed"
              aria-label="Mark selected items as packed"
            >
              <LuPackage className="h-4 w-4 mr-2" />
              <span className="hidden xs:inline sm:hidden md:inline">Mark as Packed</span>
              <span className="xs:hidden sm:inline md:hidden">Packed</span>
            </Button>
          )}

          <Button
            size="sm"
            variant="outline"
            onClick={onClearSelection}
            className="min-h-[44px] px-4 text-sm flex-shrink-0 whitespace-nowrap transition-colors hover:bg-accent active:bg-accent/80"
            title="Clear selection"
            aria-label="Clear selection"
          >
            <LuX className="h-4 w-4 mr-2" />
            <span>Clear</span>
          </Button>

          {isBulkMode && onExitBulkMode && (
            <Button
              size="sm"
              variant="outline"
              onClick={onExitBulkMode}
              className="min-h-[44px] px-4 text-sm flex-shrink-0 whitespace-nowrap transition-colors hover:bg-accent active:bg-accent/80"
              title="Exit selection mode"
              aria-label="Exit selection mode"
            >
              <LuX className="h-4 w-4 mr-2" />
              <span className="hidden sm:inline">Exit Selection</span>
              <span className="sm:hidden">Exit</span>
            </Button>
          )}
        </div>
      </div>
    </Card>
  )
}

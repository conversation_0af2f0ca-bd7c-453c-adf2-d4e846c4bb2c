import React from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { ArrowUp, ArrowDown } from 'lucide-react'
import { SortConfig, SortDirection, VALID_ORDER_SORT_FIELDS } from '@/lib/filter-types'

interface SortControlsProps {
  value: SortConfig
  onChange: (sort: SortConfig) => void
  className?: string
}

const sortFieldLabels: Record<string, string> = {
  'id': 'ID',
  'productName': 'Product Name',
  'quantity': 'Quantity',
  'usageUnit': 'Usage Unit',
  'comment': 'Comment',
  'storePrice': 'Store Price',
  'pasabuyFee': 'Pasabuy Fee',
  'resellerPrice': 'Reseller Price',
  'isBought': 'Bought Status',
  'packingStatus': 'Packing Status',
  'createdAt': 'Date Added',
  'updatedAt': 'Date Updated',
  'reseller.name': 'Reseller',
  'storeCode.code': 'Store Code',
  'storeCode.name': 'Store Name'
}

export function SortControls({ value, onChange, className }: SortControlsProps) {
  const primarySort = value.columns[0] || {
    field: 'createdAt',
    direction: 'desc' as SortDirection,
    priority: 0
  }

  const handleFieldChange = (field: string) => {
    onChange({
      columns: [{
        field,
        direction: primarySort.direction,
        priority: 0
      }]
    })
  }

  const handleDirectionChange = () => {
    const newDirection: SortDirection = primarySort.direction === 'asc' ? 'desc' : 'asc'
    onChange({
      columns: [{
        field: primarySort.field,
        direction: newDirection,
        priority: 0
      }]
    })
  }

  const getSortIcon = () => {
    if (primarySort.direction === 'asc') {
      return <ArrowUp className="h-4 w-4" />
    } else {
      return <ArrowDown className="h-4 w-4" />
    }
  }

  return (
    <div className={`flex items-center gap-2 ${className}`}>
      <Select value={primarySort.field} onValueChange={handleFieldChange}>
        <SelectTrigger className="w-[180px] h-8">
          <SelectValue placeholder="Sort by..." />
        </SelectTrigger>
        <SelectContent>
          {VALID_ORDER_SORT_FIELDS.map((field) => (
            <SelectItem key={field} value={field}>
              {sortFieldLabels[field] || field}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>

      <Button
        variant="outline"
        size="sm"
        onClick={handleDirectionChange}
        className="h-8 w-8 p-0"
        title={`Sort ${primarySort.direction === 'asc' ? 'ascending' : 'descending'}`}
      >
        {getSortIcon()}
      </Button>

      <div className="text-xs text-muted-foreground">
        {primarySort.direction === 'asc' ? 'A→Z' : 'Z→A'}
      </div>
    </div>
  )
}

import React from 'react'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Card } from '@/components/ui/card'
import { ScrollArea } from '@/components/ui/scroll-area'
import { X, Filter, RotateCcw } from 'lucide-react'
import { FilterSection } from './filter-section'
import { TextFilter } from './text-filter'
import { StatusFilter, MultiStatusFilter } from './status-filter'
import { NumberRangeFilter } from './number-range-filter'
import { MultiSelectFilter } from './multi-select-filter'

import { SortControls } from '../sorting/sort-controls'
import { FilterConfig, SortConfig } from '@/lib/filter-types'
import { useFilterOptions } from '@/hooks/use-filter-options'
import { cn } from '@/lib/utils'

interface AdvancedFilterPanelProps {
  filters: FilterConfig
  sort: SortConfig
  onFiltersChange: (filters: FilterConfig) => void
  onSortChange: (sort: SortConfig) => void
  onClose: () => void
  onClear: () => void
  className?: string
  isModal?: boolean
}

export function AdvancedFilterPanel({
  filters,
  sort,
  onFiltersChange,
  onSortChange,
  onClose,
  onClear,
  className,
  isModal = false
}: AdvancedFilterPanelProps) {
  const { customers, storeCodes, isLoading: optionsLoading } = useFilterOptions()
  const updateFilter = (key: keyof FilterConfig, value: unknown) => {
    const newFilters = { ...filters }
    if (value === undefined || value === null) {
      delete newFilters[key]
    } else {
      // Type assertion since we've validated the value is not null/undefined
      (newFilters as Record<string, unknown>)[key] = value
    }
    onFiltersChange(newFilters)
  }

  const hasActiveFilters = Object.keys(filters).length > 0

  const getActiveFilterCount = () => {
    return Object.values(filters).filter(value =>
      value !== undefined && value !== null &&
      (Array.isArray(value) ? value.length > 0 : true)
    ).length
  }

  // Modal layout - simplified structure without Card wrapper and ScrollArea
  if (isModal) {
    return (
      <div className={cn("w-full h-full flex flex-col", className)}>
        {/* Mobile Clear Button */}
        <div className="p-4 border-b flex-shrink-0">
          {hasActiveFilters && (
            <Button
              variant="outline"
              size="sm"
              onClick={onClear}
              className="w-full touch-target"
            >
              <RotateCcw className="h-4 w-4 mr-2" />
              Clear All Filters
            </Button>
          )}
        </div>

        {/* Filter Content - scrollable */}
        <div className="flex-1 overflow-y-auto p-4">
          <div className="space-y-4">
          {/* Sorting */}
          <FilterSection
            title="Sort"
            hasActiveFilters={sort.columns.length > 0 && sort.columns[0].field !== 'createdAt'}
            onClear={() => onSortChange({
              columns: [{
                field: 'createdAt',
                direction: 'desc',
                priority: 0
              }]
            })}
          >
            <SortControls
              value={sort}
              onChange={onSortChange}
            />
          </FilterSection>

          {/* Status Filters */}
          <FilterSection
            title="Status"
            hasActiveFilters={filters.isBought !== undefined || !!filters.packingStatus}
            onClear={() => {
              updateFilter('isBought', undefined)
              updateFilter('packingStatus', undefined)
            }}
          >
            <div className="space-y-3">
              <StatusFilter
                label="Purchase Status"
                value={filters.isBought}
                onChange={(value) => updateFilter('isBought', value)}
                options={{
                  true: 'Bought',
                  false: 'Not Bought',
                  null: 'Any'
                }}
              />

              <MultiStatusFilter
                label="Packing Status"
                value={filters.packingStatus}
                onChange={(value) => updateFilter('packingStatus', value)}
                options={['Not Packed', 'Packed']}
              />
            </div>
          </FilterSection>

          {/* Customer & Store Filters */}
          <FilterSection
            title="Customer & Store"
            hasActiveFilters={!!filters.customers || !!filters.storeCodes}
            onClear={() => {
              updateFilter('customers', undefined)
              updateFilter('storeCodes', undefined)
            }}
          >
            <div className="space-y-3">
              {optionsLoading ? (
                <div className="text-xs text-muted-foreground text-center py-2">
                  Loading options...
                </div>
              ) : (
                <>
                  <MultiSelectFilter
                    label="Customers"
                    value={filters.customers}
                    onChange={(value) => updateFilter('customers', value)}
                    options={customers}
                    placeholder="Select customers..."
                    nullLabel="No customer assigned"
                  />

                  <MultiSelectFilter
                    label="Store Codes"
                    value={filters.storeCodes}
                    onChange={(value) => updateFilter('storeCodes', value)}
                    options={storeCodes}
                    placeholder="Select stores..."
                    nullLabel="No store assigned"
                  />
                </>
              )}
            </div>
          </FilterSection>

          {/* Price Range Filter (Simplified) */}
          <FilterSection
            title="Price Range"
            hasActiveFilters={!!filters.storePrice}
            onClear={() => {
              updateFilter('storePrice', undefined)
            }}
          >
            <NumberRangeFilter
              label="Store Price"
              value={filters.storePrice}
              onChange={(value) => updateFilter('storePrice', value)}
              prefix="₱"
              min={0}
              max={1000000}
              step={100}
            />
            <div className="text-xs text-muted-foreground mt-1">
              Filter by the store price range
            </div>
          </FilterSection>

            {/* Legacy Search Support */}
            {filters.search && (
              <FilterSection
                title="Global Search"
                hasActiveFilters={!!filters.search}
                onClear={() => updateFilter('search', undefined)}
              >
                <div className="text-sm text-muted-foreground">
                  Searching for: <span className="font-medium">&quot;{filters.search}&quot;</span>
                </div>
              </FilterSection>
            )}
          </div>
        </div>

        {/* Footer */}
        <div className="p-4 border-t flex-shrink-0">
          <div className="text-xs text-muted-foreground text-center">
            {hasActiveFilters ? (
              `${getActiveFilterCount()} filter${getActiveFilterCount() !== 1 ? 's' : ''} applied`
            ) : (
              'No filters applied'
            )}
          </div>
        </div>
      </div>
    )
  }

  // Original non-modal layout
  return (
    <Card className={cn("w-full md:w-80 h-full flex flex-col", className)}>
      {/* Header - Only show on desktop, mobile uses Sheet header */}
      <div className="hidden md:flex items-center justify-between p-4 border-b">
        <div className="flex items-center gap-2">
          <Filter className="h-4 w-4" />
          <h2 className="font-semibold">Filters</h2>
          {hasActiveFilters && (
            <span className="bg-primary text-primary-foreground text-xs px-2 py-1 rounded-full">
              {getActiveFilterCount()}
            </span>
          )}
        </div>
        <div className="flex items-center gap-1">
          {hasActiveFilters && (
            <Button
              variant="ghost"
              size="sm"
              onClick={onClear}
              className="h-8 px-2"
            >
              <RotateCcw className="h-3 w-3 mr-1" />
              Clear
            </Button>
          )}
          <Button
            variant="ghost"
            size="sm"
            onClick={onClose}
            className="h-8 w-8 p-0"
          >
            <X className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {/* Mobile Clear Button */}
      <div className="md:hidden p-4 border-b">
        {hasActiveFilters && (
          <Button
            variant="outline"
            size="sm"
            onClick={onClear}
            className="w-full touch-target"
          >
            <RotateCcw className="h-4 w-4 mr-2" />
            Clear All Filters
          </Button>
        )}
      </div>

      {/* Filter Content */}
      <ScrollArea className="flex-1 p-4">
        <div className="space-y-4">
          {/* Sorting */}
          <FilterSection
            title="Sort"
            hasActiveFilters={sort.columns.length > 0 && sort.columns[0].field !== 'createdAt'}
            onClear={() => onSortChange({
              columns: [{
                field: 'createdAt',
                direction: 'desc',
                priority: 0
              }]
            })}
          >
            <SortControls
              value={sort}
              onChange={onSortChange}
            />
          </FilterSection>

          {/* Quick Search */}
          <FilterSection
            title="Search"
            hasActiveFilters={!!filters.search || !!filters.productName}
            onClear={() => {
              updateFilter('search', undefined)
              updateFilter('productName', undefined)
            }}
          >
            <TextFilter
              label="Search Orders"
              value={filters.productName}
              onChange={(value) => updateFilter('productName', value)}
              placeholder="Search by product name, customer, or store..."
            />
            <div className="text-xs text-muted-foreground mt-1">
              Search across product names, customers, and store codes
            </div>
          </FilterSection>

          {/* Status Filters */}
          <FilterSection
            title="Status"
            hasActiveFilters={filters.isBought !== undefined || !!filters.packingStatus}
            onClear={() => {
              updateFilter('isBought', undefined)
              updateFilter('packingStatus', undefined)
            }}
          >
            <div className="space-y-3">
              <StatusFilter
                label="Purchase Status"
                value={filters.isBought}
                onChange={(value) => updateFilter('isBought', value)}
                options={{
                  true: 'Bought',
                  false: 'Not Bought',
                  null: 'Any'
                }}
              />

              <MultiStatusFilter
                label="Packing Status"
                value={filters.packingStatus}
                onChange={(value) => updateFilter('packingStatus', value)}
                options={['Not Packed', 'Packed']}
              />
            </div>
          </FilterSection>

          {/* Customer & Store Filters */}
          <FilterSection
            title="Customer & Store"
            hasActiveFilters={!!filters.customers || !!filters.storeCodes}
            onClear={() => {
              updateFilter('customers', undefined)
              updateFilter('storeCodes', undefined)
            }}
          >
            <div className="space-y-3">
              {optionsLoading ? (
                <div className="text-xs text-muted-foreground text-center py-2">
                  Loading options...
                </div>
              ) : (
                <>
                  <MultiSelectFilter
                    label="Customers"
                    value={filters.customers}
                    onChange={(value) => updateFilter('customers', value)}
                    options={customers}
                    placeholder="Select customers..."
                    nullLabel="No customer assigned"
                  />

                  <MultiSelectFilter
                    label="Store Codes"
                    value={filters.storeCodes}
                    onChange={(value) => updateFilter('storeCodes', value)}
                    options={storeCodes}
                    placeholder="Select stores..."
                    nullLabel="No store assigned"
                  />
                </>
              )}
            </div>
          </FilterSection>

          {/* Price Range Filter (Simplified) */}
          <FilterSection
            title="Price Range"
            hasActiveFilters={!!filters.storePrice}
            onClear={() => {
              updateFilter('storePrice', undefined)
            }}
          >
            <NumberRangeFilter
              label="Store Price"
              value={filters.storePrice}
              onChange={(value) => updateFilter('storePrice', value)}
              prefix="₱"
              min={0}
              max={1000000}
              step={100}
            />
            <div className="text-xs text-muted-foreground mt-1">
              Filter by the store price range
            </div>
          </FilterSection>

          {/* Legacy Search Support */}
          {filters.search && (
            <FilterSection
              title="Global Search"
              hasActiveFilters={!!filters.search}
              onClear={() => updateFilter('search', undefined)}
            >
              <div className="text-sm text-muted-foreground">
                Searching for: <span className="font-medium">&quot;{filters.search}&quot;</span>
              </div>
            </FilterSection>
          )}
        </div>
      </ScrollArea>

      {/* Footer */}
      <div className="p-4 border-t">
        <div className="text-xs text-muted-foreground text-center">
          {hasActiveFilters ? (
            `${getActiveFilterCount()} filter${getActiveFilterCount() !== 1 ? 's' : ''} applied`
          ) : (
            'No filters applied'
          )}
        </div>
      </div>
    </Card>
  )
}

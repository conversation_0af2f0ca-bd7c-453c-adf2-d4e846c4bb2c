import React from 'react'
import { Label } from '@/components/ui/label'
import { ToggleGroup, ToggleGroupItem } from '@/components/ui/toggle-group'
import { Badge } from '@/components/ui/badge'
import { X } from 'lucide-react'

interface StatusFilterProps {
  label: string
  value?: boolean | null
  onChange: (value: boolean | null | undefined) => void
  options?: {
    true: string
    false: string
    null?: string
  }
}

interface MultiStatusFilterProps {
  label: string
  value?: string[]
  onChange: (value: string[] | undefined) => void
  options: string[]
}

export function StatusFilter({
  label,
  value,
  onChange,
  options = { true: 'Yes', false: 'No', null: 'Any' }
}: StatusFilterProps) {
  const handleValueChange = (newValue: string) => {
    if (newValue === 'true') {
      onChange(true)
    } else if (newValue === 'false') {
      onChange(false)
    } else if (newValue === 'null') {
      onChange(null)
    } else {
      onChange(undefined)
    }
  }

  const getCurrentValue = () => {
    if (value === true) return 'true'
    if (value === false) return 'false'
    if (value === null) return 'null'
    return ''
  }

  return (
    <div className="space-y-2">
      <Label className="text-xs font-medium">{label}</Label>

      <ToggleGroup
        type="single"
        value={getCurrentValue()}
        onValueChange={handleValueChange}
        className="justify-start"
      >
        {options.null && (
          <ToggleGroupItem value="null" className="h-10 md:h-8 px-4 md:px-3 text-sm md:text-xs touch-target">
            {options.null}
          </ToggleGroupItem>
        )}
        <ToggleGroupItem value="true" className="h-10 md:h-8 px-4 md:px-3 text-sm md:text-xs touch-target">
          {options.true}
        </ToggleGroupItem>
        <ToggleGroupItem value="false" className="h-10 md:h-8 px-4 md:px-3 text-sm md:text-xs touch-target">
          {options.false}
        </ToggleGroupItem>
      </ToggleGroup>
    </div>
  )
}

export function MultiStatusFilter({
  label,
  value = [],
  onChange,
  options
}: MultiStatusFilterProps) {
  // const handleToggle = (option: string) => {
  //   const currentValue = value || []
  //   const newValue = currentValue.includes(option)
  //     ? currentValue.filter(v => v !== option)
  //     : [...currentValue, option]
  //
  //   onChange(newValue.length > 0 ? newValue : undefined)
  // }

  const handleRemove = (option: string) => {
    const newValue = (value || []).filter(v => v !== option)
    onChange(newValue.length > 0 ? newValue : undefined)
  }

  const handleClear = () => {
    onChange(undefined)
  }

  return (
    <div className="space-y-2">
      <div className="flex items-center justify-between">
        <Label className="text-xs font-medium">{label}</Label>
        {value && value.length > 0 && (
          <button
            onClick={handleClear}
            className="text-xs text-muted-foreground hover:text-foreground"
          >
            Clear all
          </button>
        )}
      </div>

      <div className="space-y-2">
        <ToggleGroup
          type="multiple"
          value={value || []}
          onValueChange={onChange}
          className="justify-start flex-wrap"
        >
          {options.map((option) => (
            <ToggleGroupItem
              key={option}
              value={option}
              className="h-10 md:h-8 px-4 md:px-3 text-sm md:text-xs touch-target"
            >
              {option}
            </ToggleGroupItem>
          ))}
        </ToggleGroup>

        {value && value.length > 0 && (
          <div className="flex flex-wrap gap-1">
            {value.map((option) => (
              <Badge
                key={option}
                variant="secondary"
                className="text-xs px-2 py-1 flex items-center gap-1"
              >
                {option}
                <button
                  onClick={() => handleRemove(option)}
                  className="hover:text-destructive"
                >
                  <X className="h-3 w-3" />
                </button>
              </Badge>
            ))}
          </div>
        )}
      </div>
    </div>
  )
}

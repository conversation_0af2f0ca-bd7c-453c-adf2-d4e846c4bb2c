'use client'

import { useState, useRef } from 'react'
import { Button } from '@/components/ui/button'
import { Card } from '@/components/ui/card'
import { Progress } from '@/components/ui/progress'
import { Badge } from '@/components/ui/badge'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { LuUpload, LuFile, LuDownload, LuCircleCheck, LuTriangleAlert, LuX, LuLoader } from 'react-icons/lu'
import { cn } from '@/lib/utils'

interface ImportError {
  row: number
  field?: string
  value?: any
  errorCode: string
  errorMessage: string
}

interface ImportWarning {
  row: number
  field?: string
  value?: any
  warningCode: string
  warningMessage: string
}

interface StoreImportResult {
  operationId: string
  totalRows: number
  processedRows: number
  successfulRows: number
  failedRows: number
  errors: ImportError[]
  warnings: ImportWarning[]
  createdStores: any[]
  updatedStores: any[]
  skippedRows: number[]
}

interface StoreImportDialogProps {
  isOpen: boolean
  onClose: () => void
  onImportComplete?: (result: StoreImportResult) => void
}

export function StoreImportDialog({ isOpen, onClose, onImportComplete }: StoreImportDialogProps) {
  const [dragActive, setDragActive] = useState(false)
  const [selectedFile, setSelectedFile] = useState<File | null>(null)
  const [isUploading, setIsUploading] = useState(false)
  const [importResult, setImportResult] = useState<StoreImportResult | null>(null)
  const [error, setError] = useState<string | null>(null)
  const fileInputRef = useRef<HTMLInputElement>(null)

  const validateFile = (file: File): string | null => {
    // Check file type
    const allowedTypes = [
      'text/csv',
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    ]
    
    if (!allowedTypes.includes(file.type)) {
      return 'Please select a CSV or Excel file (.csv, .xls, .xlsx)'
    }

    // Check file size (10MB limit)
    const maxSize = 10 * 1024 * 1024 // 10MB
    if (file.size > maxSize) {
      return 'File size must be less than 10MB'
    }

    return null
  }

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    setDragActive(false)

    const files = Array.from(e.dataTransfer.files)
    if (files.length > 0) {
      const file = files[0]
      const validationError = validateFile(file)
      if (validationError) {
        setError(validationError)
      } else {
        setSelectedFile(file)
        setError(null)
      }
    }
  }

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    setDragActive(true)
  }

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    setDragActive(false)
  }

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files
    if (files && files.length > 0) {
      const file = files[0]
      const validationError = validateFile(file)
      if (validationError) {
        setError(validationError)
      } else {
        setSelectedFile(file)
        setError(null)
      }
    }
  }

  const openFileDialog = () => {
    fileInputRef.current?.click()
  }

  const downloadTemplate = async (format: 'CSV' | 'EXCEL') => {
    try {
      const response = await fetch(`/api/enhanced/stores/template?format=${format}`)
      if (response.ok) {
        const blob = await response.blob()
        const url = window.URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.href = url
        a.download = `store_import_template.${format === 'CSV' ? 'csv' : 'xlsx'}`
        document.body.appendChild(a)
        a.click()
        window.URL.revokeObjectURL(url)
        document.body.removeChild(a)
      }
    } catch (error) {
      console.error('Error downloading template:', error)
      setError('Failed to download template')
    }
  }

  const handleImport = async () => {
    if (!selectedFile) return

    setIsUploading(true)
    setError(null)

    try {
      const formData = new FormData()
      formData.append('file', selectedFile)

      const response = await fetch('/api/enhanced/stores/import', {
        method: 'POST',
        body: formData
      })

      if (response.ok) {
        const result = await response.json()
        setImportResult(result)
        onImportComplete?.(result)
      } else {
        const errorData = await response.json()
        setError(errorData.error || 'Import failed')
      }
    } catch (error) {
      console.error('Import error:', error)
      setError('Failed to import file')
    } finally {
      setIsUploading(false)
    }
  }

  const resetDialog = () => {
    setSelectedFile(null)
    setImportResult(null)
    setError(null)
    setIsUploading(false)
    if (fileInputRef.current) {
      fileInputRef.current.value = ''
    }
  }

  const handleClose = () => {
    resetDialog()
    onClose()
  }

  const getProgressPercentage = () => {
    if (!importResult) return 0
    return Math.round((importResult.processedRows / importResult.totalRows) * 100)
  }

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Import Stores</DialogTitle>
          <DialogDescription>
            Upload a CSV or Excel file to import multiple stores at once
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Template Download */}
          <Card className="p-4">
            <h4 className="text-sm font-medium mb-2">Download Template</h4>
            <p className="text-sm text-muted-foreground mb-3">
              Download a template file with the correct format and sample data
            </p>
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => downloadTemplate('CSV')}
                className="flex items-center gap-2"
              >
                <LuDownload className="h-4 w-4" />
                CSV Template
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => downloadTemplate('EXCEL')}
                className="flex items-center gap-2"
              >
                <LuDownload className="h-4 w-4" />
                Excel Template
              </Button>
            </div>
          </Card>

          {/* File Upload */}
          {!importResult && (
            <Card className="p-6">
              <h4 className="text-sm font-medium mb-4">Upload File</h4>
              
              <input
                ref={fileInputRef}
                type="file"
                accept=".csv,.xls,.xlsx"
                onChange={handleFileSelect}
                className="hidden"
              />

              {!selectedFile ? (
                <div
                  className={cn(
                    "border-2 border-dashed rounded-lg p-8 text-center transition-colors cursor-pointer",
                    dragActive ? "border-primary bg-primary/5" : "border-border hover:border-border/80"
                  )}
                  onDrop={handleDrop}
                  onDragOver={handleDragOver}
                  onDragLeave={handleDragLeave}
                  onClick={openFileDialog}
                >
                  <LuUpload className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
                  <div className="text-lg font-medium mb-2">
                    Drop your file here or click to browse
                  </div>
                  <div className="text-sm text-muted-foreground">
                    Supports CSV and Excel files (max 10MB)
                  </div>
                </div>
              ) : (
                <div className="flex items-center justify-between p-4 border rounded-lg">
                  <div className="flex items-center gap-3">
                    <LuFile className="h-8 w-8 text-blue-600" />
                    <div>
                      <div className="font-medium">{selectedFile.name}</div>
                      <div className="text-sm text-muted-foreground">
                        {(selectedFile.size / 1024 / 1024).toFixed(2)} MB
                      </div>
                    </div>
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setSelectedFile(null)}
                  >
                    <LuX className="h-4 w-4" />
                  </Button>
                </div>
              )}

              {error && (
                <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-lg">
                  <div className="flex items-center gap-2 text-red-700">
                    <LuTriangleAlert className="h-4 w-4" />
                    <span className="text-sm font-medium">{error}</span>
                  </div>
                </div>
              )}
            </Card>
          )}

          {/* Import Results */}
          {importResult && (
            <Card className="p-6">
              <div className="flex items-center gap-2 mb-4">
                <LuCircleCheck className="h-5 w-5 text-green-600" />
                <h4 className="text-sm font-medium">Import Complete</h4>
              </div>

              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
                <div className="text-center">
                  <div className="text-2xl font-bold">{importResult.totalRows}</div>
                  <div className="text-sm text-muted-foreground">Total Rows</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-600">{importResult.successfulRows}</div>
                  <div className="text-sm text-muted-foreground">Successful</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-red-600">{importResult.failedRows}</div>
                  <div className="text-sm text-muted-foreground">Failed</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-yellow-600">{importResult.warnings.length}</div>
                  <div className="text-sm text-muted-foreground">Warnings</div>
                </div>
              </div>

              {importResult.errors.length > 0 && (
                <div className="space-y-2">
                  <h5 className="text-sm font-medium text-red-700">Errors:</h5>
                  <div className="max-h-32 overflow-y-auto space-y-1">
                    {importResult.errors.slice(0, 10).map((error, index) => (
                      <div key={index} className="text-xs p-2 bg-red-50 border border-red-200 rounded">
                        Row {error.row}: {error.errorMessage}
                      </div>
                    ))}
                    {importResult.errors.length > 10 && (
                      <div className="text-xs text-muted-foreground">
                        ... and {importResult.errors.length - 10} more errors
                      </div>
                    )}
                  </div>
                </div>
              )}

              {importResult.warnings.length > 0 && (
                <div className="space-y-2 mt-4">
                  <h5 className="text-sm font-medium text-yellow-700">Warnings:</h5>
                  <div className="max-h-32 overflow-y-auto space-y-1">
                    {importResult.warnings.slice(0, 5).map((warning, index) => (
                      <div key={index} className="text-xs p-2 bg-yellow-50 border border-yellow-200 rounded">
                        Row {warning.row}: {warning.warningMessage}
                      </div>
                    ))}
                    {importResult.warnings.length > 5 && (
                      <div className="text-xs text-muted-foreground">
                        ... and {importResult.warnings.length - 5} more warnings
                      </div>
                    )}
                  </div>
                </div>
              )}
            </Card>
          )}
        </div>

        <DialogFooter>
          {!importResult ? (
            <>
              <Button variant="outline" onClick={handleClose}>
                Cancel
              </Button>
              <Button
                onClick={handleImport}
                disabled={!selectedFile || isUploading}
                className="min-w-[100px]"
              >
                {isUploading ? (
                  <>
                    <LuLoader className="h-4 w-4 mr-2 animate-spin" />
                    Importing...
                  </>
                ) : (
                  'Import Stores'
                )}
              </Button>
            </>
          ) : (
            <>
              <Button variant="outline" onClick={resetDialog}>
                Import Another File
              </Button>
              <Button onClick={handleClose}>
                Close
              </Button>
            </>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}

import { NextRequest, NextResponse } from 'next/server'
import { OrderTrackingService } from '@/lib/order-tracking'

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    const orderId = parseInt(id)

    if (isNaN(orderId)) {
      return NextResponse.json(
        { error: 'Invalid order ID' },
        { status: 400 }
      )
    }

    const trackingSummary = await OrderTrackingService.getOrderTrackingSummary(orderId)

    if (!trackingSummary) {
      return NextResponse.json(
        { error: 'Order not found' },
        { status: 404 }
      )
    }

    return NextResponse.json(trackingSummary)
  } catch (error) {
    console.error('Error fetching order tracking:', error)
    return NextResponse.json(
      { error: 'Error fetching order tracking data' },
      { status: 500 }
    )
  }
}

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    const orderId = parseInt(id)

    if (isNaN(orderId)) {
      return NextResponse.json(
        { error: 'Invalid order ID' },
        { status: 400 }
      )
    }

    const body = await request.json()
    const { action, data } = body

    switch (action) {
      case 'recalculate_metrics':
        await OrderTrackingService.calculateOrderMetrics(orderId)
        return NextResponse.json({ success: true, message: 'Metrics recalculated' })

      case 'add_event':
        await OrderTrackingService.recordEvent({
          orderId,
          eventType: data.eventType,
          eventData: data.eventData,
          performedBy: data.performedBy,
          notes: data.notes
        })
        return NextResponse.json({ success: true, message: 'Event recorded' })

      case 'add_status_change':
        await OrderTrackingService.recordStatusChange({
          orderId,
          fromStatus: data.fromStatus,
          toStatus: data.toStatus,
          statusType: data.statusType,
          changedBy: data.changedBy,
          changeReason: data.changeReason,
          metadata: data.metadata
        })
        return NextResponse.json({ success: true, message: 'Status change recorded' })

      default:
        return NextResponse.json(
          { error: 'Invalid action' },
          { status: 400 }
        )
    }
  } catch (error) {
    console.error('Error processing tracking action:', error)
    return NextResponse.json(
      { error: 'Error processing tracking action' },
      { status: 500 }
    )
  }
}

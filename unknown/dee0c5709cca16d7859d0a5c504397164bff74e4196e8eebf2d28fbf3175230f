'use client'

import { useState, useRef } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { ImagePreview } from '@/components/ui/image-preview'
import { LuUpload, LuImage, LuX } from 'react-icons/lu'
import { cn } from '@/lib/utils'

interface ImageHandlerProps {
  value?: string | null
  onChange: (file: File | null) => void
  onRemove?: () => void
  className?: string
  variant?: 'upload' | 'preview' | 'compact'
  accept?: string
  maxSize?: number // in MB
  placeholder?: string
  disabled?: boolean
  required?: boolean
  showPreview?: boolean
  previewMode?: 'dialog' | 'toggle'
  initialPreviewSize?: 'full' | 'small'
}

export function ImageHandler({
  value,
  onChange,
  onRemove,
  className,
  variant = 'upload',
  accept = 'image/*',
  maxSize = 5,
  placeholder = 'Upload an image',
  disabled = false,
  required = false,
  showPreview = true,
  previewMode = 'dialog',
  initialPreviewSize = 'full'
}: ImageHandlerProps) {
  const [dragActive, setDragActive] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const fileInputRef = useRef<HTMLInputElement>(null)

  const validateFile = (file: File): string | null => {
    // Check file type
    if (!file.type.startsWith('image/')) {
      return 'Please select a valid image file'
    }

    // Check file size
    if (file.size > maxSize * 1024 * 1024) {
      return `File size must be less than ${maxSize}MB`
    }

    return null
  }

  const handleFileSelect = (file: File) => {
    const validationError = validateFile(file)
    if (validationError) {
      setError(validationError)
      return
    }

    setError(null)
    onChange(file)
  }

  const handleFileInput = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (file) {
      handleFileSelect(file)
    }
  }

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault()
    setDragActive(false)

    const file = e.dataTransfer.files?.[0]
    if (file) {
      handleFileSelect(file)
    }
  }

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault()
    setDragActive(true)
  }

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault()
    setDragActive(false)
  }

  const handleRemove = () => {
    setError(null)
    onChange(null)
    if (onRemove) {
      onRemove()
    }
    if (fileInputRef.current) {
      fileInputRef.current.value = ''
    }
  }

  const openFileDialog = () => {
    fileInputRef.current?.click()
  }

  // Compact variant - minimal UI
  if (variant === 'compact') {
    return (
      <div className={cn('flex items-center gap-2', className)}>
        {value && showPreview && (
          <ImagePreview
            src={typeof value === 'string' ? value : URL.createObjectURL(value as File)}
            alt="Preview"
            className="w-8 h-8 object-cover rounded"
            useToggleMode={previewMode === 'toggle'}
            initialSize={initialPreviewSize}
          />
        )}
        <Button
          type="button"
          variant="outline"
          size="sm"
          onClick={openFileDialog}
          disabled={disabled}
          className="h-8"
        >
          <LuUpload className="h-3 w-3 mr-1" />
          {value ? 'Change' : 'Upload'}
        </Button>
        {value && (
          <Button
            type="button"
            variant="ghost"
            size="sm"
            onClick={handleRemove}
            disabled={disabled}
            className="h-8 w-8 p-0"
          >
            <LuX className="h-3 w-3" />
          </Button>
        )}
        <input
          ref={fileInputRef}
          type="file"
          accept={accept}
          onChange={handleFileInput}
          className="hidden"
          disabled={disabled}
          required={required}
        />
        {error && (
          <span className="text-xs text-destructive">{error}</span>
        )}
      </div>
    )
  }

  // Preview variant - shows existing image with controls
  if (variant === 'preview' && value) {
    return (
      <div className={cn('space-y-3', className)}>
        <ImagePreview
          src={typeof value === 'string' ? value : URL.createObjectURL(value as File)}
          alt="Preview"
          showChangeButton={true}
          onChangeImage={openFileDialog}
          onRemoveImage={handleRemove}
          isEditing={true}
          useToggleMode={previewMode === 'toggle'}
          initialSize={initialPreviewSize}
        />
        <input
          ref={fileInputRef}
          type="file"
          accept={accept}
          onChange={handleFileInput}
          className="hidden"
          disabled={disabled}
          required={required}
        />
        {error && (
          <div className="text-sm text-destructive">{error}</div>
        )}
      </div>
    )
  }

  // Upload variant - drag and drop area
  return (
    <div className={cn('space-y-3', className)}>
      <div
        className={cn(
          'border-2 border-dashed rounded-lg p-6 text-center transition-colors',
          dragActive
            ? 'border-primary bg-primary/5'
            : 'border-muted-foreground/25 hover:border-muted-foreground/50',
          disabled && 'opacity-50 cursor-not-allowed'
        )}
        onDrop={handleDrop}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
      >
        <div className="flex flex-col items-center gap-2">
          <LuImage className="h-8 w-8 text-muted-foreground" />
          <div className="text-sm text-muted-foreground">
            <span className="font-medium">{placeholder}</span>
            <br />
            Drag and drop or{' '}
            <button
              type="button"
              onClick={openFileDialog}
              disabled={disabled}
              className="text-primary hover:underline"
            >
              browse files
            </button>
          </div>
          <div className="text-xs text-muted-foreground">
            Max size: {maxSize}MB
          </div>
        </div>
      </div>

      {value && showPreview && (
        <ImagePreview
          src={typeof value === 'string' ? value : URL.createObjectURL(value as File)}
          alt="Preview"
          showChangeButton={true}
          onChangeImage={openFileDialog}
          onRemoveImage={handleRemove}
          isEditing={true}
          useToggleMode={previewMode === 'toggle'}
          initialSize={initialPreviewSize}
        />
      )}

      <input
        ref={fileInputRef}
        type="file"
        accept={accept}
        onChange={handleFileInput}
        className="hidden"
        disabled={disabled}
        required={required}
      />

      {error && (
        <div className="text-sm text-destructive">{error}</div>
      )}
    </div>
  )
}

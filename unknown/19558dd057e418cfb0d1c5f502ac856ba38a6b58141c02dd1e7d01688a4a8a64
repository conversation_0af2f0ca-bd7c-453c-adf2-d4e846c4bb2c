Project Documentation: "Pasabuy Pal" - Item Tracking App (Self-Hosted PWA)

Version: 1.0


1. Project Overview:
"Pasabuy Pal" is a self-hosted Progressive Web Application (PWA) designed to help individuals manage and track items they are purchasing on behalf of others (a "pasabuy" service). The app facilitates encoding item details received from resellers, tracking purchase status, managing packing status, and calculating basic profit metrics. This document outlines Phase 1 (Core Functionality) with considerations for future enhancements.

2. Target User:
Individuals running a small-scale "pasabuy" or personal shopping service who require a self-hosted, web-accessible solution.

3. Core Workflow:

    Item Request: A reseller sends an image of an item they want the app user to purchase.

    Item Encoding: The app user inputs details for this item via the "Pasabuy Pal" interface, including uploading the image, product name, quantity, store price, pasabuy fee, store code, and reseller.

    Shopping & Status Update: The user consults the app (e.g., filtered by store on the "Buy List") to see what needs to be bought. Once an item is purchased, its status is updated to "Bought (Y)".

    Packing & Status Update: After shopping, the user reviews items marked as "Bought (Y)". As they pack items for each reseller, they update the item's "Packing" status to "Packed".

4. Chosen Tech Stack (Self-Hosted Focus):

    Framework: Next.js (v13+ with App Router recommended)

        Reason: Full-stack capabilities (React for frontend, API Routes for backend), excellent PWA support, strong community, optimized for production. Simplifies development and deployment for a cohesive project.

    UI Styling: Tailwind CSS

        Reason: Utility-first for rapid, custom styling. Integrates seamlessly with Next.js and Shadcn/ui.

    UI Components: Shadcn/ui

        Reason: Collection of beautifully designed, accessible, copy-pasteable React components (built with Radix UI, styled with Tailwind CSS). User owns component code for full customization.

    Icons: lucide-react

        Reason: Comprehensive, lightweight, and easily integrated icon library, commonly used with Shadcn/ui.

    Database: PostgreSQL (Self-Hosted)

        Reason: Powerful, open-source, relational database. Reliable for self-hosting and scales well. Managed via Docker.

    Backend Logic: Next.js API Routes (app/api/...)

        Reason: Handles CRUD operations, image uploads, and server-side logic, co-located with frontend code.

    Image Storage: Local Filesystem (managed by Next.js backend, self-hosted)

        Reason: Simplest approach for initial self-hosted deployment. Images stored in a designated server folder.

    State Management (Frontend): Zustand

        Reason: Simple, modern, powerful state management for React. React Context API for localized state.

    PWA Capabilities: next-pwa plugin

        Reason: Simplifies service worker and manifest file generation for PWA functionality in Next.js.

    Deployment: Docker & Docker Compose

        Reason: Containerizes the Next.js app and PostgreSQL database for consistent environments and easy self-hosted deployment.

5. Data Model (PostgreSQL):

    StoreCodes Table:

        id (SERIAL PRIMARY KEY)

        code (VARCHAR(10) UNIQUE NOT NULL, e.g., "ES", "DM")

        name (VARCHAR(255), Optional, full name of the store)

        created_at (TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP)

    Resellers Table:

        id (SERIAL PRIMARY KEY)

        name (VARCHAR(255) UNIQUE NOT NULL)

        created_at (TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP)

    Items Table:

        id (SERIAL PRIMARY KEY)

        product_name (VARCHAR(255) NOT NULL)

        quantity (INTEGER NOT NULL DEFAULT 1)

        image_filename (VARCHAR(255), Optional - stores the filename of the uploaded image)

        store_price (DECIMAL(10, 2) DEFAULT 0.00)

        pasabuy_fee (DECIMAL(10, 2) DEFAULT 0.00)

        reseller_price (DECIMAL(10, 2) DEFAULT 0.00)

        is_bought (BOOLEAN DEFAULT FALSE) -- FALSE for N, TRUE for Y

        packing_status (VARCHAR(20) DEFAULT 'Not Packed') -- "Not Packed", "Packed"

        store_code_id (INTEGER REFERENCES StoreCodes(id) ON DELETE SET NULL)

        reseller_id (INTEGER REFERENCES Resellers(id) ON DELETE SET NULL)

        created_at (TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP)

        updated_at (TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP)

6. Key Screens & Functionality (Phase 1):

    Overall Structure:

        Bottom Navigation Bar for main sections.

        Top bar on list views for actions like Search, Refresh (future: Select/Edit).

        Floating Action Buttons (FABs) for adding new entities.

    Main Navigation Tabs (Bottom Bar):

        Buy List: Primary operational view.

        Resellers: Manage resellers and view their item status.

        Packing: Focus on items ready for packing.

        Items: (Optional for Phase 1, can be a simple master list if needed).

        Stores: Manage store codes.

    Screen Details:

        Screen 1: Buy List (Filtered by Store Code - e.g., app/buy-list/page.tsx)

            Displays "All" (shows all items not bought, across all stores) and a list of Store Codes.

            Each Store Code shows a count of its items with is_bought = FALSE.

            Tapping a Store Code navigates to a filtered item list view for that store (showing items with is_bought = FALSE).

        Screen 2 & 3: Resellers List View (app/resellers/page.tsx)

            Lists all resellers.

            Reseller Status Dot Color Logic: Dynamically colored dot next to each reseller name:

                Green Dot: If the majority (e.g., >75%) of that reseller's items are is_bought = TRUE AND packing_status = 'Not Packed'.

                Blue Dot: If the majority (e.g., >75%) of that reseller's items (that are is_bought = TRUE) are packing_status = 'Packed'.

                Red Dot: If the majority (e.g., >75%) of that reseller's items are is_bought = FALSE.

                (Default/Neutral color if none of the above majority conditions are met strongly).

            Item Count / Contextual Info:

                In a general context (Screen 2): Displays total items or items pending action for that reseller.

                When "Packing" tab is active (or a similar packing context - Screen 3): The count next to the reseller name should represent items for that reseller that are is_bought = TRUE AND packing_status = 'Not Packed'. The overall "greyness" or visual cue implies the general purchase status (many not bought yet for that reseller).

            FAB with "+" to add a new reseller (opens a form/dialog).

        Screen 4: Filtered Item List View (e.g., app/buy-list/[storeCode]/page.tsx or app/packing/page.tsx)

            Displays items based on active filters (e.g., by Store Code and is_bought = FALSE, or by is_bought = TRUE AND packing_status = 'Not Packed').

            Each item card displays: Thumbnail image, Product Name, Quantity, Store Code.

            Includes controls (e.g., toggle/button) to update item status (is_bought, packing_status).

            FAB with "+" to add a new item, pre-filling relevant context (e.g., Store Code if filtered by store).

        Screen 5: Add/Edit Item Form (Component/Dialog/Page - e.g., components/forms/ItemForm.tsx)

            Inputs (using Shadcn/ui components):

                Image Upload: Input type="file" styled.

                Quantity: Input type="number" with Button steppers.

                Product Name: Input.

                Store Price: Input type="number" with Button steppers.

                Pasabuy Fee: Input type="number" with Button steppers.

                Store Code: Select or Combobox populated from /api/store-codes.

                Reseller: Select or Combobox populated from /api/resellers.

                Reseller Price: Input type="number". Manually entered price charged to the reseller.

                Bought Status (Y/N): ToggleGroup or RadioGroup.

                Packing Status (Not Packed/Packed): ToggleGroup or RadioGroup.

            Displayed Calculations:

                Item Profit = Quantity * PasabuyFee (displayed dynamically).

            Buttons: "Cancel" and "Save".

7. Backend API (Next.js API Routes - app/api/...):

    Database Connection Utility: For connecting to PostgreSQL.

    /api/store-codes:

        GET: List all store codes. Include count of associated items (e.g., is_bought = FALSE).

        POST: Create a new store code.

    /api/resellers:

        GET: List all resellers. Include counts of associated items based on different statuses (total, to buy, to pack) to support dynamic dot colors and contextual counts.

        POST: Create a new reseller.

    /api/orders:

        GET: List orders with filtering (by store_code_id, customer_id, is_bought, packing_status, search term) and sorting.

        POST: Create a new order. Handles image upload (e.g., using multer or FormData) to local filesystem (public/uploads/orders or a configured path) and saves image_filename.

        PUT /api/orders/[id]: Update an order (details, is_bought, packing_status).

        DELETE /api/orders/[id]: Delete an order.

    (Optional) /api/images/[filename]: If images are not stored in public, an endpoint to securely serve them.

8. Calculations & Dynamic Data:

    Item Form (Client-Side & Backend Validation):

        ResellerPrice = Manually entered price charged to the reseller.

        ItemProfit = Quantity * PasabuyFee (displayed).

        ItemTotalResellerPrice = Quantity * ResellerPrice.

        ItemTotalStoreCost = Quantity * StorePrice.

    List Views (Counts via Backend Aggregation):

        Store Code counts on "Buy List" (items with is_bought = FALSE).

        Reseller item counts for dot color logic and "Packing" tab context.

    Zustand: Used for managing global UI state, potentially caching fetched lists (resellers, store codes) to avoid redundant API calls.

9. Filtering Strategy:

    Global Filters (accessible on main list views):

        Status: is_bought (All, Bought, Not Bought), packing_status (All, Packed, Not Packed).

        Entity: Reseller (dropdown), Store Code (dropdown).

    Contextual Filters: Applied implicitly by navigating to specific sections (e.g., "Buy List" shows is_bought = FALSE).

    Sorting Options (for item lists): Date Added, Product Name, Quantity, Prices.

    Search: Text search for Product Name (primary), potentially Reseller Name, Store Code.

    Implementation: API parameters for filtering/sorting, frontend controls using Shadcn/ui.

10. PWA Configuration (next.config.js & public folder):

    Use next-pwa plugin for service worker and manifest generation.

    Configure caching strategies (cache-first for static assets, network-first or stale-while-revalidate for API data).

    Provide app icons and manifest.json details.

    Ensure HTTPS for deployment.

11. Docker Setup (Dockerfile & docker-compose.yml):

    Dockerfile (Next.js app): Standard Node.js multi-stage build for Next.js.

    docker-compose.yml:

        app service (Next.js): Builds from Dockerfile, maps port, sets environment variables (DB connection), mounts volume for image uploads.

        db service (PostgreSQL): Uses official postgres image, sets environment variables, mounts volume for persistent data.

        Defines a shared network.

12. Future Enhancements (Phase 2 and Beyond):

    Dashboard/Summary: Overview of key metrics.

    Advanced Filtering & Sorting: More granular options.

    Enhanced Search Functionality.

    Reseller Communication Log/Notes.

    Order Grouping/Batching ("Shopping Trips").

    Financial Summaries & Reports.

    User-facing Notifications/Reminders.

    Bulk Operations: Mark multiple items bought/packed.

    Image Optimization & Robust Storage: (e.g., self-hosted MinIO).

    Data Export: CSV/Excel.

    User Accounts & Authentication: (e.g., NextAuth.js).

    Barcode Scanning Integration.

    Customizable Reseller Pricing Rules.

    "Packing Slip" Generation.
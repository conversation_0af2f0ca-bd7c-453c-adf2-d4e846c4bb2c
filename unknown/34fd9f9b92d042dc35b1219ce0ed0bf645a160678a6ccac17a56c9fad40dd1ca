import { useState, useCallback } from 'react'

/**
 * Standard async state interface used across the application
 */
export interface AsyncState<T> {
  data: T | null
  isLoading: boolean
  error: string | null
}

/**
 * Custom hook for managing async operations state
 * Consolidates loading, error, and data state management
 */
export function useAsyncState<T>(initialData: T | null = null): {
  state: AsyncState<T>
  setData: (data: T) => void
  setLoading: (loading: boolean) => void
  setError: (error: string | null) => void
  reset: () => void
  execute: <Args extends unknown[]>(
    asyncFn: (...args: Args) => Promise<T>
  ) => (...args: Args) => Promise<T | null>
} {
  const [state, setState] = useState<AsyncState<T>>({
    data: initialData,
    isLoading: false,
    error: null
  })

  const setData = useCallback((data: T) => {
    setState(prev => ({ ...prev, data, error: null }))
  }, [])

  const setLoading = useCallback((isLoading: boolean) => {
    setState(prev => ({ ...prev, isLoading }))
  }, [])

  const setError = useCallback((error: string | null) => {
    setState(prev => ({ ...prev, error, isLoading: false }))
  }, [])

  const reset = useCallback(() => {
    setState({
      data: initialData,
      isLoading: false,
      error: null
    })
  }, [initialData])

  const execute = useCallback(<Args extends unknown[]>(
    asyncFn: (...args: Args) => Promise<T>
  ) => {
    return async (...args: Args): Promise<T | null> => {
      setLoading(true)
      setError(null)

      try {
        const result = await asyncFn(...args)
        setData(result)
        return result
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'An error occurred'
        setError(errorMessage)
        return null
      } finally {
        setLoading(false)
      }
    }
  }, [setData, setError, setLoading])

  return {
    state,
    setData,
    setLoading,
    setError,
    reset,
    execute
  }
}

/**
 * Custom hook for API calls with standardized error handling
 */
export function useApiCall<T>() {
  const { state, execute, reset } = useAsyncState<T>()

  const call = useCallback(async (
    url: string,
    options?: RequestInit
  ): Promise<T | null> => {
    const apiCall = async () => {
      const response = await fetch(url, options)

      if (!response.ok) {
        throw new Error(`API call failed: ${response.status} ${response.statusText}`)
      }

      return response.json() as Promise<T>
    }

    return execute(apiCall)()
  }, [execute])

  return {
    ...state,
    call,
    reset
  }
}

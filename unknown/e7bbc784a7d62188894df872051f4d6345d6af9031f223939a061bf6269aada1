"use client"

import * as React from "react"
import { Check, ChevronsUpDown, Plus } from "lucide-react"

import { cn } from "@/lib/utils"
import { But<PERSON> } from "@/components/ui/button"
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"

interface ComboboxOption {
  value: string
  label: string
}

interface ComboboxProps {
  options: ComboboxOption[]
  value?: string
  onValueChange: (value: string) => void
  placeholder?: string
  searchPlaceholder?: string
  emptyText?: string
  className?: string
  disabled?: boolean
  onCreateNew?: (searchTerm: string) => Promise<void>
  isCreating?: boolean
  mode?: 'select' | 'autocomplete' // New prop for different modes
}

export function Combobox({
  options,
  value,
  onValueChange,
  placeholder = "Select option...",
  searchPlaceholder = "Search...",
  emptyText = "No option found.",
  className,
  disabled = false,
  onCreateNew,
  isCreating = false,
  mode = 'select',
}: ComboboxProps) {
  const [open, setOpen] = React.useState(false)
  const [searchTerm, setSearchTerm] = React.useState("")
  const containerRef = React.useRef<HTMLDivElement>(null)

  // Handle clicking outside to close dropdown in autocomplete mode
  React.useEffect(() => {
    if (mode === 'autocomplete' && open) {
      const handleClickOutside = (event: MouseEvent) => {
        if (containerRef.current && !containerRef.current.contains(event.target as Node)) {
          setOpen(false)
        }
      }

      document.addEventListener('mousedown', handleClickOutside)
      return () => {
        document.removeEventListener('mousedown', handleClickOutside)
      }
    }
  }, [mode, open])

  const filteredOptions = React.useMemo(() => {
    const searchValue = mode === 'autocomplete' ? (value || '') : searchTerm
    if (!searchValue) return options
    return options.filter((option) =>
      option.label.toLowerCase().includes(searchValue.toLowerCase())
    )
  }, [options, searchTerm, value, mode])

  // Show all options when dropdown is opened and no search value
  const displayOptions = React.useMemo(() => {
    if (mode === 'autocomplete' && open && (!value || value.trim() === '')) {
      return options
    }
    return filteredOptions
  }, [mode, open, value, options, filteredOptions])

  const selectedOption = options.find((option) => option.value === value)

  const showCreateNew = onCreateNew &&
    (mode === 'autocomplete' ? value : searchTerm) &&
    !(mode === 'autocomplete' ? displayOptions : filteredOptions).some(option =>
      option.label.toLowerCase() === (mode === 'autocomplete' ? value : searchTerm)?.toLowerCase()
    )

  const handleCreateNew = async () => {
    const termToCreate = mode === 'autocomplete' ? value : searchTerm
    if (onCreateNew && termToCreate) {
      await onCreateNew(termToCreate)
      setSearchTerm("")
      setOpen(false)
    }
  }

  return (
    <div className="relative" ref={containerRef}>
      {mode === 'autocomplete' ? (
        <>
          <input
            type="text"
            value={value || ''}
            onChange={(e) => onValueChange(e.target.value)}
            onFocus={() => setOpen(true)}
            placeholder={placeholder}
            className={cn(
              "flex h-9 w-full rounded-md border border-input bg-transparent px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50",
              className
            )}
            disabled={disabled}
          />
          <ChevronsUpDown className="absolute right-3 top-1/2 h-4 w-4 -translate-y-1/2 shrink-0 opacity-50 pointer-events-none" />
          {open && (
            <div className="absolute top-full left-0 right-0 z-[60] mt-1">
              <div className="rounded-md border bg-popover p-0 text-popover-foreground shadow-lg outline-none max-h-[300px] overflow-hidden">
                <Command>
                  <CommandList>
                    <CommandEmpty>
                      {showCreateNew ? (
                        <div className="p-2">
                          <Button
                            variant="ghost"
                            className="w-full justify-start"
                            onClick={handleCreateNew}
                            disabled={isCreating}
                          >
                            <Plus className="mr-2 h-4 w-4" />
                            {isCreating ? "Creating..." : `Create "${value}"`}
                          </Button>
                        </div>
                      ) : (
                        <div className="p-2 text-sm text-muted-foreground">{emptyText}</div>
                      )}
                    </CommandEmpty>
                    <CommandGroup>
                      {displayOptions.map((option) => (
                        <CommandItem
                          key={option.value}
                          value={option.value}
                          onSelect={() => {
                            onValueChange(option.label)
                            setOpen(false)
                          }}
                          className="cursor-pointer"
                        >
                          <Check
                            className={cn(
                              "mr-2 h-4 w-4",
                              value === option.label ? "opacity-100" : "opacity-0"
                            )}
                          />
                          {option.label}
                        </CommandItem>
                      ))}
                      {showCreateNew && displayOptions.length > 0 && (
                        <CommandItem
                          onSelect={handleCreateNew}
                          disabled={isCreating}
                          className="cursor-pointer"
                        >
                          <Plus className="mr-2 h-4 w-4" />
                          {isCreating ? "Creating..." : `Create "${value}"`}
                        </CommandItem>
                      )}
                    </CommandGroup>
                  </CommandList>
                </Command>
              </div>
            </div>
          )}
        </>
      ) : (
        <Popover open={open} onOpenChange={setOpen}>
          <PopoverTrigger asChild>
            <Button
              variant="outline"
              role="combobox"
              aria-expanded={open}
              className={cn("w-full justify-between", className)}
              disabled={disabled}
            >
              {selectedOption ? selectedOption.label : placeholder}
              <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-[var(--radix-popover-trigger-width)] min-w-[8rem] p-0" align="start">
            <Command>
              <CommandInput
                placeholder={searchPlaceholder}
                value={searchTerm}
                onValueChange={setSearchTerm}
              />
              <CommandList>
                <CommandEmpty>
                  {showCreateNew ? (
                    <div className="p-2">
                      <Button
                        variant="ghost"
                        className="w-full justify-start"
                        onClick={handleCreateNew}
                        disabled={isCreating}
                      >
                        <Plus className="mr-2 h-4 w-4" />
                        {isCreating ? "Creating..." : `Create "${searchTerm}"`}
                      </Button>
                    </div>
                  ) : (
                    emptyText
                  )}
                </CommandEmpty>
                <CommandGroup>
                  {filteredOptions.map((option) => (
                    <CommandItem
                      key={option.value}
                      value={option.value}
                      onSelect={(currentValue) => {
                        onValueChange(currentValue === value ? "" : currentValue)
                        setOpen(false)
                        setSearchTerm("")
                      }}
                    >
                      <Check
                        className={cn(
                          "mr-2 h-4 w-4",
                          value === option.value ? "opacity-100" : "opacity-0"
                        )}
                      />
                      {option.label}
                    </CommandItem>
                  ))}
                  {showCreateNew && filteredOptions.length > 0 && (
                    <CommandItem
                      onSelect={handleCreateNew}
                      disabled={isCreating}
                    >
                      <Plus className="mr-2 h-4 w-4" />
                      {isCreating ? "Creating..." : `Create "${searchTerm}"`}
                    </CommandItem>
                  )}
                </CommandGroup>
              </CommandList>
            </Command>
          </PopoverContent>
        </Popover>
      )}
    </div>
  )
}

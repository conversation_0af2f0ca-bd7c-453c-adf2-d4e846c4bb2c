'use client'

import { BaseOrderCard } from './base-order-card'

interface Order {
  id: number
  productName: string
  quantity: number
  usageUnit?: string | null
  comment?: string | null
  storePrice: number
  pasabuyFee: number
  customerPrice: number
  resellerPrice: number // For compatibility
  isBought: boolean
  packingStatus: string
  imageFilename?: string | null
  storeCode?: {
    id: number
    code: string
    name: string | null
  } | null
  customer?: {
    id: number
    name: string
  } | null
}

interface CustomerOrderCardProps {
  item: Order
  isSelected?: boolean
  isBulkMode?: boolean
  onToggleSelection?: (orderId: number) => void
  onLongPress?: (orderId: number) => void
  onCardClick?: (orderId: number, event: React.MouseEvent) => void
  displayOnly?: boolean
}

export function CustomerOrderCard({
  item,
  isSelected = false,
  isBulkMode = false,
  onToggleSelection = () => {},
  onLongPress = () => {},
  onCardClick = () => {},
  displayOnly = false,
}: CustomerOrderCardProps) {
  if (displayOnly) {
    return (
      <BaseOrderCard
        item={item}
        variant="customer-display"
        isSelected={false}
        isBulkMode={false}
        onToggleSelection={() => {}}
        onLongPress={() => {}}
        onCardClick={() => {}}
        actions={undefined}
      />
    )
  }

  return (
    <BaseOrderCard
      item={item}
      variant="customer-compact"
      isSelected={isSelected}
      isBulkMode={isBulkMode}
      onToggleSelection={onToggleSelection}
      onLongPress={onLongPress}
      onCardClick={onCardClick}
      actions={undefined}
    />
  )
}

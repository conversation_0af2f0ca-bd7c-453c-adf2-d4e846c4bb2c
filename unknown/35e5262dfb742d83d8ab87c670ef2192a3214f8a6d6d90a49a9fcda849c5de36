import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

// Helper function to generate dates for testing
function generateTestDate(daysAgo: number): Date {
  const date = new Date()
  date.setDate(date.getDate() - daysAgo)
  return date
}

async function main() {
  console.log('🌱 Starting database seeding...')

  // Clear existing data
  await prisma.invoiceItem.deleteMany()
  await prisma.invoice.deleteMany()
  await prisma.order.deleteMany()
  await prisma.storeCode.deleteMany()
  await prisma.customer.deleteMany()

  // Create Store Codes with varied creation dates
  const storeCodes = await Promise.all([
    prisma.storeCode.create({
      data: {
        code: 'SM',
        name: 'SM Mall of Asia',
        createdAt: generateTestDate(120) // 4 months ago
      }
    }),
    prisma.storeCode.create({
      data: {
        code: 'AYALA',
        name: 'Ayala Malls Makati',
        createdAt: generateTestDate(90) // 3 months ago
      }
    }),
    prisma.storeCode.create({
      data: {
        code: 'ROBINSONS',
        name: '<PERSON>s Galleria',
        createdAt: generateTestDate(75) // 2.5 months ago
      }
    }),
    prisma.storeCode.create({
      data: {
        code: 'TRINOMA',
        name: 'TriNoma Mall',
        createdAt: generateTestDate(60) // 2 months ago
      }
    }),
    prisma.storeCode.create({
      data: {
        code: 'GREENBELT',
        name: 'Greenbelt Mall',
        createdAt: generateTestDate(45) // 1.5 months ago
      }
    }),
    prisma.storeCode.create({
      data: {
        code: 'POWERPLANT',
        name: 'Power Plant Mall',
        createdAt: generateTestDate(30) // 1 month ago
      }
    })
  ])

  // Create Customers with varied creation dates
  const customers = await Promise.all([
    prisma.customer.create({
      data: {
        name: 'Maria Santos',
        createdAt: generateTestDate(100) // ~3.3 months ago
      }
    }),
    prisma.customer.create({
      data: {
        name: 'John Dela Cruz',
        createdAt: generateTestDate(85) // ~2.8 months ago
      }
    }),
    prisma.customer.create({
      data: {
        name: 'Anna Reyes',
        createdAt: generateTestDate(70) // ~2.3 months ago
      }
    }),
    prisma.customer.create({
      data: {
        name: 'Carlos Mendoza',
        createdAt: generateTestDate(55) // ~1.8 months ago
      }
    }),
    prisma.customer.create({
      data: {
        name: 'Lisa Garcia',
        createdAt: generateTestDate(40) // ~1.3 months ago
      }
    }),
    prisma.customer.create({
      data: {
        name: 'Miguel Rodriguez',
        createdAt: generateTestDate(25) // ~3.5 weeks ago
      }
    }),
    prisma.customer.create({
      data: {
        name: 'Sofia Villanueva',
        createdAt: generateTestDate(15) // ~2 weeks ago
      }
    })
  ])

  // Sample orders with realistic Filipino shopping data and varied dates
  const orders = [
    // Electronics & Gadgets - Recent entries (last 7 days)
    {
      productName: 'iPhone 15 Pro Max 256GB',
      quantity: 1,
      usageUnit: 'piece',
      comment: 'Natural Titanium color preferred',
      storePrice: 75990.00,
      pasabuyFee: 2000.00,
      customerPrice: 77990.00,
      isBought: false,
      packingStatus: 'Not Packed',
      imageFilename: 'iphone-15-pro-max.jpg',
      storeCodeId: storeCodes[0].id, // SM
      customerId: customers[0].id, // Maria Santos
      createdAt: generateTestDate(2), // 2 days ago
      updatedAt: generateTestDate(1) // Updated yesterday
    },
    {
      productName: 'Samsung Galaxy S24 Ultra',
      quantity: 1,
      usageUnit: 'piece',
      comment: 'Titanium Gray, 512GB storage',
      storePrice: 69990.00,
      pasabuyFee: 1500.00,
      customerPrice: 71490.00,
      isBought: true,
      packingStatus: 'Not Packed',
      imageFilename: 'samsung-s24-ultra.jpg',
      storeCodeId: storeCodes[1].id, // Ayala
      customerId: customers[1].id, // John Dela Cruz
      createdAt: generateTestDate(5), // 5 days ago
      updatedAt: generateTestDate(3) // Updated 3 days ago
    },
    {
      productName: 'Apple AirPods Pro 2nd Gen',
      quantity: 2,
      usageUnit: 'pair',
      comment: 'USB-C version, one for backup',
      storePrice: 14990.00,
      pasabuyFee: 500.00,
      customerPrice: 15490.00,
      isBought: false,
      packingStatus: 'Not Packed',
      imageFilename: 'airpods-pro-2.jpg',
      storeCodeId: storeCodes[0].id, // SM
      customerId: customers[2].id, // Anna Reyes
      createdAt: generateTestDate(1), // Yesterday
      updatedAt: generateTestDate(0) // Updated today
    },
    {
      productName: 'MacBook Air M3 15-inch',
      quantity: 1,
      usageUnit: 'piece',
      comment: '16GB RAM, 512GB SSD, Space Gray',
      storePrice: 89990.00,
      pasabuyFee: 2500.00,
      customerPrice: 92490.00,
      isBought: false,
      packingStatus: 'Not Packed',
      imageFilename: 'macbook-air-m3.jpg',
      storeCodeId: storeCodes[2].id, // Robinsons
      customerId: customers[3].id, // Carlos Mendoza
      createdAt: generateTestDate(3), // 3 days ago
      updatedAt: generateTestDate(2) // Updated 2 days ago
    },
    {
      productName: 'Sony WH-1000XM5 Headphones',
      quantity: 1,
      usageUnit: 'piece',
      comment: 'Black color, for work from home',
      storePrice: 19990.00,
      pasabuyFee: 600.00,
      customerPrice: 20590.00,
      isBought: true,
      packingStatus: 'Packed',
      imageFilename: 'sony-wh1000xm5.jpg',
      storeCodeId: storeCodes[3].id, // Trinoma
      customerId: customers[4].id, // Lisa Garcia
      createdAt: generateTestDate(6), // 6 days ago
      updatedAt: generateTestDate(4) // Updated 4 days ago
    },

    // Fashion & Accessories - Medium-term entries (last 30 days)
    {
      productName: 'Nike Air Force 1 White',
      quantity: 1,
      usageUnit: 'pair',
      comment: 'Size 9 US, classic white colorway',
      storePrice: 5495.00,
      pasabuyFee: 300.00,
      customerPrice: 5795.00,
      isBought: true,
      packingStatus: 'Packed',
      imageFilename: 'nike-air-force-1.jpg',
      storeCodeId: storeCodes[2].id, // Robinsons
      customerId: customers[3].id, // Carlos Mendoza
      createdAt: generateTestDate(12), // 12 days ago
      updatedAt: generateTestDate(8) // Updated 8 days ago
    },
    {
      productName: 'Adidas Ultraboost 22',
      quantity: 1,
      usageUnit: 'pair',
      comment: 'Size 8.5 US, Core Black colorway',
      storePrice: 8500.00,
      pasabuyFee: 400.00,
      customerPrice: 8900.00,
      isBought: false,
      packingStatus: 'Not Packed',
      imageFilename: 'adidas-ultraboost-22.jpg',
      storeCodeId: storeCodes[3].id, // Trinoma
      customerId: customers[4].id, // Lisa Garcia
      createdAt: generateTestDate(18), // 18 days ago
      updatedAt: generateTestDate(15) // Updated 15 days ago
    },
    {
      productName: 'Uniqlo Heattech Crew Neck Long Sleeve T-Shirt',
      quantity: 3,
      usageUnit: 'piece',
      comment: 'Size M, different colors: black, white, gray',
      storePrice: 790.00,
      pasabuyFee: 100.00,
      customerPrice: 890.00,
      isBought: true,
      packingStatus: 'Not Packed',
      imageFilename: 'uniqlo-heattech-shirt.jpg',
      storeCodeId: storeCodes[0].id, // SM
      customerId: customers[0].id, // Maria Santos
      createdAt: generateTestDate(25), // 25 days ago
      updatedAt: generateTestDate(20) // Updated 20 days ago
    },

    // Beauty & Personal Care - Recent entries
    {
      productName: 'Fenty Beauty Pro Filt\'r Foundation',
      quantity: 2,
      usageUnit: 'bottle',
      comment: 'Shade 240 and 260, 32ml each',
      storePrice: 2200.00,
      pasabuyFee: 150.00,
      customerPrice: 2350.00,
      isBought: false,
      packingStatus: 'Not Packed',
      imageFilename: 'fenty-foundation.jpg',
      storeCodeId: storeCodes[4].id, // Greenbelt
      customerId: customers[5].id, // Miguel Rodriguez
      createdAt: generateTestDate(4), // 4 days ago
      updatedAt: generateTestDate(2) // Updated 2 days ago
    },
    {
      productName: 'The Ordinary Niacinamide 10% + Zinc 1%',
      quantity: 3,
      usageUnit: 'bottle',
      comment: 'Stock up for skincare routine',
      storePrice: 350.00,
      pasabuyFee: 50.00,
      customerPrice: 400.00,
      isBought: true,
      packingStatus: 'Not Packed',
      imageFilename: 'the-ordinary-niacinamide.jpg',
      storeCodeId: storeCodes[1].id, // Ayala
      customerId: customers[6].id, // Sofia Villanueva
      createdAt: generateTestDate(8), // 8 days ago
      updatedAt: generateTestDate(5) // Updated 5 days ago
    },

    // Food & Snacks - Varied time periods
    {
      productName: 'Japanese Kit Kat Matcha Flavor',
      quantity: 5,
      usageUnit: 'pack',
      comment: 'Limited edition flavor, gift for friends',
      storePrice: 180.00,
      pasabuyFee: 30.00,
      customerPrice: 210.00,
      isBought: true,
      packingStatus: 'Packed',
      imageFilename: 'japanese-kitkat.jpg',
      storeCodeId: storeCodes[0].id, // SM
      customerId: customers[2].id, // Anna Reyes
      createdAt: generateTestDate(14), // 14 days ago
      updatedAt: generateTestDate(10) // Updated 10 days ago
    },
    {
      productName: 'Royce\' Nama Chocolate Au Lait',
      quantity: 2,
      usageUnit: 'box',
      comment: 'Valentine\'s gift, keep refrigerated',
      storePrice: 1250.00,
      pasabuyFee: 100.00,
      customerPrice: 1350.00,
      isBought: false,
      packingStatus: 'Not Packed',
      imageFilename: 'royce-nama-chocolate.jpg',
      storeCodeId: storeCodes[5].id, // Power Plant
      customerId: customers[1].id, // John Dela Cruz
      createdAt: generateTestDate(7), // 7 days ago
      updatedAt: generateTestDate(4) // Updated 4 days ago
    },

    // Home & Lifestyle - Older entries
    {
      productName: 'Muji Ultrasonic Aroma Diffuser',
      quantity: 1,
      usageUnit: 'piece',
      comment: 'White color, for bedroom relaxation',
      storePrice: 2990.00,
      pasabuyFee: 200.00,
      customerPrice: 3190.00,
      isBought: true,
      packingStatus: 'Packed',
      imageFilename: 'muji-aroma-diffuser.jpg',
      storeCodeId: storeCodes[4].id, // Greenbelt
      customerId: customers[4].id, // Lisa Garcia
      createdAt: generateTestDate(35), // 35 days ago
      updatedAt: generateTestDate(30) // Updated 30 days ago
    },
    {
      productName: 'IKEA FRIHETEN Corner Sofa-bed',
      quantity: 1,
      usageUnit: 'piece',
      comment: 'Skiftebo dark gray, with storage',
      storePrice: 24990.00,
      pasabuyFee: 1000.00,
      customerPrice: 25990.00,
      isBought: false,
      packingStatus: 'Not Packed',
      imageFilename: 'ikea-friheten-sofa.jpg',
      storeCodeId: storeCodes[3].id, // Trinoma
      customerId: customers[5].id, // Miguel Rodriguez
      createdAt: generateTestDate(21), // 21 days ago
      updatedAt: generateTestDate(18) // Updated 18 days ago
    },

    // Additional varied entries for comprehensive testing
    {
      productName: 'Nintendo Switch OLED Model',
      quantity: 1,
      usageUnit: 'piece',
      comment: 'White color, for family gaming',
      storePrice: 19995.00,
      pasabuyFee: 600.00,
      customerPrice: 20595.00,
      isBought: true,
      packingStatus: 'Not Packed',
      imageFilename: 'nintendo-switch-oled.jpg',
      storeCodeId: storeCodes[2].id, // Robinsons
      customerId: customers[0].id, // Maria Santos
      createdAt: generateTestDate(45), // 45 days ago
      updatedAt: generateTestDate(40) // Updated 40 days ago
    },
    {
      productName: 'Levi\'s 511 Slim Jeans',
      quantity: 2,
      usageUnit: 'piece',
      comment: 'Size 32x32, dark wash and light wash',
      storePrice: 3995.00,
      pasabuyFee: 250.00,
      customerPrice: 4245.00,
      isBought: false,
      packingStatus: 'Not Packed',
      imageFilename: 'levis-511-jeans.jpg',
      storeCodeId: storeCodes[1].id, // Ayala
      customerId: customers[3].id, // Carlos Mendoza
      createdAt: generateTestDate(28), // 28 days ago
      updatedAt: generateTestDate(25) // Updated 25 days ago
    },
    {
      productName: 'Starbucks Tumbler Limited Edition',
      quantity: 1,
      usageUnit: 'piece',
      comment: 'Philippines exclusive design, 16oz',
      storePrice: 1295.00,
      pasabuyFee: 80.00,
      customerPrice: 1375.00,
      isBought: true,
      packingStatus: 'Packed',
      imageFilename: 'starbucks-tumbler.jpg',
      storeCodeId: storeCodes[0].id, // SM
      customerId: customers[6].id, // Sofia Villanueva
      createdAt: generateTestDate(16), // 16 days ago
      updatedAt: generateTestDate(12) // Updated 12 days ago
    },
    {
      productName: 'Dyson V15 Detect Absolute',
      quantity: 1,
      usageUnit: 'piece',
      comment: 'Latest model with laser detection',
      storePrice: 39995.00,
      pasabuyFee: 1200.00,
      customerPrice: 41195.00,
      isBought: false,
      packingStatus: 'Not Packed',
      imageFilename: 'dyson-v15-detect.jpg',
      storeCodeId: storeCodes[5].id, // Power Plant
      customerId: customers[2].id, // Anna Reyes
      createdAt: generateTestDate(9), // 9 days ago
      updatedAt: generateTestDate(6) // Updated 6 days ago
    },
    {
      productName: 'Casio G-Shock GA-2100',
      quantity: 1,
      usageUnit: 'piece',
      comment: 'All black "CasiOak" model',
      storePrice: 5495.00,
      pasabuyFee: 300.00,
      customerPrice: 5795.00,
      isBought: true,
      packingStatus: 'Not Packed',
      imageFilename: 'casio-gshock-ga2100.jpg',
      storeCodeId: storeCodes[3].id, // Trinoma
      customerId: customers[1].id, // John Dela Cruz
      createdAt: generateTestDate(32), // 32 days ago
      updatedAt: generateTestDate(28) // Updated 28 days ago
    }
  ]

  // Create orders with explicit date handling
  const createdOrders = []
  for (const order of orders) {
    const createdOrder = await prisma.order.create({
      data: {
        ...order,
        // Ensure dates are properly handled
        createdAt: order.createdAt || new Date(),
        updatedAt: order.updatedAt || new Date()
      }
    })
    createdOrders.push(createdOrder)
  }

  console.log('✅ Database seeded successfully!')
  console.log(`📊 Created:`)
  console.log(`   - ${storeCodes.length} store codes`)
  console.log(`   - ${customers.length} customers`)
  console.log(`   - ${orders.length} orders`)
}

main()
  .catch((e) => {
    console.error('❌ Error seeding database:', e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })

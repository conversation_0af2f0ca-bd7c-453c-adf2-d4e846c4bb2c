'use client'

import { useState } from 'react'
import { Lu<PERSON>ilt<PERSON>, Lu<PERSON> } from 'react-icons/lu'
import { Button } from '@/components/ui/button'
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog'
import { AdvancedFilterPanel } from './advanced-filter-panel'
import type { FilterConfig, SortConfig } from '@/lib/filter-types'

interface FilterModalProps {
  filters: FilterConfig
  sort: SortConfig
  onFiltersChange: (filters: FilterConfig) => void
  onSortChange: (sort: SortConfig) => void
  onClear: () => void
  hasActiveFilters: boolean
  activeFilterCount: number
  trigger?: React.ReactNode
}

export function FilterModal({
  filters,
  sort,
  onFiltersChange,
  onSortChange,
  onClear,
  hasActiveFilters,
  activeFilterCount,
  trigger
}: FilterModalProps) {
  const [isOpen, setIsOpen] = useState(false)

  const defaultTrigger = (
    <Button
      variant={hasActiveFilters ? "default" : "outline"}
      size="sm"
      className="relative"
    >
      <LuFilter className="mr-2 h-4 w-4" />
      Filters
      {hasActiveFilters && (
        <span className="absolute -top-1 -right-1 bg-primary text-primary-foreground text-xs w-5 h-5 rounded-full flex items-center justify-center">
          {activeFilterCount}
        </span>
      )}
    </Button>
  )

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        {trigger || defaultTrigger}
      </DialogTrigger>
      <DialogContent className="max-w-md w-full max-h-[90vh] p-0 gap-0 flex flex-col">
        <DialogHeader className="p-4 border-b flex-shrink-0">
          <div className="flex items-center justify-between">
            <DialogTitle className="flex items-center gap-2">
              <LuFilter className="h-4 w-4" />
              Filters & Sort
              {hasActiveFilters && (
                <span className="bg-primary text-primary-foreground text-xs px-2 py-1 rounded-full">
                  {activeFilterCount}
                </span>
              )}
            </DialogTitle>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsOpen(false)}
              className="h-8 w-8 p-0"
            >
              <LuX className="h-4 w-4" />
              <span className="sr-only">Close</span>
            </Button>
          </div>
        </DialogHeader>

        <div className="flex-1 overflow-y-auto min-h-0">
          <AdvancedFilterPanel
            filters={filters}
            sort={sort}
            onFiltersChange={onFiltersChange}
            onSortChange={onSortChange}
            onClose={() => setIsOpen(false)}
            onClear={() => {
              onClear()
              setIsOpen(false)
            }}
            className="border-0 rounded-none h-full"
            isModal={true}
          />
        </div>
      </DialogContent>
    </Dialog>
  )
}

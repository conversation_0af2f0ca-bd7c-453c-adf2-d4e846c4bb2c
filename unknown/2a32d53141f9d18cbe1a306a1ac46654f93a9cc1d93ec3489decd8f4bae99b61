import { useState, useEffect, useCallback } from 'react'
import { GroupedSearchResults } from '@/app/api/search/route'

interface UseSearchOptions {
  debounceMs?: number
  minQueryLength?: number
}

interface UseSearchReturn {
  query: string
  setQuery: (query: string) => void
  results: GroupedSearchResults | null
  isLoading: boolean
  error: string | null
  clearResults: () => void
}

export function useSearch(options: UseSearchOptions = {}): UseSearchReturn {
  const { debounceMs = 300, minQueryLength = 1 } = options
  
  const [query, setQuery] = useState('')
  const [debouncedQuery, setDebouncedQuery] = useState('')
  const [results, setResults] = useState<GroupedSearchResults | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // Debounce the query
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedQuery(query)
    }, debounceMs)

    return () => clearTimeout(timer)
  }, [query, debounceMs])

  // Perform search when debounced query changes
  useEffect(() => {
    const performSearch = async () => {
      if (debouncedQuery.length < minQueryLength) {
        setResults(null)
        setIsLoading(false)
        setError(null)
        return
      }

      setIsLoading(true)
      setError(null)

      try {
        const response = await fetch(`/api/search?q=${encodeURIComponent(debouncedQuery)}`)
        
        if (!response.ok) {
          throw new Error('Search failed')
        }

        const data: GroupedSearchResults = await response.json()
        setResults(data)
      } catch (err) {
        setError(err instanceof Error ? err.message : 'An error occurred')
        setResults(null)
      } finally {
        setIsLoading(false)
      }
    }

    performSearch()
  }, [debouncedQuery, minQueryLength])

  const clearResults = useCallback(() => {
    setQuery('')
    setDebouncedQuery('')
    setResults(null)
    setError(null)
    setIsLoading(false)
  }, [])

  return {
    query,
    setQuery,
    results,
    isLoading,
    error,
    clearResults
  }
}

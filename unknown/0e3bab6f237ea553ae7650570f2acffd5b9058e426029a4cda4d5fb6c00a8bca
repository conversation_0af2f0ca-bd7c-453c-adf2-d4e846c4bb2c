generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

model StoreCode {
  id                    Int                  @id @default(autoincrement())
  code                  String               @unique
  name                  String?
  storeType             String               @default("RETAIL")
  status                String               @default("ACTIVE")
  parentStoreId         Int?
  storeGroup            String?
  region                String?
  district              String?
  address               String?
  city                  String?
  state                 String?
  postalCode            String?
  country               String?              @default("Philippines")
  phone                 String?
  email                 String?
  website               String?
  managerName           String?
  managerPhone          String?
  managerEmail          String?
  contactPerson         String?
  operatingHours        String?
  timezone              String?              @default("Asia/Manila")
  isOpen                Boolean              @default(true)
  allowsPickup          Boolean              @default(true)
  allowsDelivery        Boolean              @default(true)
  deliveryRadius        Float?
  minimumOrder          Float?               @default(0.00)
  serviceFee            Float?               @default(0.00)
  averageProcessingTime Int?
  capacity              Int?
  priority              String               @default("NORMAL")
  totalOrders           Int                  @default(0)
  totalRevenue          Float                @default(0.00)
  averageOrderValue     Float                @default(0.00)
  notes                 String?
  internalNotes         String?
  specialInstructions   String?
  externalStoreId       String?
  apiEndpoint           String?
  apiKey                String?
  createdAt             DateTime             @default(now())
  updatedAt             DateTime             @default(now()) @updatedAt
  orders                Order[]
  parentStore           StoreCode?           @relation("StoreHierarchy", fields: [parentStoreId], references: [id])
  childStores           StoreCode[]          @relation("StoreHierarchy")
  configurations        StoreConfiguration[]
  pricing               StorePricing?

  @@index([code])
  @@index([storeType])
  @@index([status])
  @@index([parentStoreId])
  @@index([storeGroup])
  @@index([region])
  @@index([city])
  @@index([isOpen])
  @@index([priority])
  @@index([totalOrders])
  @@index([totalRevenue])
  @@map("store_codes")
}

model StoreConfiguration {
  id          Int       @id @default(autoincrement())
  storeCodeId Int
  configKey   String
  configValue String
  dataType    String    @default("STRING")
  description String?
  category    String?
  isActive    Boolean   @default(true)
  isSystem    Boolean   @default(false)
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @default(now()) @updatedAt
  storeCode   StoreCode @relation(fields: [storeCodeId], references: [id], onDelete: Cascade)

  @@unique([storeCodeId, configKey])
  @@index([storeCodeId])
  @@index([configKey])
  @@index([category])
  @@index([isActive])
  @@map("store_configurations")
}

model Customer {
  id                      Int                     @id @default(autoincrement())
  name                    String                  @unique
  customerNumber          String?                 @unique
  customerType            String                  @default("INDIVIDUAL")
  status                  String                  @default("ACTIVE")
  email                   String?
  phone                   String?
  alternatePhone          String?
  website                 String?
  address                 String?
  city                    String?
  state                   String?
  postalCode              String?
  country                 String?                 @default("Philippines")
  businessName            String?
  taxId                   String?
  businessType            String?
  preferredDeliveryMethod String?
  preferredPaymentMethod  String?
  creditLimit             Float?                  @default(0.00)
  paymentTerms            Int?                    @default(30)
  discountRate            Float?                  @default(0.00)
  segment                 String                  @default("REGULAR")
  loyaltyTier             String                  @default("BRONZE")
  loyaltyPoints           Int                     @default(0)
  assignedSalesRep        String?
  accountManager          String?
  referredBy              String?
  firstOrderDate          DateTime?
  lastOrderDate           DateTime?
  lastContactDate         DateTime?
  totalOrders             Int                     @default(0)
  totalSpent              Float                   @default(0.00)
  averageOrderValue       Float                   @default(0.00)
  notes                   String?
  internalNotes           String?
  createdAt               DateTime                @default(now())
  updatedAt               DateTime                @default(now()) @updatedAt
  addresses               CustomerAddress[]
  communications          CustomerCommunication[]
  invoices                Invoice[]
  orders                  Order[]

  @@index([customerNumber])
  @@index([customerType])
  @@index([status])
  @@index([email])
  @@index([phone])
  @@index([segment])
  @@index([loyaltyTier])
  @@index([assignedSalesRep])
  @@index([firstOrderDate])
  @@index([lastOrderDate])
  @@index([totalSpent])
  @@index([createdAt])
  @@map("customers")
}

model CustomerCommunication {
  id                 Int       @id @default(autoincrement())
  customerId         Int
  type               String
  direction          String
  subject            String?
  content            String
  channel            String?
  contactedBy        String?
  outcome            String?
  followUpDate       DateTime?
  isFollowUpRequired Boolean   @default(false)
  externalId         String?
  attachments        String?
  createdAt          DateTime  @default(now())
  updatedAt          DateTime  @default(now()) @updatedAt
  customer           Customer  @relation(fields: [customerId], references: [id], onDelete: Cascade)

  @@index([customerId])
  @@index([type])
  @@index([direction])
  @@index([createdAt])
  @@index([followUpDate])
  @@map("customer_communications")
}

model CustomerAddress {
  id                   Int      @id @default(autoincrement())
  customerId           Int
  type                 String   @default("BILLING")
  label                String?
  addressLine1         String
  addressLine2         String?
  city                 String
  state                String?
  postalCode           String?
  country              String   @default("Philippines")
  isDefault            Boolean  @default(false)
  isActive             Boolean  @default(true)
  deliveryInstructions String?
  accessCode           String?
  contactPerson        String?
  contactPhone         String?
  createdAt            DateTime @default(now())
  updatedAt            DateTime @default(now()) @updatedAt
  customer             Customer @relation(fields: [customerId], references: [id], onDelete: Cascade)

  @@index([customerId])
  @@index([type])
  @@index([isDefault])
  @@index([isActive])
  @@map("customer_addresses")
}

model Order {
  id                  Int                  @id @default(autoincrement())
  productName         String
  quantity            Int                  @default(1)
  usageUnit           String?
  comment             String?
  imageFilename       String?
  storePrice          Float                @default(0.00)
  pasabuyFee          Float                @default(0.00)
  customerPrice       Float                @default(0.00)
  isBought            Boolean              @default(false)
  packingStatus       String               @default("Not Packed")
  orderNumber         String?              @unique
  priority            String               @default("NORMAL")
  category            String?
  brand               String?
  model               String?
  sku                 String?
  barcode             String?
  originalPrice       Float?               @default(0.00)
  discountAmount      Float?               @default(0.00)
  discountType        String?
  discountReason      String?
  taxAmount           Float?               @default(0.00)
  taxRate             Float?               @default(0.00)
  parentOrderId       Int?
  orderGroupId        String?
  source              String               @default("MANUAL")
  urgency             String               @default("NORMAL")
  specialInstructions String?
  internalNotes       String?
  customerNotes       String?
  estimatedDelivery   DateTime?
  requestedDelivery   DateTime?
  completedAt         DateTime?
  cancelledAt         DateTime?
  cancellationReason  String?
  deliveryStatus      String?              @default("Not Delivered")
  deliveryDate        DateTime?
  deliveryMethod      String?
  trackingNumber      String?
  deliveryNotes       String?
  storeCodeId         Int?
  customerId          Int?
  createdAt           DateTime             @default(now())
  updatedAt           DateTime             @default(now()) @updatedAt
  invoiceItems        InvoiceItem[]
  attachments         OrderAttachment[]
  metrics             OrderMetrics?
  notes               OrderNote[]
  statusHistory       OrderStatusHistory[]
  tags                OrderTag[]
  timeline            OrderTimeline[]
  customer            Customer?            @relation(fields: [customerId], references: [id])
  storeCode           StoreCode?           @relation(fields: [storeCodeId], references: [id])
  parentOrder         Order?               @relation("OrderBundle", fields: [parentOrderId], references: [id])
  childOrders         Order[]              @relation("OrderBundle")

  @@index([productName])
  @@index([isBought])
  @@index([packingStatus])
  @@index([storeCodeId])
  @@index([customerId])
  @@index([createdAt])
  @@index([updatedAt])
  @@index([storePrice])
  @@index([pasabuyFee])
  @@index([customerPrice])
  @@index([quantity])
  @@index([usageUnit])
  @@index([comment])
  @@index([deliveryStatus])
  @@index([deliveryDate])
  @@index([deliveryMethod])
  @@index([trackingNumber])
  @@index([orderNumber])
  @@index([priority])
  @@index([category])
  @@index([brand])
  @@index([sku])
  @@index([barcode])
  @@index([source])
  @@index([urgency])
  @@index([parentOrderId])
  @@index([orderGroupId])
  @@index([estimatedDelivery])
  @@index([requestedDelivery])
  @@index([completedAt])
  @@index([cancelledAt])
  @@index([isBought, packingStatus])
  @@index([storeCodeId, isBought])
  @@index([customerId, isBought])
  @@index([customerId, packingStatus])
  @@index([createdAt, isBought])
  @@index([storePrice, customerPrice])
  @@index([isBought, packingStatus, deliveryStatus])
  @@index([customerId, deliveryStatus])
  @@index([deliveryDate, deliveryStatus])
  @@index([packingStatus, deliveryStatus])
  @@index([priority, urgency])
  @@index([category, brand])
  @@index([source, createdAt])
  @@index([parentOrderId, orderGroupId])
  @@map("orders")
}

model OrderTag {
  id        Int      @id @default(autoincrement())
  orderId   Int
  tag       String
  color     String?
  createdAt DateTime @default(now())
  order     Order    @relation(fields: [orderId], references: [id], onDelete: Cascade)

  @@unique([orderId, tag])
  @@index([orderId])
  @@index([tag])
  @@map("order_tags")
}

model OrderAttachment {
  id           Int      @id @default(autoincrement())
  orderId      Int
  filename     String
  originalName String
  mimeType     String
  fileSize     Int
  description  String?
  uploadedBy   String?
  createdAt    DateTime @default(now())
  order        Order    @relation(fields: [orderId], references: [id], onDelete: Cascade)

  @@index([orderId])
  @@index([filename])
  @@index([mimeType])
  @@map("order_attachments")
}

model OrderNote {
  id         Int      @id @default(autoincrement())
  orderId    Int
  content    String
  noteType   String   @default("GENERAL")
  isInternal Boolean  @default(false)
  authorId   String?
  authorName String?
  createdAt  DateTime @default(now())
  updatedAt  DateTime @default(now()) @updatedAt
  order      Order    @relation(fields: [orderId], references: [id], onDelete: Cascade)

  @@index([orderId])
  @@index([noteType])
  @@index([isInternal])
  @@index([createdAt])
  @@map("order_notes")
}

model Invoice {
  id                 Int                    @id @default(autoincrement())
  invoiceNumber      String                 @unique
  customerId         Int
  status             String                 @default("DRAFT")
  invoiceType        String                 @default("STANDARD")
  priority           String                 @default("NORMAL")
  subtotal           Float                  @default(0.00)
  discountAmount     Float                  @default(0.00)
  discountPercentage Float                  @default(0.00)
  taxAmount          Float                  @default(0.00)
  taxRate            Float                  @default(0.00)
  shippingCost       Float                  @default(0.00)
  total              Float                  @default(0.00)
  paymentTerms       Int?                   @default(30)
  paymentMethod      String?
  currency           String                 @default("PHP")
  exchangeRate       Float?                 @default(1.00)
  issueDate          DateTime               @default(now())
  dueDate            DateTime?
  sentDate           DateTime?
  paidDate           DateTime?
  overdueDate        DateTime?
  approvalStatus     String                 @default("PENDING")
  approvedBy         String?
  approvedAt         DateTime?
  rejectedBy         String?
  rejectedAt         DateTime?
  rejectionReason    String?
  billingAddress     String?
  shippingAddress    String?
  customerPO         String?
  templateId         String?
  logoUrl            String?
  headerText         String?
  footerText         String?
  emailsSent         Int                    @default(0)
  lastEmailSent      DateTime?
  viewCount          Int                    @default(0)
  lastViewed         DateTime?
  notes              String?
  internalNotes      String?
  customerNotes      String?
  externalInvoiceId  String?
  syncStatus         String                 @default("PENDING")
  lastSyncAt         DateTime?
  syncError          String?
  createdAt          DateTime               @default(now())
  updatedAt          DateTime               @default(now()) @updatedAt
  attachments        InvoiceAttachment[]
  communications     InvoiceCommunication[]
  invoiceItems       InvoiceItem[]
  payments           InvoicePayment[]
  customer           Customer               @relation(fields: [customerId], references: [id], onDelete: Cascade)

  @@index([invoiceNumber])
  @@index([customerId])
  @@index([status])
  @@index([invoiceType])
  @@index([priority])
  @@index([approvalStatus])
  @@index([issueDate])
  @@index([dueDate])
  @@index([paidDate])
  @@index([sentDate])
  @@index([overdueDate])
  @@index([syncStatus])
  @@index([createdAt])
  @@index([customerId, status])
  @@index([status, dueDate])
  @@index([createdAt, status])
  @@index([approvalStatus, status])
  @@index([invoiceType, status])
  @@index([priority, dueDate])
  @@map("invoices")
}

model InvoiceItem {
  id                 Int      @id @default(autoincrement())
  invoiceId          Int
  orderId            Int
  description        String?
  quantity           Int      @default(1)
  unitPrice          Float    @default(0.00)
  originalUnitPrice  Float?   @default(0.00)
  discountAmount     Float    @default(0.00)
  discountPercentage Float    @default(0.00)
  taxAmount          Float    @default(0.00)
  taxRate            Float    @default(0.00)
  totalPrice         Float    @default(0.00)
  itemType           String   @default("PRODUCT")
  category           String?
  sku                String?
  notes              String?
  createdAt          DateTime @default(now())
  updatedAt          DateTime @default(now()) @updatedAt
  order              Order    @relation(fields: [orderId], references: [id], onDelete: Cascade)
  invoice            Invoice  @relation(fields: [invoiceId], references: [id], onDelete: Cascade)

  @@index([invoiceId])
  @@index([orderId])
  @@index([itemType])
  @@index([category])
  @@index([invoiceId, orderId])
  @@index([invoiceId, itemType])
  @@map("invoice_items")
}

model InvoicePayment {
  id                Int      @id @default(autoincrement())
  invoiceId         Int
  paymentNumber     String   @unique
  amount            Float
  paymentMethod     String
  paymentDate       DateTime @default(now())
  reference         String?
  notes             String?
  externalPaymentId String?
  processorFee      Float?   @default(0.00)
  createdAt         DateTime @default(now())
  updatedAt         DateTime @default(now()) @updatedAt
  invoice           Invoice  @relation(fields: [invoiceId], references: [id], onDelete: Cascade)

  @@index([invoiceId])
  @@index([paymentNumber])
  @@index([paymentDate])
  @@index([paymentMethod])
  @@map("invoice_payments")
}

model InvoiceCommunication {
  id        Int      @id @default(autoincrement())
  invoiceId Int
  type      String
  direction String
  subject   String?
  content   String
  channel   String?
  sentBy    String?
  sentTo    String?
  createdAt DateTime @default(now())
  invoice   Invoice  @relation(fields: [invoiceId], references: [id], onDelete: Cascade)

  @@index([invoiceId])
  @@index([type])
  @@index([direction])
  @@index([createdAt])
  @@map("invoice_communications")
}

model InvoiceAttachment {
  id           Int      @id @default(autoincrement())
  invoiceId    Int
  filename     String
  originalName String
  mimeType     String
  fileSize     Int
  description  String?
  uploadedBy   String?
  createdAt    DateTime @default(now())
  invoice      Invoice  @relation(fields: [invoiceId], references: [id], onDelete: Cascade)

  @@index([invoiceId])
  @@index([filename])
  @@index([mimeType])
  @@map("invoice_attachments")
}

model StorePricing {
  id          Int                @id @default(autoincrement())
  storeCodeId Int                @unique
  name        String
  serviceFee  Float              @default(0.00)
  isActive    Boolean            @default(true)
  createdAt   DateTime           @default(now())
  updatedAt   DateTime           @default(now()) @updatedAt
  storeCode   StoreCode          @relation(fields: [storeCodeId], references: [id], onDelete: Cascade)
  pricingTiers StorePricingTier[]

  @@index([storeCodeId])
  @@index([isActive])
  @@map("store_pricing")
}

model StorePricingTier {
  id             Int          @id @default(autoincrement())
  storePricingId Int
  minPrice       Float        @default(0.00)
  maxPrice       Float?       // null means "and above"
  markupType     String       @default("FIXED_AMOUNT") // PERCENTAGE, FIXED_AMOUNT
  markupValue    Float        @default(0.00)
  pasabuyFee     Float        @default(0.00) // Pasabuy fee for this tier
  sortOrder      Int          @default(0)
  createdAt      DateTime     @default(now())
  updatedAt      DateTime     @default(now()) @updatedAt
  storePricing   StorePricing @relation(fields: [storePricingId], references: [id], onDelete: Cascade)

  @@index([storePricingId])
  @@index([minPrice])
  @@index([maxPrice])
  @@index([sortOrder])
  @@map("store_pricing_tiers")
}

model DefaultPricing {
  id          Int                   @id @default(autoincrement())
  serviceFee  Float                 @default(0.00)
  isActive    Boolean               @default(true)
  createdAt   DateTime              @default(now())
  updatedAt   DateTime              @default(now()) @updatedAt
  pricingTiers DefaultPricingTier[]

  @@map("default_pricing")
}

model DefaultPricingTier {
  id               Int            @id @default(autoincrement())
  defaultPricingId Int
  minPrice         Float          @default(0.00)
  maxPrice         Float?         // null means "and above"
  markupType       String         @default("FIXED_AMOUNT") // PERCENTAGE, FIXED_AMOUNT
  markupValue      Float          @default(0.00)
  pasabuyFee       Float          @default(0.00) // Pasabuy fee for this tier
  sortOrder        Int            @default(0)
  createdAt        DateTime       @default(now())
  updatedAt        DateTime       @default(now()) @updatedAt
  defaultPricing   DefaultPricing @relation(fields: [defaultPricingId], references: [id], onDelete: Cascade)

  @@index([defaultPricingId])
  @@index([minPrice])
  @@index([maxPrice])
  @@index([sortOrder])
  @@map("default_pricing_tiers")
}

model OrderStatusHistory {
  id           Int      @id @default(autoincrement())
  orderId      Int
  fromStatus   String?
  toStatus     String
  statusType   String
  changedBy    String?
  changeReason String?
  metadata     Json?
  createdAt    DateTime @default(now())
  order        Order    @relation(fields: [orderId], references: [id], onDelete: Cascade)

  @@index([orderId])
  @@index([orderId, createdAt])
  @@index([statusType])
  @@index([createdAt])
  @@index([changedBy])
  @@map("order_status_history")
}

model OrderTimeline {
  id          Int      @id @default(autoincrement())
  orderId     Int
  eventType   String
  eventData   Json?
  duration    Int?
  performedBy String?
  notes       String?
  createdAt   DateTime @default(now())
  order       Order    @relation(fields: [orderId], references: [id], onDelete: Cascade)

  @@index([orderId])
  @@index([orderId, createdAt])
  @@index([eventType])
  @@index([createdAt])
  @@map("order_timeline")
}

model OrderMetrics {
  id                  Int      @id @default(autoincrement())
  orderId             Int      @unique
  timeToFirstPurchase Int?
  timeToPacking       Int?
  timeToInvoicing     Int?
  timeToDelivery      Int?
  totalProcessingTime Int?
  statusChangeCount   Int      @default(0)
  purchaseAttempts    Int      @default(0)
  packingAttempts     Int      @default(0)
  isRushed            Boolean  @default(false)
  isDelayed           Boolean  @default(false)
  hasIssues           Boolean  @default(false)
  efficiencyScore     Float?
  lastCalculated      DateTime @default(now())
  createdAt           DateTime @default(now())
  updatedAt           DateTime @default(now()) @updatedAt
  order               Order    @relation(fields: [orderId], references: [id], onDelete: Cascade)

  @@index([timeToFirstPurchase])
  @@index([timeToPacking])
  @@index([timeToInvoicing])
  @@index([totalProcessingTime])
  @@index([efficiencyScore])
  @@index([isRushed])
  @@index([isDelayed])
  @@index([hasIssues])
  @@map("order_metrics")
}

'use client'

import { Button } from '@/components/ui/button'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { LuCheck, LuPackage, LuTriangleAlert } from 'react-icons/lu'

export interface BulkConfirmationDialogProps {
  isOpen: boolean
  onClose: () => void
  onConfirm: () => void
  action: 'bought' | 'packed'
  selectedCount: number
  isLoading?: boolean
  mixedStatuses?: {
    alreadyBought?: number
    alreadyPacked?: number
  }
}

export function BulkConfirmationDialog({
  isOpen,
  onClose,
  onConfirm,
  action,
  selectedCount,
  isLoading = false,
  mixedStatuses
}: BulkConfirmationDialogProps) {
  const actionText = action === 'bought' ? 'bought' : 'packed'
  const actionIcon = action === 'bought' ? LuCheck : LuPackage
  const ActionIcon = actionIcon

  const hasWarnings = mixedStatuses && (
    (mixedStatuses.alreadyBought && mixedStatuses.alreadyBought > 0) ||
    (mixedStatuses.alreadyPacked && mixedStatuses.alreadyPacked > 0)
  )

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <ActionIcon className="h-5 w-5" />
            Confirm Bulk Action
          </DialogTitle>
          <DialogDescription>
            Are you sure you want to mark {selectedCount} item{selectedCount !== 1 ? 's' : ''} as {actionText}?
          </DialogDescription>
        </DialogHeader>

        {hasWarnings && (
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
            <div className="flex items-start gap-2">
              <LuTriangleAlert className="h-4 w-4 text-yellow-600 mt-0.5 flex-shrink-0" />
              <div className="text-sm">
                <p className="font-medium text-yellow-800 mb-1">Warning:</p>
                <ul className="text-yellow-700 space-y-1">
                  {mixedStatuses?.alreadyBought && mixedStatuses.alreadyBought > 0 && (
                    <li>
                      • {mixedStatuses.alreadyBought} item{mixedStatuses.alreadyBought !== 1 ? 's are' : ' is'} already marked as bought
                    </li>
                  )}
                  {mixedStatuses?.alreadyPacked && mixedStatuses.alreadyPacked > 0 && (
                    <li>
                      • {mixedStatuses.alreadyPacked} item{mixedStatuses.alreadyPacked !== 1 ? 's are' : ' is'} already marked as packed
                    </li>
                  )}
                </ul>
                <p className="mt-2 text-xs">
                  These items will be skipped. Only applicable items will be updated.
                </p>
              </div>
            </div>
          </div>
        )}

        <DialogFooter>
          <Button
            variant="outline"
            onClick={onClose}
            disabled={isLoading}
          >
            Cancel
          </Button>
          <Button
            onClick={onConfirm}
            disabled={isLoading}
            className={action === 'bought' ? 'bg-green-600 hover:bg-green-700' : 'bg-blue-600 hover:bg-blue-700'}
          >
            {isLoading ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                Processing...
              </>
            ) : (
              <>
                <ActionIcon className="h-4 w-4 mr-2" />
                Mark as {actionText.charAt(0).toUpperCase() + actionText.slice(1)}
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}

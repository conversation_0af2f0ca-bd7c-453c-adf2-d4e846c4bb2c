import { prisma } from '@/lib/db'
import { NextRequest, NextResponse } from 'next/server'
import { QueryBuilder } from '@/lib/query-builder'
import {
  parseFiltersFromRequest,
  buildPaginationParams,
  calculatePaginationMeta
} from '@/lib/filter-utils'
import {
  FilterValidationError,
  QueryBuilderError,
  FilterResponse
} from '@/lib/filter-types'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)

    // Check if this is a legacy request (for backward compatibility)
    const isLegacyRequest = searchParams.has('storeCodeId') ||
                           searchParams.has('customerId') ||
                           (searchParams.has('isBought') && !searchParams.has('isBought.operator') && !searchParams.has('isBought.value')) ||
                           (searchParams.has('packingStatus') && !searchParams.has('packingStatus.operator') && !searchParams.has('packingStatus.value')) ||
                           (searchParams.has('search') && !searchParams.has('productName.value'))

    if (isLegacyRequest) {
      return handleLegacyRequest(request)
    }

    // Parse advanced filters, sorting, and pagination
    const { filters, sort, pagination } = parseFiltersFromRequest(request)

    // Build Prisma query using QueryBuilder
    const queryConfig = QueryBuilder.buildOrderQuery(filters, sort, {
      storeCode: true,
      customer: true
    })

    // Build pagination parameters
    const paginationParams = buildPaginationParams(pagination)

    // Execute query with pagination
    const [orders, totalCount] = await Promise.all([
      prisma.order.findMany({
        ...queryConfig,
        ...paginationParams
      }),
      prisma.order.count({
        where: queryConfig.where
      })
    ])

    // Calculate pagination metadata
    const paginationMeta = calculatePaginationMeta(totalCount, pagination)

    // Return structured response
    const response: FilterResponse<typeof orders[0]> = {
      data: orders,
      pagination: paginationMeta,
      appliedFilters: filters,
      appliedSort: sort
    }

    return NextResponse.json(response)

  } catch (error) {
    console.error('Error fetching orders:', error)

    if (error instanceof FilterValidationError) {
      return NextResponse.json(
        {
          error: 'Invalid filter parameters',
          details: error.message,
          field: error.field
        },
        { status: 400 }
      )
    }

    if (error instanceof QueryBuilderError) {
      return NextResponse.json(
        {
          error: 'Query building error',
          details: error.message
        },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { error: 'Error fetching orders' },
      { status: 500 }
    )
  }
}

/**
 * Handle legacy API requests for backward compatibility
 */
async function handleLegacyRequest(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const storeCodeId = searchParams.get('storeCodeId')
    const customerId = searchParams.get('customerId')
    const isBought = searchParams.get('isBought')
    const packingStatus = searchParams.get('packingStatus')
    const search = searchParams.get('search')

    // Build where clause (legacy logic)
    const where: Record<string, unknown> = {}

    if (storeCodeId) {
      where.storeCodeId = parseInt(storeCodeId)
    }

    if (customerId) {
      where.customerId = parseInt(customerId)
    }

    if (isBought !== null && isBought !== undefined) {
      where.isBought = isBought === 'true'
    }

    if (packingStatus) {
      where.packingStatus = packingStatus
    }

    if (search) {
      // Convert to lowercase for case-insensitive search in SQLite
      const lowerSearch = search.toLowerCase()
      where.OR = [
        {
          productName: {
            contains: lowerSearch
          }
        },
        {
          customer: {
            name: {
              contains: lowerSearch
            }
          }
        },
        {
          storeCode: {
            code: {
              contains: lowerSearch
            }
          }
        }
      ]
    }

    const orders = await prisma.order.findMany({
      where,
      include: {
        storeCode: true,
        customer: true
      },
      orderBy: {
        createdAt: 'desc'
      }
    })

    return NextResponse.json(orders)
  } catch (error) {
    console.error('Error in legacy request handler:', error)
    return NextResponse.json(
      { error: 'Error fetching orders' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const {
      productName,
      quantity = 1,
      usageUnit,
      comment,
      storePrice = 0,
      pasabuyFee = 0,
      customerPrice,
      storeCodeId,
      customerId,
      isBought = false,
      packingStatus = 'Not Packed',
      imageFilename
    } = body

    if (!productName) {
      return NextResponse.json(
        { error: 'Product name is required' },
        { status: 400 }
      )
    }

    // Calculate customer price if not provided
    const calculatedCustomerPrice = customerPrice ?? (parseFloat(storePrice) + parseFloat(pasabuyFee))

    const order = await prisma.order.create({
      data: {
        productName: productName.trim(),
        quantity: parseInt(quantity),
        usageUnit: usageUnit?.trim() || null,
        comment: comment?.trim() || null,
        storePrice: parseFloat(storePrice),
        pasabuyFee: parseFloat(pasabuyFee),
        customerPrice: calculatedCustomerPrice,
        storeCodeId: storeCodeId ? parseInt(storeCodeId) : null,
        customerId: customerId ? parseInt(customerId) : null,
        isBought,
        packingStatus,
        imageFilename: imageFilename || null
      },
      include: {
        storeCode: true,
        customer: true
      }
    })

    return NextResponse.json(order, { status: 201 })
  } catch (error: unknown) {
    console.error('Error creating order:', error)
    return NextResponse.json(
      { error: 'Error creating order' },
      { status: 500 }
    )
  }
}

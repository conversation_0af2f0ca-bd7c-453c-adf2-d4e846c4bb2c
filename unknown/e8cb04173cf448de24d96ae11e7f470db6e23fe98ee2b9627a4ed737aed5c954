'use client'

import { But<PERSON> } from '@/components/ui/button'
import { <PERSON><PERSON>heckCheck } from 'react-icons/lu'
import { BaseOrderCard } from './base-order-card'

interface Item {
  id: number
  productName: string
  quantity: number
  usageUnit?: string | null
  comment?: string | null
  storePrice: number
  pasabuyFee: number
  resellerPrice: number
  isBought: boolean
  packingStatus: string
  imageFilename?: string | null
  storeCode?: {
    id: number
    code: string
    name: string | null
  } | null
  reseller?: {
    id: number
    name: string
  }
}

interface PackingItemCardProps {
  item: Item
  isSelected: boolean
  isBulkMode: boolean
  onToggleSelection: (itemId: number) => void
  onLongPress: (itemId: number) => void
  onMarkAsPacked: (itemId: number) => void
  onCardClick: (itemId: number, event: React.MouseEvent) => void
}

export function PackingItemCard({
  item,
  isSelected,
  isBulkMode,
  onToggleSelection,
  onLongPress,
  onMarkAsPacked,
  onCardClick,
}: PackingItemCardProps) {
  const handlePackedClick = (e: React.MouseEvent) => {
    e.preventDefault()
    e.stopPropagation()
    onMarkAsPacked(item.id)
  }

  const actions = (
    <div className="flex gap-1.5 flex-shrink-0">
      <Button
        onClick={handlePackedClick}
        className="bg-blue-600 hover:bg-blue-700 active:bg-blue-800 h-7 px-2 text-xs font-medium transition-colors min-h-[44px] sm:min-h-[28px]"
        title="Mark as packed"
        aria-label="Mark as packed"
      >
        <LuCheckCheck className="h-3 w-3 mr-1" />
        <span className="hidden sm:inline">Pack</span>
      </Button>
    </div>
  )

  return (
    <BaseOrderCard
      item={item}
      variant="packing-compact"
      isSelected={isSelected}
      isBulkMode={isBulkMode}
      onToggleSelection={onToggleSelection}
      onLongPress={onLongPress}
      onCardClick={onCardClick}
      actions={actions}
    />
  )
}

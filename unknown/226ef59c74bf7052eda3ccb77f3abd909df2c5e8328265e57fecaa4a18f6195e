const fs = require('fs')
const path = require('path')
const https = require('https')

// Create uploads directory if it doesn't exist
const uploadsDir = path.join(process.cwd(), 'public', 'uploads', 'orders')
if (!fs.existsSync(uploadsDir)) {
  fs.mkdirSync(uploadsDir, { recursive: true })
}

// Sample images using placehold.co (reliable and feature-rich)
const sampleImages = [
  {
    filename: 'iphone-15-pro-max.jpg',
    url: 'https://placehold.co/400x400/1a1a1a/ffffff/png?text=iPhone%2015%20Pro%20Max',
    description: 'iPhone 15 Pro Max'
  },
  {
    filename: 'samsung-s24-ultra.jpg',
    url: 'https://placehold.co/400x400/2563eb/ffffff/png?text=Samsung%20S24%20Ultra',
    description: 'Samsung Galaxy S24 Ultra'
  },
  {
    filename: 'airpods-pro-2.jpg',
    url: 'https://placehold.co/400x400/f3f4f6/1f2937/png?text=AirPods%20Pro%202',
    description: 'Apple AirPods Pro 2nd Gen'
  },
  {
    filename: 'nike-air-force-1.jpg',
    url: 'https://placehold.co/400x400/dc2626/ffffff/png?text=Nike%20Air%20Force%201',
    description: 'Nike Air Force 1'
  },
  {
    filename: 'adidas-ultraboost-22.jpg',
    url: 'https://placehold.co/400x400/059669/ffffff/png?text=Adidas%20Ultraboost',
    description: 'Adidas Ultraboost 22'
  },
  {
    filename: 'uniqlo-heattech-shirt.jpg',
    url: 'https://placehold.co/400x400/7c3aed/ffffff/png?text=Uniqlo%20Heattech',
    description: 'Uniqlo Heattech Shirt'
  },
  {
    filename: 'fenty-foundation.jpg',
    url: 'https://placehold.co/400x400/ec4899/ffffff/png?text=Fenty%20Foundation',
    description: 'Fenty Beauty Foundation'
  },
  {
    filename: 'the-ordinary-niacinamide.jpg',
    url: 'https://placehold.co/400x400/0891b2/ffffff/png?text=The%20Ordinary',
    description: 'The Ordinary Niacinamide'
  },
  {
    filename: 'japanese-kitkat.jpg',
    url: 'https://placehold.co/400x400/ea580c/ffffff/png?text=Japanese%20KitKat',
    description: 'Japanese Kit Kat'
  },
  {
    filename: 'royce-nama-chocolate.jpg',
    url: 'https://placehold.co/400x400/92400e/ffffff/png?text=Royce%20Chocolate',
    description: 'Royce Nama Chocolate'
  },
  {
    filename: 'muji-aroma-diffuser.jpg',
    url: 'https://placehold.co/400x400/64748b/ffffff/png?text=Muji%20Diffuser',
    description: 'Muji Aroma Diffuser'
  },
  {
    filename: 'ikea-friheten-sofa.jpg',
    url: 'https://placehold.co/400x400/0369a1/ffffff/png?text=IKEA%20Sofa',
    description: 'IKEA FRIHETEN Sofa'
  },
  // New product images for expanded mock data
  {
    filename: 'macbook-air-m3.jpg',
    url: 'https://placehold.co/400x400/1f2937/ffffff/png?text=MacBook%20Air%20M3',
    description: 'MacBook Air M3 15-inch'
  },
  {
    filename: 'sony-wh1000xm5.jpg',
    url: 'https://placehold.co/400x400/000000/ffffff/png?text=Sony%20WH-1000XM5',
    description: 'Sony WH-1000XM5 Headphones'
  },
  {
    filename: 'levis-501-jeans.jpg',
    url: 'https://placehold.co/400x400/1e40af/ffffff/png?text=Levi%27s%20501',
    description: 'Levi\'s 501 Original Jeans'
  },
  {
    filename: 'champion-hoodie.jpg',
    url: 'https://placehold.co/400x400/7c2d12/ffffff/png?text=Champion%20Hoodie',
    description: 'Champion Reverse Weave Hoodie'
  },
  {
    filename: 'rare-beauty-blush.jpg',
    url: 'https://placehold.co/400x400/ec4899/ffffff/png?text=Rare%20Beauty%20Blush',
    description: 'Rare Beauty Soft Pinch Liquid Blush'
  },
  {
    filename: 'cerave-cleanser.jpg',
    url: 'https://placehold.co/400x400/06b6d4/ffffff/png?text=CeraVe%20Cleanser',
    description: 'CeraVe Hydrating Cleanser'
  },
  {
    filename: 'matcha-powder.jpg',
    url: 'https://placehold.co/400x400/16a34a/ffffff/png?text=Matcha%20Powder',
    description: 'Matcha Green Tea Powder - Premium Grade'
  },
  {
    filename: 'honey-butter-chips.jpg',
    url: 'https://placehold.co/400x400/f59e0b/ffffff/png?text=Honey%20Butter%20Chips',
    description: 'Korean Honey Butter Chips'
  },
  {
    filename: 'atomic-habits-book.jpg',
    url: 'https://placehold.co/400x400/374151/ffffff/png?text=Atomic%20Habits',
    description: 'Atomic Habits by James Clear'
  },
  {
    filename: 'nintendo-switch-oled.jpg',
    url: 'https://placehold.co/400x400/dc2626/ffffff/png?text=Nintendo%20Switch%20OLED',
    description: 'Nintendo Switch OLED Console'
  },
  {
    filename: 'yoga-mat.jpg',
    url: 'https://placehold.co/400x400/8b5cf6/ffffff/png?text=Yoga%20Mat',
    description: 'Yoga Mat - Premium Non-Slip'
  },
  {
    filename: 'resistance-bands.jpg',
    url: 'https://placehold.co/400x400/059669/ffffff/png?text=Resistance%20Bands',
    description: 'Resistance Bands Set'
  },
  {
    filename: 'ipad-air-5.jpg',
    url: 'https://placehold.co/400x400/6b7280/ffffff/png?text=iPad%20Air%205',
    description: 'iPad Air 5th Generation'
  }
]

function downloadImage(url, filename) {
  return new Promise((resolve, reject) => {
    const filePath = path.join(uploadsDir, filename)

    // Skip if file already exists
    if (fs.existsSync(filePath)) {
      console.log(`✅ ${filename} already exists, skipping...`)
      resolve()
      return
    }

    const file = fs.createWriteStream(filePath)

    https.get(url, (response) => {
      // Handle redirects
      if (response.statusCode === 301 || response.statusCode === 302) {
        downloadImage(response.headers.location, filename).then(resolve).catch(reject)
        return
      }

      if (response.statusCode !== 200) {
        reject(new Error(`Failed to download ${filename}: ${response.statusCode}`))
        return
      }

      response.pipe(file)

      file.on('finish', () => {
        file.close()
        console.log(`✅ Downloaded: ${filename}`)
        resolve()
      })

      file.on('error', (err) => {
        fs.unlink(filePath, () => {}) // Delete the file on error
        reject(err)
      })
    }).on('error', (err) => {
      reject(err)
    })
  })
}

async function downloadAllImages() {
  console.log('📸 Starting to download sample images from placehold.co...')
  console.log(`📁 Saving to: ${uploadsDir}`)

  try {
    for (const image of sampleImages) {
      await downloadImage(image.url, image.filename)
      // Add a small delay to be respectful to the service
      await new Promise(resolve => setTimeout(resolve, 300))
    }

    console.log('\n🎉 All sample images downloaded successfully!')
    console.log(`📊 Total images: ${sampleImages.length}`)
    console.log('🌐 Images generated using placehold.co service')
  } catch (error) {
    console.error('❌ Error downloading images:', error)
    process.exit(1)
  }
}

// Run the download
downloadAllImages()

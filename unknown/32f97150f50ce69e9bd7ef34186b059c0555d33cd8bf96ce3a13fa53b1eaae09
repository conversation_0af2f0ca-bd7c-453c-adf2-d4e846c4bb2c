import { prisma } from '@/lib/db'
import { NextRequest, NextResponse } from 'next/server'

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    const storeCodeId = parseInt(id)

    if (isNaN(storeCodeId)) {
      return NextResponse.json(
        { error: 'Invalid store code ID' },
        { status: 400 }
      )
    }

    const storeCode = await prisma.storeCode.findUnique({
      where: { id: storeCodeId },
      include: {
        _count: {
          select: {
            orders: true
          }
        }
      }
    })

    if (!storeCode) {
      return NextResponse.json(
        { error: 'Store code not found' },
        { status: 404 }
      )
    }

    return NextResponse.json(storeCode)
  } catch (error) {
    console.error('Error fetching store code:', error)
    return NextResponse.json(
      { error: 'Error fetching store code' },
      { status: 500 }
    )
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    const storeCodeId = parseInt(id)

    if (isNaN(storeCodeId)) {
      return NextResponse.json(
        { error: 'Invalid store code ID' },
        { status: 400 }
      )
    }

    const body = await request.json()
    const { code, name } = body

    if (!code) {
      return NextResponse.json(
        { error: 'Store code is required' },
        { status: 400 }
      )
    }

    // Check if store code exists
    const existingStoreCode = await prisma.storeCode.findUnique({
      where: { id: storeCodeId }
    })

    if (!existingStoreCode) {
      return NextResponse.json(
        { error: 'Store code not found' },
        { status: 404 }
      )
    }

    // Update the store code
    const updatedStoreCode = await prisma.storeCode.update({
      where: { id: storeCodeId },
      data: {
        code: code.toUpperCase(),
        name: name || null
      },
      include: {
        _count: {
          select: {
            orders: true
          }
        }
      }
    })

    return NextResponse.json(updatedStoreCode)
  } catch (error: unknown) {
    if (error && typeof error === 'object' && 'code' in error && error.code === 'P2002') {
      return NextResponse.json(
        { error: 'Store code already exists' },
        { status: 400 }
      )
    }

    console.error('Error updating store code:', error)
    return NextResponse.json(
      { error: 'Error updating store code' },
      { status: 500 }
    )
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    const storeCodeId = parseInt(id)

    if (isNaN(storeCodeId)) {
      return NextResponse.json(
        { error: 'Invalid store code ID' },
        { status: 400 }
      )
    }

    // Check if store code exists
    const existingStoreCode = await prisma.storeCode.findUnique({
      where: { id: storeCodeId },
      include: {
        _count: {
          select: {
            orders: true
          }
        }
      }
    })

    if (!existingStoreCode) {
      return NextResponse.json(
        { error: 'Store code not found' },
        { status: 404 }
      )
    }

    // Check if store code has associated orders
    if (existingStoreCode._count.orders > 0) {
      return NextResponse.json(
        { error: 'Cannot delete store code with associated orders' },
        { status: 400 }
      )
    }

    // Delete the store code
    await prisma.storeCode.delete({
      where: { id: storeCodeId }
    })

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error('Error deleting store code:', error)
    return NextResponse.json(
      { error: 'Error deleting store code' },
      { status: 500 }
    )
  }
}

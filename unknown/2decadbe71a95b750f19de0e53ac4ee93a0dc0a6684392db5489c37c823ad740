'use client'

import { useEffect, useState } from 'react'
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { Card } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { ToggleGroup, ToggleGroupItem } from '@/components/ui/toggle-group'
import Link from 'next/link'
import { LuArrowLeft, LuPackage, LuCheck, LuX, LuPencil, LuUser, LuStore } from 'react-icons/lu'
import { ImagePreview } from '@/components/ui/image-preview'
import { useScrollToTop } from '@/hooks/use-scroll'

type Order = {
  id: number
  productName: string
  quantity: number
  usageUnit?: string | null
  comment?: string | null
  imageFilename: string | null
  storePrice: number
  pasabuyFee: number
  customerPrice: number
  resellerPrice: number // For compatibility
  isBought: boolean
  packingStatus: string
  storeCode?: {
    id: number
    code: string
    name: string | null
  } | null
  customer?: {
    id: number
    name: string
  } | null
  createdAt: string
  updatedAt: string
}

export default function OrderDetailPage() {
  const params = useParams()
  const router = useRouter()
  const orderId = parseInt(params.id as string)

  const [order, setOrder] = useState<Order | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // Automatically scroll to top when page loads
  useScrollToTop()

  useEffect(() => {
    async function fetchOrder() {
      try {
        setIsLoading(true)
        setError(null)

        const response = await fetch(`/api/orders/${orderId}`)
        if (!response.ok) {
          if (response.status === 404) {
            throw new Error('Order not found')
          }
          throw new Error('Failed to fetch order')
        }

        const order = await response.json()
        setOrder(order)
      } catch (err) {
        setError(err instanceof Error ? err.message : 'An error occurred')
      } finally {
        setIsLoading(false)
      }
    }

    if (orderId && !isNaN(orderId)) {
      fetchOrder()
    } else {
      setError('Invalid order ID')
      setIsLoading(false)
    }
  }, [orderId])

  const handleStatusUpdate = async (field: 'isBought' | 'packingStatus', value: boolean | string) => {
    if (!order) return

    try {
      const response = await fetch(`/api/orders/${order.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ [field]: value }),
      })

      if (!response.ok) {
        throw new Error('Failed to update order')
      }

      const updatedOrder = await response.json()
      setOrder(updatedOrder)
    } catch (err) {
      console.error('Error updating order:', err)
    }
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-PH', {
      style: 'currency',
      currency: 'PHP',
    }).format(amount)
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-PH', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  if (isLoading) {
    return (
      <div className="space-y-6 py-4">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/3 mb-4"></div>
          <div className="h-64 bg-gray-200 rounded"></div>
        </div>
      </div>
    )
  }

  if (error || !order) {
    return (
      <div className="space-y-6 py-4">
        <div className="flex orders-center gap-4">
          <Button
            variant="outline"
            size="icon"
            onClick={() => router.back()}
          >
            <LuArrowLeft className="h-4 w-4" />
          </Button>
          <h1 className="text-2xl font-semibold tracking-tight">Order Not Found</h1>
        </div>

        <Card className="p-6">
          <div className="text-center">
            <h2 className="text-sm font-medium text-red-600">Error</h2>
            <p className="text-sm text-muted-foreground mt-1">{error}</p>
            <Button onClick={() => router.back()} className="mt-4">
              Go Back
            </Button>
          </div>
        </Card>
      </div>
    )
  }

  const totalCustomerPrice = order.quantity * order.customerPrice
  const totalStorePrice = order.quantity * order.storePrice
  const totalProfit = order.quantity * order.pasabuyFee

  return (
    <div className="space-y-6 py-4">
      {/* Header */}
      <div className="flex orders-center gap-4">
        <Button
          variant="outline"
          size="icon"
          onClick={() => router.back()}
        >
          <LuArrowLeft className="h-4 w-4" />
        </Button>
        <div className="flex-1">
          <h1 className="text-2xl font-semibold tracking-tight">{order.productName}</h1>
          <p className="text-muted-foreground">
            Added on {formatDate(order.createdAt)}
          </p>
        </div>
        <Button asChild>
          <Link href={`/orders/${order.id}/edit`}>
            <LuPencil className="h-4 w-4 mr-2" />
            Edit Order
          </Link>
        </Button>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* order Image */}
        <Card className="p-6">
          <h2 className="text-lg font-semibold mb-4">Product Image</h2>
          {order.imageFilename ? (
            <div className="w-full max-w-md mx-auto">
              <ImagePreview
                src={`/api/images/orders/${order.imageFilename}`}
                alt={order.productName}
                className="w-full h-auto rounded-lg"
              />
            </div>
          ) : (
            <div className="w-full h-64 bg-gray-100 rounded-lg flex orders-center justify-center">
              <div className="text-center">
                <LuPackage className="mx-auto h-16 w-16 text-gray-400" />
                <p className="text-sm text-muted-foreground mt-2">No image available</p>
              </div>
            </div>
          )}
        </Card>

        {/* order Details */}
        <div className="space-y-6">
          {/* Basic Info */}
          <Card className="p-6">
            <h2 className="text-lg font-semibold mb-4">order Details</h2>
            <div className="space-y-4">
              <div className="flex justify-between">
                <span className="text-muted-foreground">Quantity:</span>
                <span className="font-medium">
                  {order.quantity}{order.usageUnit ? ` ${order.usageUnit}` : ''}
                </span>
              </div>
              {order.usageUnit && (
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Unit:</span>
                  <span className="font-medium">{order.usageUnit}</span>
                </div>
              )}
              {order.comment && (
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Comment:</span>
                  <span className="font-medium">{order.comment}</span>
                </div>
              )}
              <div className="flex justify-between">
                <span className="text-muted-foreground">Store Price:</span>
                <span className="font-medium">{formatCurrency(order.storePrice)}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-muted-foreground">Pasabuy Fee:</span>
                <span className="font-medium">{formatCurrency(order.pasabuyFee)}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-muted-foreground">customer Price:</span>
                <span className="font-medium">{formatCurrency(order.customerPrice)}</span>
              </div>
              <hr />
              <div className="flex justify-between text-lg">
                <span className="font-medium">Total Value:</span>
                <span className="font-bold">{formatCurrency(totalCustomerPrice)}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-muted-foreground">Total Store Cost:</span>
                <span>{formatCurrency(totalStorePrice)}</span>
              </div>
              <div className="flex justify-between text-green-600">
                <span className="font-medium">Total Profit:</span>
                <span className="font-bold">{formatCurrency(totalProfit)}</span>
              </div>
            </div>
          </Card>

          {/* Associated Info */}
          <Card className="p-6">
            <h2 className="text-lg font-semibold mb-4">Associated Information</h2>
            <div className="space-y-4">
              {order.customer && (
                <div className="flex orders-center justify-between">
                  <div className="flex orders-center gap-2">
                    <LuUser className="h-4 w-4 text-muted-foreground" />
                    <span className="text-muted-foreground">customer:</span>
                  </div>
                  <Button variant="outline" size="sm" asChild>
                    <Link href={`/customers/${order.customer.id}`}>
                      {order.customer.name}
                    </Link>
                  </Button>
                </div>
              )}
              {order.storeCode && (
                <div className="flex orders-center justify-between">
                  <div className="flex orders-center gap-2">
                    <LuStore className="h-4 w-4 text-muted-foreground" />
                    <span className="text-muted-foreground">Store:</span>
                  </div>
                  <Button variant="outline" size="sm" asChild>
                    <Link href={`/buy-list/${order.storeCode.code.toLowerCase()}`}>
                      {order.storeCode.name || order.storeCode.code}
                    </Link>
                  </Button>
                </div>
              )}
            </div>
          </Card>
        </div>
      </div>

      {/* Status Controls */}
      <Card className="p-6">
        <h2 className="text-lg font-semibold mb-4">Status Management</h2>
        <div className="flex flex-col sm:flex-row gap-6">
          {/* Bought Status */}
          <div className="flex-1">
            <div className="flex orders-center gap-2 mb-3">
              <span className="font-medium">Purchase Status:</span>
              <Badge variant={order.isBought ? "default" : "outline"}>
                {order.isBought ? "Bought" : "To Buy"}
              </Badge>
            </div>
            <ToggleGroup
              type="single"
              value={order.isBought ? 'yes' : 'no'}
              onValueChange={(value) => {
                if (value) {
                  handleStatusUpdate('isBought', value === 'yes')
                }
              }}
              className="justify-start"
            >
              <ToggleGroupItem value="no" className="px-4">
                <LuX className="h-4 w-4 mr-2" />
                To Buy
              </ToggleGroupItem>
              <ToggleGroupItem value="yes" className="px-4">
                <LuCheck className="h-4 w-4 mr-2" />
                Bought
              </ToggleGroupItem>
            </ToggleGroup>
          </div>

          {/* Packing Status */}
          <div className="flex-1">
            <div className="flex orders-center gap-2 mb-3">
              <span className="font-medium">Packing Status:</span>
              <Badge variant={order.packingStatus === 'Packed' ? "default" : "outline"}>
                {order.packingStatus}
              </Badge>
            </div>
            <ToggleGroup
              type="single"
              value={order.packingStatus.toLowerCase().replace(' ', '_')}
              onValueChange={(value) => {
                if (value) {
                  const status = value === 'not_packed' ? 'Not Packed' : 'Packed'
                  handleStatusUpdate('packingStatus', status)
                }
              }}
              className="justify-start"
            >
              <ToggleGroupItem value="not_packed" className="px-4">
                Not Packed
              </ToggleGroupItem>
              <ToggleGroupItem value="packed" className="px-4">
                Packed
              </ToggleGroupItem>
            </ToggleGroup>
          </div>
        </div>
      </Card>
    </div>
  )
}

'use client'

import { useEffect, useState } from 'react'
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { Card } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { LuArrowLeft, LuPencil, LuStore, LuMapPin, LuPhone, LuMail, LuGlobe, LuClock, LuTruck, LuPackage } from 'react-icons/lu'
import Link from 'next/link'

// Enhanced store data type
interface EnhancedStoreData {
  id: number
  code: string
  name?: string | null
  storeType?: string
  status?: string
  parentStoreId?: number
  storeGroup?: string
  region?: string
  district?: string
  address?: string
  city?: string
  state?: string
  postalCode?: string
  country?: string
  phone?: string
  email?: string
  website?: string
  managerName?: string
  managerPhone?: string
  managerEmail?: string
  contactPerson?: string
  operatingHours?: string
  timezone?: string
  isOpen?: boolean
  allowsPickup?: boolean
  allowsDelivery?: boolean
  deliveryRadius?: number
  minimumOrder?: number
  serviceFee?: number
  averageProcessingTime?: number
  capacity?: number
  priority?: string
  totalOrders?: number
  totalRevenue?: number
  averageOrderValue?: number
  notes?: string
  internalNotes?: string
  specialInstructions?: string
  externalStoreId?: string
  apiEndpoint?: string
  apiKey?: string
  createdAt?: string
  updatedAt?: string
  _count?: {
    orders: number
  }
}

export default function StoreDetailPage() {
  const params = useParams()
  const router = useRouter()
  const storeId = params.id as string

  const [store, setStore] = useState<EnhancedStoreData | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    async function fetchStore() {
      try {
        setIsLoading(true)
        setError(null)

        const response = await fetch(`/api/store-codes/${storeId}`)
        if (!response.ok) {
          if (response.status === 404) {
            throw new Error('Store not found')
          }
          throw new Error('Failed to fetch store')
        }

        const data = await response.json()
        setStore(data)
      } catch (err) {
        setError(err instanceof Error ? err.message : 'An error occurred')
      } finally {
        setIsLoading(false)
      }
    }

    if (storeId) {
      fetchStore()
    }
  }, [storeId])

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }

  const getStatusColor = (status?: string) => {
    switch (status) {
      case 'ACTIVE': return 'bg-green-100 text-green-800'
      case 'INACTIVE': return 'bg-gray-100 text-gray-800'
      case 'MAINTENANCE': return 'bg-yellow-100 text-yellow-800'
      case 'TEMPORARILY_CLOSED': return 'bg-orange-100 text-orange-800'
      case 'PERMANENTLY_CLOSED': return 'bg-red-100 text-red-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  if (isLoading) {
    return (
      <div className="space-y-6 py-4">
        <div className="flex items-center gap-4">
          <Button variant="outline" size="icon" onClick={() => router.back()}>
            <LuArrowLeft className="h-4 w-4" />
          </Button>
          <div className="animate-pulse">
            <div className="h-8 bg-muted/50 rounded w-48"></div>
            <div className="h-4 bg-muted/50 rounded w-32 mt-2"></div>
          </div>
        </div>

        <div className="grid gap-4">
          {[...Array(3)].map((_, i) => (
            <Card key={i} className="p-6">
              <div className="animate-pulse">
                <div className="h-4 bg-muted/50 rounded w-24 mb-4"></div>
                <div className="space-y-2">
                  <div className="h-3 bg-muted/50 rounded w-full"></div>
                  <div className="h-3 bg-muted/50 rounded w-3/4"></div>
                </div>
              </div>
            </Card>
          ))}
        </div>
      </div>
    )
  }

  if (error || !store) {
    return (
      <div className="space-y-6 py-4">
        <div className="flex items-center gap-4">
          <Button variant="outline" size="icon" onClick={() => router.back()}>
            <LuArrowLeft className="h-4 w-4" />
          </Button>
          <h1 className="text-2xl font-semibold tracking-tight">Store Not Found</h1>
        </div>

        <Card className="p-6">
          <div className="text-center">
            <h2 className="text-sm font-medium text-red-600">Error</h2>
            <p className="text-sm text-muted-foreground mt-1">{error}</p>
            <Button onClick={() => router.back()} className="mt-4">
              Go Back
            </Button>
          </div>
        </Card>
      </div>
    )
  }

  return (
    <div className="space-y-6 py-4">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="outline" size="icon" onClick={() => router.back()}>
            <LuArrowLeft className="h-4 w-4" />
          </Button>
          <div>
            <h1 className="text-2xl font-semibold tracking-tight">
              {store.name || store.code}
            </h1>
            <p className="text-muted-foreground">
              Store Code: {store.code}
            </p>
          </div>
        </div>
        <Button asChild>
          <Link href={`/stores/${store.id}/edit`}>
            <LuPencil className="h-4 w-4 mr-2" />
            Edit Store
          </Link>
        </Button>
      </div>

      {/* Status and Type */}
      <div className="flex gap-2">
        {store.status && (
          <Badge className={getStatusColor(store.status)}>
            {store.status.replace('_', ' ')}
          </Badge>
        )}
        {store.storeType && (
          <Badge variant="outline">
            {store.storeType}
          </Badge>
        )}
        {store.isOpen !== undefined && (
          <Badge variant={store.isOpen ? "default" : "secondary"}>
            {store.isOpen ? 'Open' : 'Closed'}
          </Badge>
        )}
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        <Card className="p-4">
          <div className="text-center">
            <p className="text-2xl font-bold text-blue-600">{store._count?.orders || 0}</p>
            <p className="text-sm text-muted-foreground">Total Orders</p>
          </div>
        </Card>
        <Card className="p-4">
          <div className="text-center">
            <p className="text-2xl font-bold text-green-600">{store.totalOrders || 0}</p>
            <p className="text-sm text-muted-foreground">Completed Orders</p>
          </div>
        </Card>
        <Card className="p-4">
          <div className="text-center">
            <p className="text-2xl font-bold text-purple-600">₱{(store.totalRevenue || 0).toLocaleString()}</p>
            <p className="text-sm text-muted-foreground">Total Revenue</p>
          </div>
        </Card>
        <Card className="p-4">
          <div className="text-center">
            <p className="text-2xl font-bold text-orange-600">₱{(store.averageOrderValue || 0).toLocaleString()}</p>
            <p className="text-sm text-muted-foreground">Avg Order Value</p>
          </div>
        </Card>
      </div>

      {/* Store Information Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {/* Basic Information */}
        <Card className="p-4">
          <h3 className="font-medium text-sm mb-3 flex items-center gap-2">
            <LuStore className="h-4 w-4" />
            Basic Information
          </h3>
          <div className="space-y-2 text-sm">
            <div className="flex justify-between">
              <span className="text-muted-foreground">Store Code:</span>
              <span className="font-medium">{store.code}</span>
            </div>
            {store.name && (
              <div className="flex justify-between">
                <span className="text-muted-foreground">Name:</span>
                <span>{store.name}</span>
              </div>
            )}
            {store.storeGroup && (
              <div className="flex justify-between">
                <span className="text-muted-foreground">Group:</span>
                <span>{store.storeGroup}</span>
              </div>
            )}
            {store.region && (
              <div className="flex justify-between">
                <span className="text-muted-foreground">Region:</span>
                <span>{store.region}</span>
              </div>
            )}
            {store.priority && (
              <div className="flex justify-between">
                <span className="text-muted-foreground">Priority:</span>
                <span>{store.priority}</span>
              </div>
            )}
          </div>
        </Card>

        {/* Contact Information */}
        <Card className="p-4">
          <h3 className="font-medium text-sm mb-3 flex items-center gap-2">
            <LuPhone className="h-4 w-4" />
            Contact Information
          </h3>
          <div className="space-y-2 text-sm">
            {store.phone && (
              <div className="flex items-center gap-2">
                <LuPhone className="h-3 w-3 text-muted-foreground" />
                <a href={`tel:${store.phone}`} className="text-blue-600 hover:underline">
                  {store.phone}
                </a>
              </div>
            )}
            {store.email && (
              <div className="flex items-center gap-2">
                <LuMail className="h-3 w-3 text-muted-foreground" />
                <a href={`mailto:${store.email}`} className="text-blue-600 hover:underline">
                  {store.email}
                </a>
              </div>
            )}
            {store.website && (
              <div className="flex items-center gap-2">
                <LuGlobe className="h-3 w-3 text-muted-foreground" />
                <a href={store.website} target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline">
                  {store.website}
                </a>
              </div>
            )}
            {store.managerName && (
              <div className="flex justify-between">
                <span className="text-muted-foreground">Manager:</span>
                <span>{store.managerName}</span>
              </div>
            )}
            {store.contactPerson && (
              <div className="flex justify-between">
                <span className="text-muted-foreground">Contact Person:</span>
                <span>{store.contactPerson}</span>
              </div>
            )}
          </div>
        </Card>
      </div>

      {/* Additional Information */}
      {(store.address || store.city || store.operatingHours || store.notes) && (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {/* Location */}
          {(store.address || store.city) && (
            <Card className="p-4">
              <h3 className="font-medium text-sm mb-3 flex items-center gap-2">
                <LuMapPin className="h-4 w-4" />
                Location
              </h3>
              <div className="space-y-1 text-sm">
                {store.address && <p>{store.address}</p>}
                {store.city && <p>{store.city}, {store.state || ''} {store.postalCode || ''}</p>}
                {store.country && <p>{store.country}</p>}
              </div>
            </Card>
          )}

          {/* Operations */}
          {(store.operatingHours || store.allowsDelivery !== undefined || store.allowsPickup !== undefined) && (
            <Card className="p-4">
              <h3 className="font-medium text-sm mb-3 flex items-center gap-2">
                <LuClock className="h-4 w-4" />
                Operations
              </h3>
              <div className="space-y-2 text-sm">
                {store.operatingHours && (
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Hours:</span>
                    <span>{store.operatingHours}</span>
                  </div>
                )}
                {store.allowsDelivery !== undefined && (
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Delivery:</span>
                    <span>{store.allowsDelivery ? 'Available' : 'Not Available'}</span>
                  </div>
                )}
                {store.allowsPickup !== undefined && (
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Pickup:</span>
                    <span>{store.allowsPickup ? 'Available' : 'Not Available'}</span>
                  </div>
                )}
                {store.minimumOrder && (
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Min Order:</span>
                    <span>₱{store.minimumOrder.toLocaleString()}</span>
                  </div>
                )}
              </div>
            </Card>
          )}
        </div>
      )}

      {/* Notes */}
      {(store.notes || store.specialInstructions) && (
        <Card className="p-4">
          <h3 className="font-medium text-sm mb-3">Notes & Instructions</h3>
          <div className="space-y-2 text-sm">
            {store.notes && (
              <div>
                <span className="text-muted-foreground">Notes:</span>
                <p className="mt-1">{store.notes}</p>
              </div>
            )}
            {store.specialInstructions && (
              <div>
                <span className="text-muted-foreground">Special Instructions:</span>
                <p className="mt-1">{store.specialInstructions}</p>
              </div>
            )}
          </div>
        </Card>
      )}

      {/* Timestamps */}
      {(store.createdAt || store.updatedAt) && (
        <Card className="p-4">
          <h3 className="font-medium text-sm mb-3">Timestamps</h3>
          <div className="grid grid-cols-2 gap-4 text-sm">
            {store.createdAt && (
              <div>
                <span className="text-muted-foreground">Created:</span>
                <p className="mt-1">{formatDate(store.createdAt)}</p>
              </div>
            )}
            {store.updatedAt && (
              <div>
                <span className="text-muted-foreground">Last Updated:</span>
                <p className="mt-1">{formatDate(store.updatedAt)}</p>
              </div>
            )}
          </div>
        </Card>
      )}
    </div>
  )
}

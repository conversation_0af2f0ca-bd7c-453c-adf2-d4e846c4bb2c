import { NextRequest, NextResponse } from 'next/server'
import { PricingService } from '@/lib/pricing-service'
import { prisma } from '@/lib/db'

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ storeCodeId: string }> }
) {
  try {
    const { storeCodeId: storeCodeIdParam } = await params
    const storeCodeId = parseInt(storeCodeIdParam)
    
    if (isNaN(storeCodeId)) {
      return NextResponse.json(
        { error: 'Invalid store code ID' },
        { status: 400 }
      )
    }

    const storePricing = await prisma.storePricing.findUnique({
      where: { storeCodeId },
      include: {
        storeCode: {
          select: {
            id: true,
            code: true,
            name: true
          }
        },
        pricingTiers: {
          orderBy: { sortOrder: 'asc' }
        }
      }
    })

    if (!storePricing) {
      return NextResponse.json(
        { error: 'Store pricing configuration not found' },
        { status: 404 }
      )
    }

    return NextResponse.json(storePricing)
  } catch (error) {
    console.error('Error fetching store pricing:', error)
    return NextResponse.json(
      { error: 'Error fetching store pricing' },
      { status: 500 }
    )
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ storeCodeId: string }> }
) {
  try {
    const { storeCodeId: storeCodeIdParam } = await params
    const storeCodeId = parseInt(storeCodeIdParam)
    
    if (isNaN(storeCodeId)) {
      return NextResponse.json(
        { error: 'Invalid store code ID' },
        { status: 400 }
      )
    }

    const body = await request.json()
    const {
      name,
      serviceFee,
      pricingTiers
    } = body

    // Check if store pricing exists
    const existingPricing = await prisma.storePricing.findUnique({
      where: { storeCodeId }
    })

    if (!existingPricing) {
      return NextResponse.json(
        { error: 'Store pricing configuration not found' },
        { status: 404 }
      )
    }

    // Validation
    if (name !== undefined && (!name || !name.trim())) {
      return NextResponse.json(
        { error: 'Pricing configuration name cannot be empty' },
        { status: 400 }
      )
    }

    if (serviceFee !== undefined && (serviceFee < 0)) {
      return NextResponse.json(
        { error: 'Pasabuy fee cannot be negative' },
        { status: 400 }
      )
    }

    if (pricingTiers !== undefined && (!Array.isArray(pricingTiers) || pricingTiers.length === 0)) {
      return NextResponse.json(
        { error: 'At least one pricing tier is required' },
        { status: 400 }
      )
    }

    const updateData: any = {}
    if (name !== undefined) updateData.name = name.trim()
    if (serviceFee !== undefined) updateData.serviceFee = parseFloat(serviceFee)
    if (pricingTiers !== undefined) updateData.pricingTiers = pricingTiers

    const storePricing = await PricingService.updateStorePricing(storeCodeId, updateData)

    return NextResponse.json(storePricing)
  } catch (error) {
    console.error('Error updating store pricing:', error)
    return NextResponse.json(
      { error: 'Error updating store pricing' },
      { status: 500 }
    )
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ storeCodeId: string }> }
) {
  try {
    const { storeCodeId: storeCodeIdParam } = await params
    const storeCodeId = parseInt(storeCodeIdParam)
    
    if (isNaN(storeCodeId)) {
      return NextResponse.json(
        { error: 'Invalid store code ID' },
        { status: 400 }
      )
    }

    // Check if store pricing exists
    const existingPricing = await prisma.storePricing.findUnique({
      where: { storeCodeId }
    })

    if (!existingPricing) {
      return NextResponse.json(
        { error: 'Store pricing configuration not found' },
        { status: 404 }
      )
    }

    await PricingService.deleteStorePricing(storeCodeId)

    return NextResponse.json({ 
      success: true, 
      message: 'Store pricing configuration deleted successfully' 
    })
  } catch (error) {
    console.error('Error deleting store pricing:', error)
    return NextResponse.json(
      { error: 'Error deleting store pricing' },
      { status: 500 }
    )
  }
}

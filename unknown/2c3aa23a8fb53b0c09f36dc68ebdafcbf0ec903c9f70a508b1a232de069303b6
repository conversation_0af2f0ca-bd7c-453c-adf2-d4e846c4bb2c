'use client'

import { useTheme as useNextTheme } from 'next-themes'
import { useEffect, useState } from 'react'

export function useTheme() {
  const { theme, setTheme, resolvedTheme } = useNextTheme()
  const [mounted, setMounted] = useState(false)

  useEffect(() => {
    setMounted(true)
  }, [])

  return {
    theme,
    setTheme,
    resolvedTheme,
    mounted,
    isDark: mounted ? resolvedTheme === 'dark' : false,
    isLight: mounted ? resolvedTheme === 'light' : false,
    isSystem: mounted ? theme === 'system' : false,
  }
}

// Utility function to get theme-aware classes
export function getThemeAwareClass(lightClass: string, darkClass: string) {
  return `${lightClass} dark:${darkClass}`
}

// Utility function for theme-aware values
export function useThemeValue<T>(lightValue: T, darkValue: T): T | undefined {
  const { resolvedTheme, mounted } = useTheme()
  
  if (!mounted) return undefined
  
  return resolvedTheme === 'dark' ? darkValue : lightValue
}

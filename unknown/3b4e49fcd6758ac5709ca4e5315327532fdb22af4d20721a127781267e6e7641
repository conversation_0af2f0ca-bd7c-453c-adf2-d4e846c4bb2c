import { cn } from '@/lib/utils'

/**
 * Standard animation durations used across the application
 */
export const ANIMATION_DURATIONS = {
  fast: 150,
  normal: 200,
  slow: 300,
  slower: 500
} as const

/**
 * Standard easing functions
 */
export const EASING = {
  easeInOut: 'cubic-bezier(0.4, 0, 0.2, 1)',
  easeOut: 'cubic-bezier(0, 0, 0.2, 1)',
  easeIn: 'cubic-bezier(0.4, 0, 1, 1)',
  bounce: 'cubic-bezier(0.68, -0.55, 0.265, 1.55)'
} as const

/**
 * Common transition classes for consistent animations
 */
export const TRANSITIONS = {
  // Basic transitions
  all: 'transition-all duration-200 ease-in-out',
  colors: 'transition-colors duration-200 ease-in-out',
  transform: 'transition-transform duration-200 ease-in-out',
  opacity: 'transition-opacity duration-200 ease-in-out',
  
  // Hover effects
  hover: 'transition-all duration-200 hover:shadow-md hover:bg-accent/30 dark:hover:bg-accent/20',
  hoverScale: 'transition-transform duration-200 hover:scale-105',
  hoverLift: 'transition-all duration-200 hover:shadow-lg hover:-translate-y-1',
  
  // Press effects
  press: 'transition-transform duration-150 active:scale-95',
  pressSubtle: 'transition-transform duration-150 active:scale-98',
  
  // Focus effects
  focus: 'focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2',
  
  // Loading states
  pulse: 'animate-pulse',
  spin: 'animate-spin',
  bounce: 'animate-bounce',
  
  // Slide animations
  slideInFromLeft: 'animate-in slide-in-from-left-2 duration-300',
  slideInFromRight: 'animate-in slide-in-from-right-2 duration-300',
  slideInFromTop: 'animate-in slide-in-from-top-2 duration-300',
  slideInFromBottom: 'animate-in slide-in-from-bottom-2 duration-300',
  
  // Fade animations
  fadeIn: 'animate-in fade-in duration-200',
  fadeOut: 'animate-out fade-out duration-200',
  
  // Scale animations
  scaleIn: 'animate-in zoom-in-95 duration-200',
  scaleOut: 'animate-out zoom-out-95 duration-200'
} as const

/**
 * Animation utility functions
 */
export const animations = {
  /**
   * Get transition classes for interactive elements
   */
  interactive: (options?: {
    hover?: boolean
    press?: boolean
    focus?: boolean
  }) => {
    const { hover = true, press = true, focus = true } = options || {}
    
    return cn(
      TRANSITIONS.colors,
      hover && TRANSITIONS.hover,
      press && TRANSITIONS.press,
      focus && TRANSITIONS.focus
    )
  },

  /**
   * Get transition classes for cards
   */
  card: (options?: {
    hover?: boolean
    press?: boolean
    selected?: boolean
  }) => {
    const { hover = true, press = true, selected = false } = options || {}
    
    return cn(
      TRANSITIONS.all,
      hover && 'hover:shadow-md hover:bg-accent/30 dark:hover:bg-accent/20',
      press && 'active:scale-[0.99]',
      selected && 'ring-2 ring-primary/40 bg-primary/10 dark:bg-primary/5'
    )
  },

  /**
   * Get transition classes for buttons
   */
  button: (variant?: 'default' | 'subtle' | 'ghost') => {
    switch (variant) {
      case 'subtle':
        return cn(TRANSITIONS.colors, TRANSITIONS.pressSubtle)
      case 'ghost':
        return cn(TRANSITIONS.colors, 'hover:bg-accent hover:text-accent-foreground')
      default:
        return cn(TRANSITIONS.all, TRANSITIONS.press)
    }
  },

  /**
   * Get entrance animation classes
   */
  entrance: (type?: 'fade' | 'slide' | 'scale', direction?: 'up' | 'down' | 'left' | 'right') => {
    switch (type) {
      case 'slide':
        switch (direction) {
          case 'left': return TRANSITIONS.slideInFromLeft
          case 'right': return TRANSITIONS.slideInFromRight
          case 'up': return TRANSITIONS.slideInFromTop
          case 'down': return TRANSITIONS.slideInFromBottom
          default: return TRANSITIONS.slideInFromBottom
        }
      case 'scale':
        return TRANSITIONS.scaleIn
      default:
        return TRANSITIONS.fadeIn
    }
  },

  /**
   * Get exit animation classes
   */
  exit: (type?: 'fade' | 'scale') => {
    switch (type) {
      case 'scale':
        return TRANSITIONS.scaleOut
      default:
        return TRANSITIONS.fadeOut
    }
  },

  /**
   * Get loading animation classes
   */
  loading: (type?: 'pulse' | 'spin' | 'bounce') => {
    switch (type) {
      case 'spin': return TRANSITIONS.spin
      case 'bounce': return TRANSITIONS.bounce
      default: return TRANSITIONS.pulse
    }
  }
}

/**
 * Stagger animation utility for lists
 */
export const staggerChildren = (delay: number = 50) => ({
  container: 'animate-in fade-in duration-300',
  child: (index: number) => `animate-in slide-in-from-bottom-2 duration-300 delay-[${index * delay}ms]`
})

/**
 * Spring animation configurations
 */
export const springs = {
  gentle: { type: 'spring', stiffness: 300, damping: 30 },
  wobbly: { type: 'spring', stiffness: 180, damping: 12 },
  stiff: { type: 'spring', stiffness: 400, damping: 40 },
  slow: { type: 'spring', stiffness: 200, damping: 50 }
} as const

/**
 * Common animation variants for framer-motion
 */
export const motionVariants = {
  fadeIn: {
    initial: { opacity: 0 },
    animate: { opacity: 1 },
    exit: { opacity: 0 }
  },
  
  slideUp: {
    initial: { opacity: 0, y: 20 },
    animate: { opacity: 1, y: 0 },
    exit: { opacity: 0, y: -20 }
  },
  
  slideDown: {
    initial: { opacity: 0, y: -20 },
    animate: { opacity: 1, y: 0 },
    exit: { opacity: 0, y: 20 }
  },
  
  scale: {
    initial: { opacity: 0, scale: 0.95 },
    animate: { opacity: 1, scale: 1 },
    exit: { opacity: 0, scale: 0.95 }
  },
  
  staggerContainer: {
    animate: {
      transition: {
        staggerChildren: 0.1
      }
    }
  },
  
  staggerChild: {
    initial: { opacity: 0, y: 20 },
    animate: { opacity: 1, y: 0 }
  }
} as const

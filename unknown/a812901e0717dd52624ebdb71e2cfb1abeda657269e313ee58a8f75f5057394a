'use client'

import { useState, useEffect } from 'react'
import { SearchIcon, XIcon } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { cn } from '@/lib/utils'

interface SearchInputProps {
  value: string
  onChange: (value: string) => void
  placeholder?: string
  className?: string
  showClearButton?: boolean
  debounceMs?: number
  autoFocus?: boolean
  onClear?: () => void
  variant?: 'default' | 'command' | 'filter'
  size?: 'sm' | 'md' | 'lg'
}

export function SearchInput({
  value,
  onChange,
  placeholder = 'Search...',
  className,
  showClearButton = true,
  debounceMs = 0,
  autoFocus = false,
  onClear,
  variant = 'default',
  size = 'md'
}: SearchInputProps) {
  const [internalValue, setInternalValue] = useState(value)

  // Debounced value update
  useEffect(() => {
    if (debounceMs > 0) {
      const timer = setTimeout(() => {
        onChange(internalValue)
      }, debounceMs)

      return () => clearTimeout(timer)
    } else {
      onChange(internalValue)
    }
  }, [internalValue, debounceMs, onChange])

  // Sync external value changes
  useEffect(() => {
    setInternalValue(value)
  }, [value])

  const handleClear = () => {
    setInternalValue('')
    if (onClear) {
      onClear()
    } else {
      onChange('')
    }
  }

  const getVariantStyles = () => {
    switch (variant) {
      case 'command':
        return 'border-0 shadow-none focus-visible:ring-0 focus-visible:ring-offset-0'
      case 'filter':
        return 'h-10 md:h-8 touch-target'
      default:
        return ''
    }
  }

  const getSizeStyles = () => {
    switch (size) {
      case 'sm':
        return 'h-8 text-sm'
      case 'lg':
        return 'h-12 text-lg'
      default:
        return 'h-10'
    }
  }

  const getIconSize = () => {
    switch (size) {
      case 'sm':
        return 'h-3 w-3'
      case 'lg':
        return 'h-5 w-5'
      default:
        return 'h-4 w-4'
    }
  }

  if (variant === 'command') {
    // Command variant for dialog/command palette usage
    return (
      <div className={cn('flex items-center border-b px-3', className)}>
        <SearchIcon className={cn('mr-2 shrink-0 opacity-50', getIconSize())} />
        <Input
          placeholder={placeholder}
          value={internalValue}
          onChange={(e) => setInternalValue(e.target.value)}
          className={cn(getVariantStyles(), getSizeStyles())}
          autoFocus={autoFocus}
        />
        {showClearButton && internalValue && (
          <Button
            variant="ghost"
            size="sm"
            className="h-8 w-8 p-0"
            onClick={handleClear}
          >
            <XIcon className={getIconSize()} />
          </Button>
        )}
      </div>
    )
  }

  if (variant === 'filter') {
    // Filter variant for filter panels
    return (
      <div className={cn('relative', className)}>
        <SearchIcon className={cn('absolute left-2 top-1/2 -translate-y-1/2 text-muted-foreground', getIconSize())} />
        <Input
          placeholder={placeholder}
          value={internalValue}
          onChange={(e) => setInternalValue(e.target.value)}
          className={cn('pl-8 md:pl-7 text-sm md:text-xs', getVariantStyles(), getSizeStyles())}
          autoFocus={autoFocus}
        />
        {showClearButton && internalValue && (
          <Button
            variant="ghost"
            size="sm"
            className="absolute right-1 top-1/2 -translate-y-1/2 h-6 w-6 p-0"
            onClick={handleClear}
          >
            <XIcon className="h-3 w-3" />
          </Button>
        )}
      </div>
    )
  }

  // Default variant
  return (
    <div className={cn('relative', className)}>
      <SearchIcon className={cn('absolute left-3 top-1/2 -translate-y-1/2 text-muted-foreground', getIconSize())} />
      <Input
        placeholder={placeholder}
        value={internalValue}
        onChange={(e) => setInternalValue(e.target.value)}
        className={cn('pl-10', getSizeStyles())}
        autoFocus={autoFocus}
      />
      {showClearButton && internalValue && (
        <Button
          variant="ghost"
          size="sm"
          className="absolute right-1 top-1/2 -translate-y-1/2 h-8 w-8 p-0"
          onClick={handleClear}
        >
          <XIcon className={getIconSize()} />
        </Button>
      )}
    </div>
  )
}

// Order Analytics and Performance Metrics for PasaBuy Pal
import { prisma } from '@/lib/db'

export interface PerformanceMetrics {
  totalOrders: number
  averageProcessingTime: number
  averageTimeToFirstPurchase: number
  averageTimeToPacking: number
  averageTimeToInvoicing: number
  averageTimeToDelivery: number
  efficiencyScore: number
  onTimeDeliveryRate: number
  orderCompletionRate: number
}

export interface StatusDistribution {
  pending: number
  bought: number
  packed: number
  invoiced: number
  delivered: number
  confirmed: number
}

export interface TrendData {
  date: string
  ordersCreated: number
  ordersPurchased: number
  ordersPacked: number
  ordersDelivered: number
  averageProcessingTime: number
}

export interface BottleneckAnalysis {
  stage: string
  averageTime: number
  delayedOrders: number
  bottleneckScore: number // Higher = more problematic
}

export interface CustomerPerformance {
  customerId: number
  customerName: string
  totalOrders: number
  averageProcessingTime: number
  onTimeDeliveryRate: number
  totalValue: number
  lastOrderDate: string
}

export class OrderAnalyticsService {
  /**
   * Get overall performance metrics
   */
  static async getPerformanceMetrics(
    dateRange?: { from: Date; to: Date }
  ): Promise<PerformanceMetrics> {
    const whereClause = dateRange ? {
      createdAt: {
        gte: dateRange.from,
        lte: dateRange.to
      }
    } : {}

    const [
      totalOrders,
      metricsAgg,
      completedOrders,
      onTimeDeliveries
    ] = await Promise.all([
      // Total orders count
      prisma.order.count({ where: whereClause }),

      // Aggregate metrics
      prisma.orderMetrics.aggregate({
        where: {
          order: whereClause
        },
        _avg: {
          totalProcessingTime: true,
          timeToFirstPurchase: true,
          timeToPacking: true,
          timeToInvoicing: true,
          timeToDelivery: true,
          efficiencyScore: true
        }
      }),

      // Completed orders (delivered)
      prisma.order.count({
        where: {
          ...whereClause,
          deliveryStatus: 'Delivered'
        }
      }),

      // On-time deliveries (efficiency score > 70)
      prisma.orderMetrics.count({
        where: {
          order: whereClause,
          efficiencyScore: { gte: 70 }
        }
      })
    ])

    return {
      totalOrders,
      averageProcessingTime: metricsAgg._avg.totalProcessingTime || 0,
      averageTimeToFirstPurchase: metricsAgg._avg.timeToFirstPurchase || 0,
      averageTimeToPacking: metricsAgg._avg.timeToPacking || 0,
      averageTimeToInvoicing: metricsAgg._avg.timeToInvoicing || 0,
      averageTimeToDelivery: metricsAgg._avg.timeToDelivery || 0,
      efficiencyScore: metricsAgg._avg.efficiencyScore || 0,
      onTimeDeliveryRate: totalOrders > 0 ? (onTimeDeliveries / totalOrders) * 100 : 0,
      orderCompletionRate: totalOrders > 0 ? (completedOrders / totalOrders) * 100 : 0
    }
  }

  /**
   * Get status distribution
   */
  static async getStatusDistribution(
    dateRange?: { from: Date; to: Date }
  ): Promise<StatusDistribution> {
    const whereClause = dateRange ? {
      createdAt: {
        gte: dateRange.from,
        lte: dateRange.to
      }
    } : {}

    const [
      pending,
      bought,
      packed,
      invoiced,
      delivered,
      confirmed
    ] = await Promise.all([
      prisma.order.count({
        where: { ...whereClause, isBought: false }
      }),
      prisma.order.count({
        where: { ...whereClause, isBought: true, packingStatus: 'Not Packed' }
      }),
      prisma.order.count({
        where: { ...whereClause, packingStatus: 'Packed', deliveryStatus: { not: 'Delivered' } }
      }),
      prisma.order.count({
        where: {
          ...whereClause,
          invoiceItems: { some: {} },
          deliveryStatus: { not: 'Delivered' }
        }
      }),
      prisma.order.count({
        where: { ...whereClause, deliveryStatus: 'Delivered' }
      }),
      prisma.order.count({
        where: { ...whereClause, deliveryStatus: 'Confirmed' }
      })
    ])

    return {
      pending,
      bought,
      packed,
      invoiced,
      delivered,
      confirmed
    }
  }

  /**
   * Get trend data over time
   */
  static async getTrendData(
    dateRange: { from: Date; to: Date },
    interval: 'day' | 'week' | 'month' = 'day'
  ): Promise<TrendData[]> {
    // This would require more complex SQL queries
    // For now, providing a simplified version
    const orders = await prisma.order.findMany({
      where: {
        createdAt: {
          gte: dateRange.from,
          lte: dateRange.to
        }
      },
      include: {
        timeline: true,
        metrics: true
      }
    })

    // Group by date and calculate metrics
    const groupedData = new Map<string, {
      ordersCreated: number
      ordersPurchased: number
      ordersPacked: number
      ordersDelivered: number
      processingTimes: number[]
    }>()

    orders.forEach(order => {
      const dateKey = order.createdAt.toISOString().split('T')[0]
      
      if (!groupedData.has(dateKey)) {
        groupedData.set(dateKey, {
          ordersCreated: 0,
          ordersPurchased: 0,
          ordersPacked: 0,
          ordersDelivered: 0,
          processingTimes: []
        })
      }

      const data = groupedData.get(dateKey)!
      data.ordersCreated++

      if (order.isBought) data.ordersPurchased++
      if (order.packingStatus === 'Packed') data.ordersPacked++
      if (order.deliveryStatus === 'Delivered') data.ordersDelivered++
      
      if (order.metrics?.totalProcessingTime) {
        data.processingTimes.push(order.metrics.totalProcessingTime)
      }
    })

    return Array.from(groupedData.entries()).map(([date, data]) => ({
      date,
      ordersCreated: data.ordersCreated,
      ordersPurchased: data.ordersPurchased,
      ordersPacked: data.ordersPacked,
      ordersDelivered: data.ordersDelivered,
      averageProcessingTime: data.processingTimes.length > 0
        ? data.processingTimes.reduce((a, b) => a + b, 0) / data.processingTimes.length
        : 0
    }))
  }

  /**
   * Identify bottlenecks in the workflow
   */
  static async getBottleneckAnalysis(
    dateRange?: { from: Date; to: Date }
  ): Promise<BottleneckAnalysis[]> {
    const whereClause = dateRange ? {
      order: {
        createdAt: {
          gte: dateRange.from,
          lte: dateRange.to
        }
      }
    } : {}

    const [
      purchaseMetrics,
      packingMetrics,
      invoicingMetrics,
      deliveryMetrics
    ] = await Promise.all([
      prisma.orderMetrics.aggregate({
        where: whereClause,
        _avg: { timeToFirstPurchase: true },
        _count: { timeToFirstPurchase: true }
      }),
      prisma.orderMetrics.aggregate({
        where: whereClause,
        _avg: { timeToPacking: true },
        _count: { timeToPacking: true }
      }),
      prisma.orderMetrics.aggregate({
        where: whereClause,
        _avg: { timeToInvoicing: true },
        _count: { timeToInvoicing: true }
      }),
      prisma.orderMetrics.aggregate({
        where: whereClause,
        _avg: { timeToDelivery: true },
        _count: { timeToDelivery: true }
      })
    ])

    // Count delayed orders for each stage (> 24 hours)
    const [
      delayedPurchases,
      delayedPacking,
      delayedInvoicing,
      delayedDeliveries
    ] = await Promise.all([
      prisma.orderMetrics.count({
        where: { ...whereClause, timeToFirstPurchase: { gt: 24 * 60 } }
      }),
      prisma.orderMetrics.count({
        where: { ...whereClause, timeToPacking: { gt: 24 * 60 } }
      }),
      prisma.orderMetrics.count({
        where: { ...whereClause, timeToInvoicing: { gt: 24 * 60 } }
      }),
      prisma.orderMetrics.count({
        where: { ...whereClause, timeToDelivery: { gt: 24 * 60 } }
      })
    ])

    const stages = [
      {
        stage: 'Purchase',
        averageTime: purchaseMetrics._avg.timeToFirstPurchase || 0,
        delayedOrders: delayedPurchases,
        totalOrders: purchaseMetrics._count.timeToFirstPurchase
      },
      {
        stage: 'Packing',
        averageTime: packingMetrics._avg.timeToPacking || 0,
        delayedOrders: delayedPacking,
        totalOrders: packingMetrics._count.timeToPacking
      },
      {
        stage: 'Invoicing',
        averageTime: invoicingMetrics._avg.timeToInvoicing || 0,
        delayedOrders: delayedInvoicing,
        totalOrders: invoicingMetrics._count.timeToInvoicing
      },
      {
        stage: 'Delivery',
        averageTime: deliveryMetrics._avg.timeToDelivery || 0,
        delayedOrders: delayedDeliveries,
        totalOrders: deliveryMetrics._count.timeToDelivery
      }
    ]

    return stages.map(stage => ({
      stage: stage.stage,
      averageTime: stage.averageTime,
      delayedOrders: stage.delayedOrders,
      bottleneckScore: stage.totalOrders > 0
        ? (stage.delayedOrders / stage.totalOrders) * 100 + (stage.averageTime / (24 * 60)) * 50
        : 0
    })).sort((a, b) => b.bottleneckScore - a.bottleneckScore)
  }

  /**
   * Get customer performance analysis
   */
  static async getCustomerPerformance(
    dateRange?: { from: Date; to: Date }
  ): Promise<CustomerPerformance[]> {
    const whereClause = dateRange ? {
      createdAt: {
        gte: dateRange.from,
        lte: dateRange.to
      }
    } : {}

    const customers = await prisma.customer.findMany({
      include: {
        orders: {
          where: whereClause,
          include: {
            metrics: true
          }
        }
      }
    })

    return customers
      .filter(customer => customer.orders.length > 0)
      .map(customer => {
        const orders = customer.orders
        const totalOrders = orders.length
        const totalValue = orders.reduce((sum, order) => sum + order.customerPrice, 0)
        
        const processingTimes = orders
          .map(order => order.metrics?.totalProcessingTime)
          .filter(time => time !== null && time !== undefined) as number[]
        
        const averageProcessingTime = processingTimes.length > 0
          ? processingTimes.reduce((a, b) => a + b, 0) / processingTimes.length
          : 0

        const onTimeOrders = orders.filter(order => 
          order.metrics?.efficiencyScore && order.metrics.efficiencyScore >= 70
        ).length

        const onTimeDeliveryRate = totalOrders > 0 ? (onTimeOrders / totalOrders) * 100 : 0

        const lastOrderDate = orders.length > 0
          ? Math.max(...orders.map(order => order.createdAt.getTime()))
          : 0

        return {
          customerId: customer.id,
          customerName: customer.name,
          totalOrders,
          averageProcessingTime,
          onTimeDeliveryRate,
          totalValue,
          lastOrderDate: new Date(lastOrderDate).toISOString()
        }
      })
      .sort((a, b) => b.totalValue - a.totalValue)
  }

  /**
   * Get real-time dashboard metrics
   */
  static async getDashboardMetrics() {
    const today = new Date()
    const weekAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000)
    const monthAgo = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000)

    const [
      todayMetrics,
      weekMetrics,
      monthMetrics,
      statusDistribution,
      recentBottlenecks
    ] = await Promise.all([
      this.getPerformanceMetrics({ from: today, to: today }),
      this.getPerformanceMetrics({ from: weekAgo, to: today }),
      this.getPerformanceMetrics({ from: monthAgo, to: today }),
      this.getStatusDistribution(),
      this.getBottleneckAnalysis({ from: weekAgo, to: today })
    ])

    return {
      today: todayMetrics,
      week: weekMetrics,
      month: monthMetrics,
      statusDistribution,
      topBottlenecks: recentBottlenecks.slice(0, 3)
    }
  }
}

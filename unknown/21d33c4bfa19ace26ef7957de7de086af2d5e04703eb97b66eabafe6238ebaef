'use client'

import React from 'react'
import { Label } from '@/components/ui/label'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { ChevronDown, ChevronUp, X } from 'lucide-react'
import { cn } from '@/lib/utils'

interface BaseFilterProps {
  label: string
  value?: unknown
  onChange: (value: unknown) => void
  children: React.ReactNode
  className?: string
  collapsible?: boolean
  defaultExpanded?: boolean
  showClearButton?: boolean
  showValueBadge?: boolean
  valueFormatter?: (value: unknown) => string
  variant?: 'default' | 'compact' | 'inline'
}

export function BaseFilter({
  label,
  value,
  onChange,
  children,
  className,
  collapsible = false,
  defaultExpanded = true,
  showClearButton = true,
  showValueBadge = false,
  valueFormatter,
  variant = 'default'
}: BaseFilterProps) {
  const [isExpanded, setIsExpanded] = React.useState(defaultExpanded)
  const hasValue = value !== undefined && value !== null && value !== ''

  const handleClear = () => {
    onChange(undefined)
  }

  const toggleExpanded = () => {
    if (collapsible) {
      setIsExpanded(!isExpanded)
    }
  }

  const formatValue = (val: unknown): string => {
    if (valueFormatter) {
      return valueFormatter(val)
    }

    if (Array.isArray(val)) {
      return `${val.length} selected`
    }

    if (typeof val === 'object' && val !== null) {
      return 'Custom filter'
    }

    return String(val)
  }

  const getVariantStyles = () => {
    switch (variant) {
      case 'compact':
        return {
          container: 'space-y-2',
          header: 'flex items-center justify-between py-1',
          label: 'text-xs font-medium',
          content: 'space-y-2'
        }
      case 'inline':
        return {
          container: 'flex items-center gap-2',
          header: 'flex items-center gap-2',
          label: 'text-sm font-medium whitespace-nowrap',
          content: 'flex items-center gap-2'
        }
      default:
        return {
          container: 'space-y-3',
          header: 'flex items-center justify-between',
          label: 'text-sm font-medium',
          content: 'space-y-3'
        }
    }
  }

  const styles = getVariantStyles()

  if (variant === 'inline') {
    return (
      <div className={cn(styles.container, className)}>
        <Label className={styles.label}>{label}</Label>
        <div className={styles.content}>
          {children}
        </div>
        {hasValue && showValueBadge && (
          <Badge variant="secondary" className="text-xs">
            {formatValue(value)}
          </Badge>
        )}
        {hasValue && showClearButton && (
          <Button
            variant="ghost"
            size="sm"
            onClick={handleClear}
            className="h-6 w-6 p-0"
            title="Clear filter"
          >
            <X className="h-3 w-3" />
          </Button>
        )}
      </div>
    )
  }

  return (
    <div className={cn(styles.container, className)}>
      {/* Filter Header */}
      <div className={styles.header}>
        <div className="flex items-center gap-2">
          <Label
            className={cn(
              styles.label,
              collapsible && 'cursor-pointer select-none'
            )}
            onClick={toggleExpanded}
          >
            {label}
          </Label>

          {hasValue && showValueBadge && (
            <Badge variant="secondary" className="text-xs">
              {formatValue(value)}
            </Badge>
          )}
        </div>

        <div className="flex items-center gap-1">
          {hasValue && showClearButton && (
            <Button
              variant="ghost"
              size="sm"
              onClick={handleClear}
              className="h-6 w-6 p-0"
              title="Clear filter"
            >
              <X className="h-3 w-3" />
            </Button>
          )}

          {collapsible && (
            <Button
              variant="ghost"
              size="sm"
              onClick={toggleExpanded}
              className="h-6 w-6 p-0"
              title={isExpanded ? 'Collapse' : 'Expand'}
            >
              {isExpanded ? (
                <ChevronUp className="h-3 w-3" />
              ) : (
                <ChevronDown className="h-3 w-3" />
              )}
            </Button>
          )}
        </div>
      </div>

      {/* Filter Content */}
      {(!collapsible || isExpanded) && (
        <div className={styles.content}>
          {children}
        </div>
      )}
    </div>
  )
}

/**
 * Specialized filter wrapper for form-like filters
 */
export function FormFilter({
  children,
  className,
  ...props
}: Omit<BaseFilterProps, 'variant'> & { className?: string }) {
  return (
    <BaseFilter
      {...props}
      variant="default"
      className={cn('border rounded-md p-3 bg-background', className)}
    >
      {children}
    </BaseFilter>
  )
}

/**
 * Specialized filter wrapper for sidebar filters
 */
export function SidebarFilter({
  children,
  className,
  ...props
}: Omit<BaseFilterProps, 'variant'> & { className?: string }) {
  return (
    <BaseFilter
      {...props}
      variant="compact"
      collapsible={true}
      defaultExpanded={false}
      className={cn('border-b pb-3', className)}
    >
      {children}
    </BaseFilter>
  )
}

/**
 * Specialized filter wrapper for inline filters
 */
export function InlineFilter({
  children,
  className,
  ...props
}: Omit<BaseFilterProps, 'variant'> & { className?: string }) {
  return (
    <BaseFilter
      {...props}
      variant="inline"
      showValueBadge={true}
      className={className}
    >
      {children}
    </BaseFilter>
  )
}

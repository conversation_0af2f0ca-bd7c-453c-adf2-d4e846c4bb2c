'use client'

import { useEffect, useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import Link from 'next/link'
import { LuX, LuCheck, LuInfo, LuTriangleAlert } from 'react-icons/lu'

export interface Notification {
  id: string
  type: 'success' | 'info' | 'warning' | 'error'
  title: string
  message: string
  action?: {
    label: string
    href?: string
    onClick?: () => void
  }
  autoHide?: boolean
  duration?: number
}

interface NotificationProps {
  notification: Notification
  onDismiss: (id: string) => void
}

const iconMap = {
  success: LuCheck,
  info: LuInfo,
  warning: LuTriangleAlert,
  error: LuTriangleAlert,
}

const colorMap = {
  success: 'bg-green-50 border-green-200 text-green-800',
  info: 'bg-blue-50 border-blue-200 text-blue-800',
  warning: 'bg-yellow-50 border-yellow-200 text-yellow-800',
  error: 'bg-red-50 border-red-200 text-red-800',
}

const iconColorMap = {
  success: 'text-green-600',
  info: 'text-blue-600',
  warning: 'text-yellow-600',
  error: 'text-red-600',
}

export function NotificationItem({ notification, onDismiss }: NotificationProps) {
  const Icon = iconMap[notification.type]

  useEffect(() => {
    if (notification.autoHide !== false) {
      const timer = setTimeout(() => {
        onDismiss(notification.id)
      }, notification.duration || 5000)

      return () => clearTimeout(timer)
    }
  }, [notification.id, notification.autoHide, notification.duration, onDismiss])

  return (
    <Card className={`p-4 ${colorMap[notification.type]} border-l-4`}>
      <div className="flex items-start gap-3">
        <Icon className={`h-5 w-5 mt-0.5 flex-shrink-0 ${iconColorMap[notification.type]}`} />

        <div className="flex-1 min-w-0">
          <h4 className="font-medium text-sm">{notification.title}</h4>
          <p className="text-sm mt-1 opacity-90">{notification.message}</p>

          {notification.action && (
            <div className="mt-3">
              {notification.action.href ? (
                <Link href={notification.action.href}>
                  <Button size="sm" variant="outline" className="h-8 text-xs">
                    {notification.action.label}
                  </Button>
                </Link>
              ) : (
                <Button
                  size="sm"
                  variant="outline"
                  className="h-8 text-xs"
                  onClick={notification.action.onClick}
                >
                  {notification.action.label}
                </Button>
              )}
            </div>
          )}
        </div>

        <Button
          variant="ghost"
          size="icon"
          className="h-6 w-6 opacity-70 hover:opacity-100"
          onClick={() => onDismiss(notification.id)}
        >
          <LuX className="h-4 w-4" />
        </Button>
      </div>
    </Card>
  )
}

interface NotificationContainerProps {
  notifications: Notification[]
  onDismiss: (id: string) => void
}

export function NotificationContainer({ notifications, onDismiss }: NotificationContainerProps) {
  if (notifications.length === 0) return null

  return (
    <div className="fixed top-4 right-4 z-50 space-y-2 max-w-sm w-full">
      {notifications.map((notification) => (
        <NotificationItem
          key={notification.id}
          notification={notification}
          onDismiss={onDismiss}
        />
      ))}
    </div>
  )
}

// Hook for managing notifications
export function useNotifications() {
  const [notifications, setNotifications] = useState<Notification[]>([])

  const addNotification = (notification: Omit<Notification, 'id'>) => {
    const id = Math.random().toString(36).substr(2, 9)
    setNotifications(prev => [...prev, { ...notification, id }])
    return id
  }

  const dismissNotification = (id: string) => {
    setNotifications(prev => prev.filter(n => n.id !== id))
  }

  const clearAll = () => {
    setNotifications([])
  }

  return {
    notifications,
    addNotification,
    dismissNotification,
    clearAll
  }
}

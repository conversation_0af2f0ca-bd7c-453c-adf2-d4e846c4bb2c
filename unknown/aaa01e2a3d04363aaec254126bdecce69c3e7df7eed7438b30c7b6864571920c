import { useState, useCallback, useMemo } from 'react'

export interface BulkOperationsState {
  selectedItems: Set<number>
  isSelectAllChecked: boolean
  isSelectAllIndeterminate: boolean
  isBulkMode: boolean
}

export interface BulkOperationsActions {
  toggleItem: (itemId: number) => void
  toggleSelectAll: (allItemIds: number[]) => void
  clearSelection: () => void
  selectItems: (itemIds: number[]) => void
  isItemSelected: (itemId: number) => boolean
  enterBulkMode: () => void
  exitBulkMode: () => void
  selectItemAndEnterBulkMode: (itemId: number) => void
}

export interface BulkOperationsReturn extends BulkOperationsState, BulkOperationsActions {
  selectedCount: number
  hasSelection: boolean
}

export function useBulkOperations(): BulkOperationsReturn {
  const [selectedItems, setSelectedItems] = useState<Set<number>>(new Set())
  const [isBulkMode, setIsBulkMode] = useState(false)

  const selectedCount = selectedItems.size
  const hasSelection = selectedCount > 0

  const toggleItem = useCallback((itemId: number) => {
    setSelectedItems(prev => {
      const newSet = new Set(prev)
      if (newSet.has(itemId)) {
        newSet.delete(itemId)
      } else {
        newSet.add(itemId)
      }
      return newSet
    })
  }, [])

  const toggleSelectAll = useCallback((allItemIds: number[]) => {
    setSelectedItems(prev => {
      const allSelected = allItemIds.every(id => prev.has(id))
      if (allSelected) {
        // Deselect all
        return new Set()
      } else {
        // Select all
        return new Set(allItemIds)
      }
    })
  }, [])

  const clearSelection = useCallback(() => {
    setSelectedItems(new Set())
    setIsBulkMode(false)
  }, [])

  const selectItems = useCallback((itemIds: number[]) => {
    setSelectedItems(new Set(itemIds))
  }, [])

  const isItemSelected = useCallback((itemId: number) => {
    return selectedItems.has(itemId)
  }, [selectedItems])

  const enterBulkMode = useCallback(() => {
    setIsBulkMode(true)
  }, [])

  const exitBulkMode = useCallback(() => {
    setIsBulkMode(false)
    setSelectedItems(new Set())
  }, [])

  const selectItemAndEnterBulkMode = useCallback((itemId: number) => {
    setSelectedItems(new Set([itemId]))
    setIsBulkMode(true)
  }, [])

  const { isSelectAllChecked, isSelectAllIndeterminate } = useMemo(() => {
    // This will be calculated based on current items in the parent component
    return {
      isSelectAllChecked: false,
      isSelectAllIndeterminate: false
    }
  }, [])

  return {
    selectedItems,
    selectedCount,
    hasSelection,
    isBulkMode,
    isSelectAllChecked,
    isSelectAllIndeterminate,
    toggleItem,
    toggleSelectAll,
    clearSelection,
    selectItems,
    isItemSelected,
    enterBulkMode,
    exitBulkMode,
    selectItemAndEnterBulkMode
  }
}

export function calculateSelectAllState(
  selectedItems: Set<number>,
  allItemIds: number[]
): { isSelectAllChecked: boolean; isSelectAllIndeterminate: boolean } {
  const selectedCount = selectedItems.size
  const totalCount = allItemIds.length
  const selectedFromCurrentItems = allItemIds.filter(id => selectedItems.has(id)).length

  if (selectedFromCurrentItems === 0) {
    return { isSelectAllChecked: false, isSelectAllIndeterminate: false }
  } else if (selectedFromCurrentItems === totalCount) {
    return { isSelectAllChecked: true, isSelectAllIndeterminate: false }
  } else {
    return { isSelectAllChecked: false, isSelectAllIndeterminate: true }
  }
}

import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/db'

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    const customerId = parseInt(id)
    
    if (isNaN(customerId)) {
      return NextResponse.json(
        { error: 'Invalid customer ID' },
        { status: 400 }
      )
    }

    const customer = await prisma.customer.findUnique({
      where: { id: customerId },
      include: {
        _count: {
          select: {
            orders: true,
          }
        }
      }
    })

    if (!customer) {
      return NextResponse.json(
        { error: 'Customer not found' },
        { status: 404 }
      )
    }

    // Calculate additional metrics
    const toBuyCount = await prisma.order.count({
      where: {
        customerId: customer.id,
        isBought: false
      }
    })

    const toPackCount = await prisma.order.count({
      where: {
        customerId: customer.id,
        isBought: true,
        packingStatus: 'Not Packed'
      }
    })

    // Calculate financial metrics from orders
    const orders = await prisma.order.findMany({
      where: { customerId: customer.id }
    })

    const totalSpent = orders.reduce((sum, order) => {
      return sum + (order.quantity * order.customerPrice)
    }, 0)

    const averageOrderValue = orders.length > 0 ? totalSpent / orders.length : 0

    // Calculate appropriate loyalty tier based on spending and order count
    const calculateLoyaltyTier = (totalSpent: number, totalOrders: number) => {
      if (totalSpent >= 500000 && totalOrders >= 20) return 'DIAMOND'  // ₱500,000+ and 20+ orders
      if (totalSpent >= 200000 && totalOrders >= 10) return 'PLATINUM' // ₱200,000+ and 10+ orders
      if (totalSpent >= 100000 && totalOrders >= 5) return 'GOLD'      // ₱100,000+ and 5+ orders
      if (totalSpent >= 50000 && totalOrders >= 3) return 'SILVER'     // ₱50,000+ and 3+ orders
      return 'BRONZE'                                                   // Default tier
    }

    const calculatedLoyaltyTier = calculateLoyaltyTier(totalSpent, orders.length)

    // Return customer with calculated metrics and updated loyalty tier
    const customerWithMetrics = {
      ...customer,
      _count: {
        ...customer._count,
        toBuy: toBuyCount,
        toPack: toPackCount
      },
      totalSpent,
      averageOrderValue,
      loyaltyTier: calculatedLoyaltyTier // Use calculated tier instead of database value
    }

    return NextResponse.json(customerWithMetrics)
  } catch (error) {
    console.error('Error fetching customer:', error)
    return NextResponse.json(
      { error: 'Error fetching customer' },
      { status: 500 }
    )
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    const customerId = parseInt(id)
    
    if (isNaN(customerId)) {
      return NextResponse.json(
        { error: 'Invalid customer ID' },
        { status: 400 }
      )
    }

    const updateData = await request.json()
    
    if (!updateData.name) {
      return NextResponse.json(
        { error: 'Customer name is required' },
        { status: 400 }
      )
    }

    // Check if customer exists
    const existingCustomer = await prisma.customer.findUnique({
      where: { id: customerId }
    })

    if (!existingCustomer) {
      return NextResponse.json(
        { error: 'Customer not found' },
        { status: 404 }
      )
    }

    // Update the customer
    const updatedCustomer = await prisma.customer.update({
      where: { id: customerId },
      data: {
        name: updateData.name,
        customerType: updateData.customerType || 'INDIVIDUAL',
        status: updateData.status || 'ACTIVE',
        segment: updateData.segment || 'REGULAR',
        loyaltyTier: updateData.loyaltyTier || 'BRONZE',
        email: updateData.email || null,
        phone: updateData.phone || null,
        alternatePhone: updateData.alternatePhone || null,
        website: updateData.website || null,
        address: updateData.address || null,
        city: updateData.city || null,
        state: updateData.state || null,
        postalCode: updateData.postalCode || null,
        country: updateData.country || 'Philippines',
        businessName: updateData.businessName || null,
        taxId: updateData.taxId || null,
        businessType: updateData.businessType || null,
        preferredDeliveryMethod: updateData.preferredDeliveryMethod || null,
        preferredPaymentMethod: updateData.preferredPaymentMethod || null,
        creditLimit: updateData.creditLimit || 0,
        paymentTerms: updateData.paymentTerms || 30,
        discountRate: updateData.discountRate || 0,
        assignedSalesRep: updateData.assignedSalesRep || null,
        accountManager: updateData.accountManager || null,
        referredBy: updateData.referredBy || null,
        notes: updateData.notes || null,
        internalNotes: updateData.internalNotes || null,
      },
      include: {
        _count: {
          select: {
            orders: true,
          }
        }
      }
    })

    return NextResponse.json(updatedCustomer)
  } catch (error) {
    console.error('Error updating customer:', error)
    return NextResponse.json(
      { error: 'Error updating customer' },
      { status: 500 }
    )
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    const customerId = parseInt(id)
    
    if (isNaN(customerId)) {
      return NextResponse.json(
        { error: 'Invalid customer ID' },
        { status: 400 }
      )
    }

    // Check if customer exists and has orders
    const customer = await prisma.customer.findUnique({
      where: { id: customerId },
      include: {
        _count: {
          select: {
            orders: true,
          }
        }
      }
    })

    if (!customer) {
      return NextResponse.json(
        { error: 'Customer not found' },
        { status: 404 }
      )
    }

    if (customer._count.orders > 0) {
      return NextResponse.json(
        { error: 'Cannot delete customer with existing orders' },
        { status: 400 }
      )
    }

    // Delete the customer
    await prisma.customer.delete({
      where: { id: customerId }
    })

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error('Error deleting customer:', error)
    return NextResponse.json(
      { error: 'Error deleting customer' },
      { status: 500 }
    )
  }
}

'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { CustomerForm } from '@/components/forms/customer-form'
import { useAppStore } from '@/lib/store'

export default function NewCustomerPage() {
  const router = useRouter()
  const { addCustomer } = useAppStore()
  const [isLoading, setIsLoading] = useState(false)

  const handleSubmit = async (data: Record<string, unknown>) => {
    try {
      setIsLoading(true)

      const response = await fetch('/api/enhanced/customers', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to create customer')
      }

      const newCustomer = await response.json()
      addCustomer(newCustomer)

      router.push('/customers')
    } catch (error) {
      console.error('Error creating customer:', error)
      // You might want to show a toast notification here
      alert(error instanceof Error ? error.message : 'Failed to create customer')
    } finally {
      setIsLoading(false)
    }
  }

  const handleCancel = () => {
    router.back()
  }

  return (
    <div className="space-y-6 py-4">
      <div>
        <h1 className="text-2xl font-semibold tracking-tight">Add New Customer</h1>
        <p className="text-muted-foreground">
          Add a new customer to track orders for specific people.
        </p>
      </div>

      <CustomerForm
        onSubmit={handleSubmit}
        onCancel={handleCancel}
        isLoading={isLoading}
      />
    </div>
  )
}

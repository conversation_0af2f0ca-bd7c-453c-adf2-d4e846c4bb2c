'use client'

import { useState, useMemo } from 'react'
import { Card } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { LuChevronDown, LuChevronUp, LuPackage, LuShoppingCart, LuBox, LuTruck } from 'react-icons/lu'
import { useScroll } from '@/hooks/use-scroll'

interface Order {
  id: number
  productName: string
  quantity: number
  storePrice: number
  pasabuyFee: number
  customerPrice: number
  isBought: boolean
  packingStatus: string
}

interface AnimatedSummaryCardProps {
  orders: Order[]
  className?: string
}

interface StatusCounts {
  pending: number
  bought: number
  packed: number
  delivered: number
  total: number
}

// Status determination logic
function getOrderStatus(order: Order): 'pending' | 'bought' | 'packed' | 'delivered' {
  if (!order.isBought) return 'pending'
  if (order.packingStatus === 'Packed') return 'packed'
  if (order.isBought && order.packingStatus !== 'Packed') return 'bought'
  return 'pending'
}

// Calculate status counts
function calculateStatusCounts(orders: Order[]): StatusCounts {
  const counts = orders.reduce(
    (acc, order) => {
      const status = getOrderStatus(order)
      acc[status]++
      return acc
    },
    { pending: 0, bought: 0, packed: 0, delivered: 0, total: orders.length }
  )
  return counts
}

// Format currency
function formatCurrency(amount: number): string {
  return new Intl.NumberFormat('en-PH', {
    style: 'currency',
    currency: 'PHP',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(amount)
}

export function AnimatedSummaryCard({ orders, className = '' }: AnimatedSummaryCardProps) {
  // Only use scroll for sticky behavior, not for auto-collapse
  const scroll = useScroll({ threshold: 80 })
  const [isMinimized, setIsMinimized] = useState(false)

  // Memoize calculations to ensure they update when orders change
  const statusCounts = useMemo(() => calculateStatusCounts(orders), [orders])
  const totalValue = useMemo(() =>
    orders.reduce((acc, order) => acc + (order.storePrice * order.quantity), 0),
    [orders]
  )

  // Manual toggle only - no automatic scroll-based state changes
  const handleToggle = () => {
    setIsMinimized(!isMinimized)
  }

  const statusItems = [
    { label: 'Pending', count: statusCounts.pending, icon: LuShoppingCart, color: 'text-muted-foreground' },
    { label: 'Bought', count: statusCounts.bought, icon: LuPackage, color: 'text-emerald-600' },
    { label: 'Packed', count: statusCounts.packed, icon: LuBox, color: 'text-blue-600' },
    { label: 'Delivered', count: statusCounts.delivered, icon: LuTruck, color: 'text-purple-600' }
  ]

  // Determine if card should be sticky based on scroll position
  const isSticky = scroll.isScrolled

  return (
    <Card
      className={`
        transition-all duration-300 ease-in-out
        ${isSticky ? 'sticky top-16 z-30 bg-background/95 backdrop-blur-sm shadow-md' : ''}
        ${isMinimized ? 'p-2' : 'p-3'}
        ${className}
      `}
    >
      <div className="space-y-2">
        {/* Header with toggle */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <h2 className={`font-semibold transition-all duration-300 ${isMinimized ? 'text-sm' : 'text-base'}`}>
              Orders Overview
            </h2>
            <Badge variant="outline" className="text-xs">
              {statusCounts.total}
            </Badge>
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={handleToggle}
            className="h-6 w-6 p-0 hover:bg-accent/50"
            aria-label={isMinimized ? 'Expand summary' : 'Minimize summary'}
          >
            {isMinimized ? (
              <LuChevronDown className="h-3 w-3" />
            ) : (
              <LuChevronUp className="h-3 w-3" />
            )}
          </Button>
        </div>

        {/* Minimized view - single row with key metrics */}
        {isMinimized ? (
          <div className="flex items-center justify-between text-xs">
            <div className="flex items-center gap-3">
              {statusItems.map((status) => {
                const Icon = status.icon
                return status.count > 0 ? (
                  <div key={status.label} className="flex items-center gap-1">
                    <Icon className={`h-3 w-3 ${status.color}`} />
                    <span className="font-medium">{status.count}</span>
                  </div>
                ) : null
              })}
            </div>
            <span className="text-muted-foreground">
              {formatCurrency(totalValue)}
            </span>
          </div>
        ) : (
          /* Full view - existing layout but more compact */
          <div className="space-y-3">
            {/* Status breakdown */}
            <div className="grid grid-cols-2 lg:grid-cols-4 gap-2">
              {statusItems.map((status) => {
                const Icon = status.icon
                return (
                  <div key={status.label} className="flex items-center gap-2 p-2 rounded-lg border bg-muted/30">
                    <div className="p-1.5 rounded-md bg-muted">
                      <Icon className={`h-3 w-3 ${status.color}`} />
                    </div>
                    <div className="flex-1 min-w-0">
                      <span className="text-base font-semibold">{status.count}</span>
                      <p className="text-xs text-muted-foreground truncate">{status.label}</p>
                    </div>
                  </div>
                )
              })}
            </div>

            {/* Quick total value */}
            <div className="flex items-center justify-between text-xs text-muted-foreground pt-1.5 border-t">
              <span>Total Value: {formatCurrency(totalValue)}</span>
              <span>
                Completion: {statusCounts.total > 0 ? Math.round(((statusCounts.packed + statusCounts.delivered) / statusCounts.total) * 100) : 0}%
              </span>
            </div>
          </div>
        )}
      </div>
    </Card>
  )
}

import { NextRequest, NextResponse } from 'next/server'
import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const date = searchParams.get('date') // Format: YYYY-MM-DD

    // Use provided date or default to today
    const targetDate = date ? new Date(date) : new Date()
    const startOfDay = new Date(targetDate)
    startOfDay.setHours(0, 0, 0, 0)

    const endOfDay = new Date(targetDate)
    endOfDay.setHours(23, 59, 59, 999)

    // Find all customers with packed orders from the target date
    const customersWithPackedOrders = await prisma.customer.findMany({
      include: {
        orders: {
          where: {
            isBought: true,
            packingStatus: 'Packed',
            updatedAt: {
              gte: startOfDay,
              lte: endOfDay
            },
            // Exclude orders already in invoices
            NOT: {
              invoiceItems: {
                some: {}
              }
            }
          },
          include: {
            storeCode: true
          }
        }
      }
    })

    // Filter to only customers that have packed orders
    const eligibleCustomers = customersWithPackedOrders.filter(customer => customer.orders.length > 0)

    const summary = eligibleCustomers.map(customer => ({
      customerId: customer.id,
      customerName: customer.name,
      orderCount: customer.orders.length,
      totalValue: customer.orders.reduce((sum, order) => sum + (order.customerPrice * order.quantity), 0),
      orders: customer.orders.map(order => ({
        id: order.id,
        productName: order.productName,
        quantity: order.quantity,
        customerPrice: order.customerPrice,
        totalPrice: order.customerPrice * order.quantity
      }))
    }))

    return NextResponse.json({
      date: targetDate.toISOString().split('T')[0],
      eligibleCustomers: summary.length,
      totalOrders: summary.reduce((sum, c) => sum + c.orderCount, 0),
      totalValue: summary.reduce((sum, c) => sum + c.totalValue, 0),
      customers: summary
    })
  } catch (error) {
    console.error('Error checking daily batch status:', error)
    return NextResponse.json(
      { error: 'Error checking daily batch status' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { date, customerId, triggerType = 'daily-batch' } = body

    // Use provided date or default to today
    const targetDate = date ? new Date(date) : new Date()
    const startOfDay = new Date(targetDate)
    startOfDay.setHours(0, 0, 0, 0)

    const endOfDay = new Date(targetDate)
    endOfDay.setHours(23, 59, 59, 999)

    let customersToProcess = []

    if (customerId) {
      // Process specific customer
      const customer = await prisma.customer.findUnique({
        where: { id: customerId },
        include: {
          orders: {
            where: {
              isBought: true,
              packingStatus: 'Packed',
              updatedAt: {
                gte: startOfDay,
                lte: endOfDay
              },
              NOT: {
                invoiceItems: {
                  some: {}
                }
              }
            },
            include: {
              storeCode: true
            }
          }
        }
      })

      if (customer && customer.orders.length > 0) {
        customersToProcess = [customer]
      }
    } else {
      // Process all eligible customers
      const customers = await prisma.customer.findMany({
        include: {
          orders: {
            where: {
              isBought: true,
              packingStatus: 'Packed',
              updatedAt: {
                gte: startOfDay,
                lte: endOfDay
              },
              NOT: {
                invoiceItems: {
                  some: {}
                }
              }
            },
            include: {
              storeCode: true
            }
          }
        }
      })

      customersToProcess = customers.filter(customer => customer.orders.length > 0)
    }

    if (customersToProcess.length === 0) {
      return NextResponse.json({
        success: true,
        message: 'No eligible orders found for batch processing',
        invoicesCreated: 0
      })
    }

    const createdInvoices = []

    // Process each customer
    for (const customer of customersToProcess) {
      try {
        // Get current invoice count for numbering
        const invoiceCount = await prisma.invoice.count()
        const invoiceNumber = `INV-${String(invoiceCount + 1).padStart(6, '0')}`

        // Calculate totals
        let subtotal = 0
        const invoiceItemsData = customer.orders.map((order) => {
          const quantity = order.quantity
          const unitPrice = order.customerPrice
          const totalPrice = quantity * unitPrice

          subtotal += totalPrice

          return {
            orderId: order.id,
            quantity,
            unitPrice,
            totalPrice
          }
        })

        const total = subtotal
        const dueDate = new Date()
        dueDate.setDate(dueDate.getDate() + 30) // 30 days from now

        // Create invoice with transaction
        const invoice = await prisma.invoice.create({
          data: {
            invoiceNumber,
            customerId: customer.id,
            status: 'DRAFT',
            subtotal,
            total,
            dueDate,
            notes: `Daily batch generated invoice for ${targetDate.toLocaleDateString()} (${triggerType})`,
            invoiceItems: {
              create: invoiceItemsData
            }
          },
          include: {
            customer: true,
            invoiceItems: {
              include: {
                order: true
              }
            }
          }
        })

        createdInvoices.push(invoice)
        console.log(`Daily batch created invoice ${invoice.invoiceNumber} for customer ${customer.name} with ${customer.orders.length} orders`)
      } catch (error) {
        console.error(`Error creating invoice for customer ${customer.name}:`, error)
        // Continue with other customers even if one fails
      }
    }

    return NextResponse.json({
      success: true,
      message: `Successfully created ${createdInvoices.length} invoice${createdInvoices.length !== 1 ? 's' : ''} from daily batch`,
      invoicesCreated: createdInvoices.length,
      invoices: createdInvoices.map(inv => ({
        id: inv.id,
        invoiceNumber: inv.invoiceNumber,
        customerName: inv.customer?.name,
        total: inv.total,
        orderCount: inv.invoiceItems.length
      }))
    }, { status: 201 })
  } catch (error) {
    console.error('Error processing daily batch:', error)
    return NextResponse.json(
      { error: 'Error processing daily batch' },
      { status: 500 }
    )
  }
}

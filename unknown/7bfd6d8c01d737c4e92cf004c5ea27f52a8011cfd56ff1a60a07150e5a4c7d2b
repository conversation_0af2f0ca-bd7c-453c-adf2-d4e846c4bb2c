@import "tailwindcss";

@custom-variant dark (&:is(.dark *));

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}

:root {
  --radius: 0.625rem;
  --background: oklch(1 0 0);
  --foreground: oklch(0.145 0 0);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.145 0 0);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.145 0 0);
  --primary: oklch(0.205 0 0);
  --primary-foreground: oklch(0.985 0 0);
  --secondary: oklch(0.97 0 0);
  --secondary-foreground: oklch(0.205 0 0);
  --muted: oklch(0.97 0 0);
  --muted-foreground: oklch(0.556 0 0);
  --accent: oklch(0.97 0 0);
  --accent-foreground: oklch(0.205 0 0);
  --destructive: oklch(0.577 0.245 27.325);
  --border: oklch(0.922 0 0);
  --input: oklch(0.922 0 0);
  --ring: oklch(0.708 0 0);
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.145 0 0);
  --sidebar-primary: oklch(0.205 0 0);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.97 0 0);
  --sidebar-accent-foreground: oklch(0.205 0 0);
  --sidebar-border: oklch(0.922 0 0);
  --sidebar-ring: oklch(0.708 0 0);
}

.dark {
  /* Softer, more comfortable dark mode colors */
  --background: oklch(0.12 0 0);
  --foreground: oklch(0.94 0 0);
  --card: oklch(0.16 0 0);
  --card-foreground: oklch(0.94 0 0);
  --popover: oklch(0.16 0 0);
  --popover-foreground: oklch(0.94 0 0);
  --primary: oklch(0.85 0 0);
  --primary-foreground: oklch(0.16 0 0);
  --secondary: oklch(0.22 0 0);
  --secondary-foreground: oklch(0.94 0 0);
  --muted: oklch(0.22 0 0);
  --muted-foreground: oklch(0.65 0 0);
  --accent: oklch(0.20 0 0);
  --accent-foreground: oklch(0.94 0 0);
  --destructive: oklch(0.65 0.15 20);
  --border: oklch(0.3 0 0);
  --input: oklch(0.25 0 0);
  --ring: oklch(0.5 0 0);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.16 0 0);
  --sidebar-foreground: oklch(0.94 0 0);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.94 0 0);
  --sidebar-accent: oklch(0.22 0 0);
  --sidebar-accent-foreground: oklch(0.94 0 0);
  --sidebar-border: oklch(0.3 0 0);
  --sidebar-ring: oklch(0.5 0 0);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }

  /* Smooth scrolling behavior */
  html {
    scroll-behavior: smooth;
  }
}

/* Mobile-specific responsive utilities */
@layer utilities {
  /* Extra small breakpoint for mobile-specific styling */
  @media (min-width: 375px) {
    .xs\:inline {
      display: inline;
    }
    .xs\:hidden {
      display: none;
    }
    .xs\:block {
      display: block;
    }
    .xs\:flex {
      display: flex;
    }
  }

  /* Enhanced touch targets for mobile */
  .touch-target {
    min-width: 44px;
    min-height: 44px;
  }

  /* Mobile-optimized button spacing */
  .mobile-button-group {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
  }

  .mobile-button-group > * {
    flex: 1;
    min-height: 44px;
  }

  /* Improved mobile form layouts */
  .mobile-form-row {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
  }

  @media (min-width: 640px) {
    .mobile-form-row {
      flex-direction: row;
      align-items: center;
    }
  }

  /* Better mobile navigation spacing */
  .mobile-nav-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 0.5rem;
    min-width: 44px;
    min-height: 44px;
    border-radius: 0.5rem;
    transition: all 0.2s ease-in-out;
  }

  .mobile-nav-item:hover {
    background-color: hsl(var(--accent) / 0.5);
  }

  .mobile-nav-item:active {
    background-color: hsl(var(--accent) / 0.7);
    transform: scale(0.95);
  }

  /* Dark mode specific improvements */
  .dark .shadow-md {
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -1px rgba(0, 0, 0, 0.2);
  }

  .dark .shadow-lg {
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.3), 0 4px 6px -2px rgba(0, 0, 0, 0.2);
  }

  /* Improved dark mode card styling */
  .dark .card-enhanced {
    background: oklch(0.17 0 0);
    border: 1px solid oklch(0.25 0 0);
  }

  .dark .card-enhanced:hover {
    background: oklch(0.19 0 0);
    border-color: oklch(0.3 0 0);
  }

  /* Better dark mode text contrast */
  .dark .text-enhanced {
    color: oklch(0.9 0 0);
  }

  .dark .text-muted-enhanced {
    color: oklch(0.7 0 0);
  }

  /* Scroll animation utilities */
  .scroll-smooth {
    scroll-behavior: smooth;
  }

  .transform-gpu {
    transform: translateZ(0);
  }

  /* Enhanced scroll animations for better performance */
  .scroll-animate {
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1),
                opacity 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    will-change: transform, opacity;
  }

  .scroll-animate-fast {
    transition: transform 0.2s cubic-bezier(0.4, 0, 0.2, 1),
                opacity 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    will-change: transform, opacity;
  }

  /* Simplified navigation animations */
  .nav-smooth {
    transition: transform 0.3s ease-in-out, opacity 0.3s ease-in-out;
    will-change: transform, opacity;
    backface-visibility: hidden;
    -webkit-backface-visibility: hidden;
  }

  .fab-smooth {
    transition: transform 0.3s ease-in-out, opacity 0.3s ease-in-out, scale 0.3s ease-in-out;
    will-change: transform, opacity, scale;
    backface-visibility: hidden;
    -webkit-backface-visibility: hidden;
  }

  /* Prevent scroll animation flickering */
  .scroll-stable {
    transform: translateZ(0);
    -webkit-transform: translateZ(0);
    backface-visibility: hidden;
    -webkit-backface-visibility: hidden;
    perspective: 1000px;
    -webkit-perspective: 1000px;
  }

  /* Smooth scroll behavior for better UX */
  @media (prefers-reduced-motion: no-preference) {
    html {
      scroll-behavior: smooth;
    }
  }

  /* Respect user's motion preferences */
  @media (prefers-reduced-motion: reduce) {
    .nav-smooth,
    .fab-smooth,
    .transition-all {
      transition: none !important;
      animation: none !important;
    }
  }
}

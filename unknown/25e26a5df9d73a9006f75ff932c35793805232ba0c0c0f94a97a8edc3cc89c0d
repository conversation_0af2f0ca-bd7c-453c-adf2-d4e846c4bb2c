'use client'

import { useState } from 'react'
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, DialogTrigger, DialogTitle, DialogVisuallyHidden } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { LuX, LuUpload } from 'react-icons/lu'

interface ImagePreviewProps {
  src: string
  alt: string
  className?: string
  showChangeButton?: boolean
  onChangeImage?: () => void
  onRemoveImage?: () => void
  isEditing?: boolean
  initialSize?: 'full' | 'small' // New prop to control initial display size
  useToggleMode?: boolean // New prop to enable toggle functionality
}

export function ImagePreview({
  src,
  alt,
  className = '',
  showChangeButton = false,
  onChangeImage,
  onRemoveImage,
  isEditing = false,
  initialSize = 'full',
  useToggleMode = false
}: ImagePreviewProps) {
  const [isOpen, setIsOpen] = useState(false)
  const [isFullSize, setIsFullSize] = useState(initialSize === 'full')

  const toggleSize = () => {
    setIsFullSize(!isFullSize)
  }

  return (
    <div className={showChangeButton ? "space-y-3" : ""}>
      {useToggleMode ? (
        /* Toggle mode - for item form */
        <div className="relative inline-block">
          <button
            type="button"
            className="block cursor-pointer hover:opacity-80 transition-opacity"
            onClick={toggleSize}
          >
            <img
              src={src}
              alt={alt}
              className={
                className ||
                (isFullSize
                  ? "w-64 h-auto max-h-64 object-contain rounded border"
                  : "w-32 h-32 object-cover rounded border"
                )
              }
            />
          </button>

          {/* Remove button - only show when editing */}
          {isEditing && onRemoveImage && (
            <Button
              type="button"
              variant="destructive"
              size="icon"
              className="absolute -top-2 -right-2 h-6 w-6 z-10"
              onClick={onRemoveImage}
            >
              <LuX className="h-3 w-3" />
            </Button>
          )}

          {/* Size indicator */}
          <div className="absolute bottom-2 right-2 bg-black/50 text-white text-xs px-2 py-1 rounded">
            {isFullSize ? 'Click to minimize' : 'Click to expand'}
          </div>
        </div>
      ) : (
        /* Original dialog mode - for all other parts of the app */
        <div className="relative inline-block">
          <Dialog open={isOpen} onOpenChange={setIsOpen}>
            <DialogTrigger asChild>
              <button
                type="button"
                className="block cursor-pointer hover:opacity-80 transition-opacity"
              >
                <img
                  src={src}
                  alt={alt}
                  className={className || "w-32 h-32 object-cover rounded border"}
                />
              </button>
            </DialogTrigger>

            {/* Full size image dialog */}
            <DialogContent className="max-w-4xl w-full max-h-[90vh] p-0">
              <DialogVisuallyHidden>
                <DialogTitle>Image Preview: {alt}</DialogTitle>
              </DialogVisuallyHidden>
              <div className="relative">
                <img
                  src={src}
                  alt={alt}
                  className="w-full h-auto max-h-[85vh] object-contain"
                />
              </div>
            </DialogContent>
          </Dialog>

          {/* Remove button - only show when editing */}
          {isEditing && onRemoveImage && (
            <Button
              type="button"
              variant="destructive"
              size="icon"
              className="absolute -top-2 -right-2 h-6 w-6"
              onClick={onRemoveImage}
            >
              <LuX className="h-3 w-3" />
            </Button>
          )}
        </div>
      )}

      {/* Change image button - only show when editing */}
      {showChangeButton && onChangeImage && (
        <div>
          <Button
            type="button"
            variant="outline"
            size="sm"
            onClick={onChangeImage}
          >
            <LuUpload className="h-4 w-4 mr-2" />
            Change Image
          </Button>
        </div>
      )}
    </div>
  )
}

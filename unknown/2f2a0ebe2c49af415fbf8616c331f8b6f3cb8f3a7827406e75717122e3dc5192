// Filter type definitions for advanced filtering and sorting

export type TextOperator = 'contains' | 'startsWith' | 'endsWith' | 'equals' | 'notContains'
export type NumberOperator = 'equals' | 'gt' | 'gte' | 'lt' | 'lte' | 'between'
export type DateOperator = 'equals' | 'gt' | 'gte' | 'lt' | 'lte' | 'between'
export type SortDirection = 'asc' | 'desc'

export interface TextFilter {
  value: string
  operator: TextOperator
  caseSensitive?: boolean
}

export interface NumberFilter {
  operator: NumberOperator
  value?: number
  min?: number
  max?: number
}

export interface DateFilter {
  operator: DateOperator
  value?: string | Date
  startDate?: string | Date
  endDate?: string | Date
}

export interface EntityFilter {
  include: number[]
  exclude: number[]
  includeNull?: boolean
}

export interface SortColumn {
  field: string
  direction: SortDirection
  priority: number
}

export interface FilterConfig {
  // Text filters
  productName?: TextFilter
  usageUnit?: TextFilter
  comment?: TextFilter

  // Number filters
  quantity?: NumberFilter
  storePrice?: NumberFilter
  pasabuyFee?: NumberFilter
  customerPrice?: NumberFilter

  // Date filters
  createdAt?: DateFilter
  updatedAt?: DateFilter

  // Entity filters
  customers?: EntityFilter
  storeCodes?: EntityFilter

  // Status filters
  isBought?: boolean | null
  packingStatus?: string[]

  // General search (legacy support)
  search?: string
}

export interface SortConfig {
  columns: SortColumn[]
  defaultSort?: SortColumn
}

export interface PaginationConfig {
  page?: number
  limit?: number
  offset?: number
}

export interface FilterRequest {
  filters?: FilterConfig
  sort?: SortConfig
  pagination?: PaginationConfig
}

export interface FilterResponse<T> {
  data: T[]
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
    hasNext: boolean
    hasPrev: boolean
  }
  appliedFilters: FilterConfig
  appliedSort: SortConfig
}

// Validation schemas for runtime type checking
export const VALID_TEXT_OPERATORS: TextOperator[] = ['contains', 'startsWith', 'endsWith', 'equals', 'notContains']
export const VALID_NUMBER_OPERATORS: NumberOperator[] = ['equals', 'gt', 'gte', 'lt', 'lte', 'between']
export const VALID_DATE_OPERATORS: DateOperator[] = ['equals', 'gt', 'gte', 'lt', 'lte', 'between']
export const VALID_SORT_DIRECTIONS: SortDirection[] = ['asc', 'desc']

export const VALID_ORDER_SORT_FIELDS = [
  'createdAt',
  'productName',
  'storePrice',
  'customerPrice',
  'quantity',
  'isBought',
  'packingStatus',
  'customer.name',
  'storeCode.code'
] as const

export type ValidOrderSortField = typeof VALID_ORDER_SORT_FIELDS[number]

// Error types for filter validation
export class FilterValidationError extends Error {
  constructor(message: string, public field?: string) {
    super(message)
    this.name = 'FilterValidationError'
  }
}

export class QueryBuilderError extends Error {
  constructor(message: string) {
    super(message)
    this.name = 'QueryBuilderError'
  }
}

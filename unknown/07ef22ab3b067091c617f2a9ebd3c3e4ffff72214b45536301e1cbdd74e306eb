'use client'

import { useEffect, useState } from 'react'
import { usePara<PERSON>, useRouter } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { Card } from '@/components/ui/card'

import Link from 'next/link'
import { LuArrowLeft, LuCheckCheck, LuFileText } from 'react-icons/lu'
import { useBulkOperations, calculateSelectAllState } from '@/hooks/use-bulk-operations'
import { BulkActionsBar } from '@/components/bulk/bulk-actions-bar'
import { BulkConfirmationDialog } from '@/components/bulk/bulk-confirmation-dialog'
import { PackingItemCard } from '@/components/orders/packing-order-card'
import { useScrollToTop } from '@/hooks/use-scroll'

type Order = {
  id: number
  productName: string
  quantity: number
  imageFilename: string | null
  storePrice: number
  pasabuyFee: number
  customerPrice: number
  resellerPrice: number // For compatibility with PackingItemCard
  isBought: boolean
  packingStatus: string
  storeCode?: {
    id: number
    code: string
    name: string | null
  } | null
  createdAt: string
  updatedAt: string
}

type Customer = {
  id: number
  name: string
}

export default function PackingDetailPage() {
  const params = useParams()
  const router = useRouter()
  const customerId = parseInt(params.id as string)

  // Automatically scroll to top when page loads
  useScrollToTop()

  const [customer, setCustomer] = useState<Customer | null>(null)
  const [orders, setOrders] = useState<Order[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [autoInvoiceStatus, setAutoInvoiceStatus] = useState<{
    canGenerate: boolean
    packedOrdersCount: number
    isGenerating: boolean
  }>({
    canGenerate: false,
    packedOrdersCount: 0,
    isGenerating: false
  })

  // Bulk operations
  const bulkOps = useBulkOperations()
  const [confirmDialog, setConfirmDialog] = useState<{
    isOpen: boolean
    action: 'bought' | 'packed'
    isLoading: boolean
  }>({
    isOpen: false,
    action: 'packed',
    isLoading: false
  })

  useEffect(() => {
    async function fetchData() {
      try {
        setIsLoading(true)
        setError(null)

        const [customerRes, ordersRes] = await Promise.all([
          fetch('/api/customers'),
          fetch(`/api/orders?customerId=${customerId}&isBought=true&packingStatus=Not Packed`)
        ])

        if (!customerRes.ok || !ordersRes.ok) {
          throw new Error('Failed to fetch data')
        }

        const customersData = await customerRes.json()
        const ordersData = await ordersRes.json()

        const currentCustomer = customersData.find((c: Customer) => c.id === customerId)
        if (!currentCustomer) {
          throw new Error('Customer not found')
        }

        setCustomer(currentCustomer)

        // Handle both legacy and advanced response formats for orders
        if (Array.isArray(ordersData)) {
          // Legacy format - direct array
          setOrders(ordersData)
        } else if (ordersData && ordersData.data && Array.isArray(ordersData.data)) {
          // Advanced format - structured response
          setOrders(ordersData.data)
        } else {
          setOrders([])
        }

        // Check auto-invoice generation status
        await checkAutoInvoiceStatus()
      } catch (err) {
        setError(err instanceof Error ? err.message : 'An error occurred')
      } finally {
        setIsLoading(false)
      }
    }

    if (customerId) {
      fetchData()
    }
  }, [customerId])

  const checkAutoInvoiceStatus = async () => {
    try {
      const response = await fetch(`/api/invoices/auto-generate?customerId=${customerId}`)
      if (response.ok) {
        const data = await response.json()
        setAutoInvoiceStatus({
          canGenerate: data.canGenerate,
          packedOrdersCount: data.packedOrdersCount,
          isGenerating: false
        })
      }
    } catch (error) {
      console.error('Error checking auto-invoice status:', error)
    }
  }

  const handleGenerateInvoice = async () => {
    try {
      setAutoInvoiceStatus(prev => ({ ...prev, isGenerating: true }))

      const response = await fetch('/api/invoices/auto-generate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          customerId,
          triggerType: 'manual'
        }),
      })

      if (response.ok) {
        const result = await response.json()
        if (result.invoice) {
          // Navigate to the generated invoice
          router.push(`/invoices/${result.invoice.id}`)
        } else {
          alert(result.message || 'No items available for invoice generation')
        }
      } else {
        const error = await response.json()
        alert(error.error || 'Failed to generate invoice')
      }
    } catch (error) {
      console.error('Error generating invoice:', error)
      alert('Failed to generate invoice')
    } finally {
      setAutoInvoiceStatus(prev => ({ ...prev, isGenerating: false }))
    }
  }

  const handlePackOrder = async (orderId: number) => {
    try {
      const response = await fetch(`/api/orders/${orderId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ packingStatus: 'Packed' }),
      })

      if (response.ok) {
        // Remove the order from the list since it's now packed
        setOrders(orders.filter(order => order.id !== orderId))

        // Refresh auto-invoice status
        await checkAutoInvoiceStatus()
      } else {
        throw new Error('Failed to update order status')
      }
    } catch (err) {
      console.error('Error packing order:', err)
    }
  }

  const handlePackAll = async () => {
    // Use bulk operations for pack all
    bulkOps.selectItems(orders.map(order => order.id))
    setConfirmDialog({
      isOpen: true,
      action: 'packed',
      isLoading: false
    })
  }

  // Bulk operations handlers
  const handleBulkMarkAsPacked = () => {
    setConfirmDialog({
      isOpen: true,
      action: 'packed',
      isLoading: false
    })
  }

  const handleBulkConfirm = async () => {
    const selectedOrderIds = Array.from(bulkOps.selectedItems)
    if (selectedOrderIds.length === 0) return

    setConfirmDialog(prev => ({ ...prev, isLoading: true }))

    try {
      const updates = { packingStatus: 'Packed' }

      const response = await fetch('/api/orders/bulk', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          orderIds: selectedOrderIds,
          updates
        }),
      })

      if (!response.ok) {
        throw new Error('Failed to update orders')
      }

      const result = await response.json()

      // Remove updated orders from the list since they're now packed
      setOrders(orders.filter(order => !selectedOrderIds.includes(order.id)))

      // Clear selection and close dialog
      bulkOps.clearSelection()
      setConfirmDialog({ isOpen: false, action: 'packed', isLoading: false })

      // Show success message
      console.log(`Successfully packed ${result.updatedCount} orders`)

      // Refresh auto-invoice status
      await checkAutoInvoiceStatus()
    } catch (err) {
      console.error('Error in bulk update:', err)
      setConfirmDialog(prev => ({ ...prev, isLoading: false }))
    }
  }

  const handleBulkCancel = () => {
    setConfirmDialog({ isOpen: false, action: 'packed', isLoading: false })
  }

  // Long press handler for entering bulk mode
  const handleOrderLongPress = (orderId: number) => {
    bulkOps.selectItemAndEnterBulkMode(orderId)
  }

  const handleCardClick = (orderId: number, _event: React.MouseEvent) => {
    // Navigate to order detail page
    router.push(`/orders/${orderId}`)
  }


  const totalOrders = orders.length
  // const totalValue = orders.reduce((sum, order) => sum + (order.quantity * order.customerPrice), 0)
  // const totalProfit = orders.reduce((sum, order) => sum + (order.quantity * order.pasabuyFee), 0)

  if (isLoading) {
    return (
      <div className="space-y-6 py-4">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/3 mb-4"></div>
          <div className="h-4 bg-gray-200 rounded w-1/2"></div>
        </div>
      </div>
    )
  }

  if (error || !customer) {
    return (
      <div className="space-y-6 py-4">
        <div className="flex items-center gap-4">
          <Button variant="outline" size="icon" asChild>
            <Link href="/packing">
              <LuArrowLeft className="h-4 w-4" />
            </Link>
          </Button>
          <h1 className="text-2xl font-semibold tracking-tight">Packing Not Found</h1>
        </div>

        <Card className="p-6">
          <div className="text-center">
            <h2 className="text-sm font-medium text-red-600">Error</h2>
            <p className="text-sm text-muted-foreground mt-1">{error}</p>
            <Button asChild className="mt-4">
              <Link href="/packing">Back to Packing</Link>
            </Button>
          </div>
        </Card>
      </div>
    )
  }

  return (
    <div className="space-y-6 py-4">
      {/* Header */}
      <div className="flex items-center gap-4">
        <Button variant="outline" size="icon" asChild>
          <Link href="/packing">
            <LuArrowLeft className="h-4 w-4" />
          </Link>
        </Button>
        <div className="flex-1">
          <h1 className="text-2xl font-semibold tracking-tight">Packing for {customer.name}</h1>
          <p className="text-muted-foreground">
            {totalOrders} orders ready to pack
          </p>
        </div>
        {totalOrders > 0 && (
          <Button onClick={handlePackAll} className="bg-green-600 hover:bg-green-700">
            <LuCheckCheck className="h-4 w-4 mr-2" />
            Pack All
          </Button>
        )}
      </div>

      {/* Summary */}
      <div className="grid grid-cols-1 md:grid-cols-1 gap-4">
        <Card className="p-4">
          <div className="text-center">
            <p className="text-2xl font-bold text-orange-600">{totalOrders}</p>
            <p className="text-sm text-muted-foreground">Orders to Pack</p>
          </div>
        </Card>
      </div>

      {/* Bulk Actions Bar */}
      {totalOrders > 0 && (bulkOps.isBulkMode || bulkOps.hasSelection) && (
        <BulkActionsBar
          selectedCount={bulkOps.selectedCount}
          totalCount={totalOrders}
          isSelectAllChecked={calculateSelectAllState(bulkOps.selectedItems, orders.map(order => order.id)).isSelectAllChecked}
          isSelectAllIndeterminate={calculateSelectAllState(bulkOps.selectedItems, orders.map(order => order.id)).isSelectAllIndeterminate}
          isBulkMode={bulkOps.isBulkMode}
          onSelectAllChange={() => bulkOps.toggleSelectAll(orders.map(order => order.id))}
          onClearSelection={bulkOps.clearSelection}
          onExitBulkMode={bulkOps.exitBulkMode}
          onMarkAsPacked={handleBulkMarkAsPacked}
          showBoughtAction={false}
          showPackedAction={true}
        />
      )}

      {/* Orders List */}
      {totalOrders === 0 ? (
        <Card className="p-6">
          <div className="text-center">
            <LuCheckCheck className="mx-auto h-12 w-12 text-green-500" />
            <h3 className="text-lg font-medium mt-2 text-green-700">All orders packed!</h3>
            <p className="text-sm text-muted-foreground mt-1">
              All orders for {customer.name} have been successfully packed.
            </p>
            <div className="flex flex-col gap-3 items-center mt-4">
              {autoInvoiceStatus.canGenerate && (
                <div className="text-center">
                  <p className="text-sm text-muted-foreground mb-3">
                    {autoInvoiceStatus.packedOrdersCount} packed orders are ready for invoicing
                  </p>
                  <div className="flex gap-2 justify-center">
                    <Button
                      onClick={handleGenerateInvoice}
                      disabled={autoInvoiceStatus.isGenerating}
                      className="min-h-[44px]"
                    >
                      <LuFileText className="h-5 w-5 mr-2" />
                      {autoInvoiceStatus.isGenerating ? 'Generating...' : 'Generate Invoice Now'}
                    </Button>
                    <Button asChild variant="outline" className="min-h-[44px]">
                      <Link href="/invoices/daily-batch">
                        Daily Batch
                      </Link>
                    </Button>
                  </div>
                  <p className="text-xs text-muted-foreground mt-2">
                    💡 Orders will be automatically included in daily batch processing
                  </p>
                </div>
              )}

              <div className="flex gap-2 justify-center">
                <Button asChild variant="outline" className="min-h-[44px]">
                  <Link href="/packing">Back to Packing</Link>
                </Button>
                <Button asChild className="min-h-[44px]">
                  <Link href={`/customers/${customer.id}`}>View Customer</Link>
                </Button>
              </div>
            </div>
          </div>
        </Card>
      ) : (
        <div className="grid grid-cols-2 gap-2 sm:gap-2.5 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-4">
          {orders.map((order) => (
            <PackingItemCard
              key={order.id}
              item={order}
              isSelected={bulkOps.isItemSelected(order.id)}
              isBulkMode={bulkOps.isBulkMode}
              onToggleSelection={bulkOps.toggleItem}
              onLongPress={handleOrderLongPress}
              onMarkAsPacked={handlePackOrder}
              onCardClick={handleCardClick}
            />
          ))}
        </div>
      )}

      {/* Bulk Confirmation Dialog */}
      <BulkConfirmationDialog
        isOpen={confirmDialog.isOpen}
        onClose={handleBulkCancel}
        onConfirm={handleBulkConfirm}
        action={confirmDialog.action}
        selectedCount={bulkOps.selectedCount}
        isLoading={confirmDialog.isLoading}
      />
    </div>
  )
}

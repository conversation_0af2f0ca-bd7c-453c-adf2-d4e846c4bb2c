'use client'

import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { Card } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Combobox } from '@/components/ui/combobox'
import { Checkbox } from '@/components/ui/checkbox'
import Link from 'next/link'
import { LuArrowLeft, LuPlus, LuMinus, LuShoppingCart } from 'react-icons/lu'
import { Customer, Order } from '@/lib/store'

interface SelectedOrder {
  orderId: number
  order: Order
  quantity: number
}

export default function NewInvoicePage() {
  const [customers, setCustomers] = useState<Customer[]>([])
  const [orders, setOrders] = useState<Order[]>([])
  const [selectedCustomerId, setSelectedCustomerId] = useState<string>('')
  const [selectedOrders, setSelectedOrders] = useState<SelectedOrder[]>([])
  const [dueDate, setDueDate] = useState('')
  const [notes, setNotes] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [isLoadingData, setIsLoadingData] = useState(true)
  const router = useRouter()

  useEffect(() => {
    fetchData()
  }, [])

  useEffect(() => {
    if (selectedCustomerId) {
      fetchCustomerOrders(parseInt(selectedCustomerId))
    } else {
      setOrders([])
      setSelectedOrders([])
    }
  }, [selectedCustomerId])

  const fetchData = async () => {
    try {
      setIsLoadingData(true)
      const response = await fetch('/api/customers')
      if (response.ok) {
        const data = await response.json()
        setCustomers(data)
      }
    } catch (error) {
      console.error('Error fetching customers:', error)
    } finally {
      setIsLoadingData(false)
    }
  }

  const fetchCustomerOrders = async (customerId: number) => {
    try {
      const response = await fetch(`/api/orders?customerId=${customerId}&isBought=true`)
      if (response.ok) {
        const data = await response.json()
        setOrders(data)
      }
    } catch (error) {
      console.error('Error fetching orders:', error)
    }
  }

  const handleOrderToggle = (order: Order, checked: boolean) => {
    if (checked) {
      setSelectedOrders(prev => [...prev, { orderId: order.id, order, quantity: order.quantity }])
    } else {
      setSelectedOrders(prev => prev.filter(so => so.orderId !== order.id))
    }
  }

  const updateOrderQuantity = (orderId: number, quantity: number) => {
    if (quantity < 1) return
    setSelectedOrders(prev =>
      prev.map(so => so.orderId === orderId ? { ...so, quantity } : so)
    )
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-PH', {
      style: 'currency',
      currency: 'PHP',
    }).format(amount)
  }

  const calculateTotal = () => {
    return selectedOrders.reduce((total, so) => total + (so.quantity * so.order.customerPrice), 0)
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!selectedCustomerId || selectedOrders.length === 0) {
      alert('Please select a customer and at least one order')
      return
    }

    try {
      setIsLoading(true)

      const invoiceData = {
        customerId: parseInt(selectedCustomerId),
        orders: selectedOrders.map(so => ({
          orderId: so.orderId,
          quantity: so.quantity
        })),
        dueDate: dueDate || null,
        notes: notes || null
      }

      const response = await fetch('/api/invoices', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(invoiceData),
      })

      if (response.ok) {
        const invoice = await response.json()
        router.push(`/invoices/${invoice.id}`)
      } else {
        const error = await response.json()
        alert(error.error || 'Failed to create invoice')
      }
    } catch (error) {
      console.error('Error creating invoice:', error)
      alert('Failed to create invoice')
    } finally {
      setIsLoading(false)
    }
  }

  const customerOptions = customers.map(customer => ({
    value: customer.id.toString(),
    label: customer.name
  }))

  if (isLoadingData) {
    return (
      <div className="space-y-6 py-4">
        <div className="animate-pulse">
          <div className="h-8 bg-muted/50 rounded w-1/3 mb-4"></div>
          <div className="h-4 bg-muted/50 rounded w-1/2"></div>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6 py-4">
      {/* Header */}
      <div className="flex items-center gap-4">
        <Link href="/invoices">
          <Button variant="outline" size="icon" className="min-w-[44px] min-h-[44px]">
            <LuArrowLeft className="h-5 w-5" />
          </Button>
        </Link>
        <div>
          <h1 className="text-2xl font-bold">Create Invoice</h1>
          <p className="text-muted-foreground">
            Generate a new invoice for a customer
          </p>
        </div>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Customer Selection */}
        <Card className="p-6">
          <h3 className="text-base font-medium mb-4">Invoice Details</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="customer">Customer *</Label>
              <Combobox
                options={customerOptions}
                value={selectedCustomerId}
                onValueChange={setSelectedCustomerId}
                placeholder="Select customer..."
                emptyText="No customers found"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="dueDate">Due Date</Label>
              <Input
                id="dueDate"
                type="date"
                value={dueDate}
                onChange={(e) => setDueDate(e.target.value)}
                className="min-h-[44px]"
              />
            </div>
          </div>
          <div className="mt-4 space-y-2">
            <Label htmlFor="notes">Notes</Label>
            <Input
              id="notes"
              placeholder="Optional notes for the invoice..."
              value={notes}
              onChange={(e) => setNotes(e.target.value)}
              className="min-h-[44px]"
            />
          </div>
        </Card>

        {/* Orders Selection */}
        {selectedCustomerId && (
          <Card className="p-6">
            <h3 className="text-base font-medium mb-4">Select Orders</h3>
            {orders.length === 0 ? (
              <div className="text-center py-8">
                <LuShoppingCart className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                <p className="text-muted-foreground">
                  No bought orders found for this customer
                </p>
              </div>
            ) : (
              <div className="space-y-3">
                {orders.map((order) => {
                  const selectedOrder = selectedOrders.find(so => so.orderId === order.id)
                  const isSelected = !!selectedOrder

                  return (
                    <div key={order.id} className="flex items-center gap-4 p-3 border rounded-lg">
                      <Checkbox
                        checked={isSelected}
                        onCheckedChange={(checked) => handleOrderToggle(order, checked as boolean)}
                      />

                      <div className="flex-1 min-w-0">
                        <h4 className="font-medium truncate">{order.productName}</h4>
                        <p className="text-sm text-muted-foreground">
                          {formatCurrency(order.customerPrice)} each
                        </p>
                      </div>

                      {isSelected && (
                        <div className="flex items-center gap-2">
                          <Button
                            type="button"
                            variant="outline"
                            size="icon"
                            className="h-8 w-8"
                            onClick={() => updateOrderQuantity(order.id, selectedOrder.quantity - 1)}
                            disabled={selectedOrder.quantity <= 1}
                          >
                            <LuMinus className="h-4 w-4" />
                          </Button>
                          <span className="w-8 text-center text-sm font-medium">
                            {selectedOrder.quantity}
                          </span>
                          <Button
                            type="button"
                            variant="outline"
                            size="icon"
                            className="h-8 w-8"
                            onClick={() => updateOrderQuantity(order.id, selectedOrder.quantity + 1)}
                          >
                            <LuPlus className="h-4 w-4" />
                          </Button>
                        </div>
                      )}

                      <div className="text-right">
                        <p className="font-medium">
                          {formatCurrency(isSelected ? selectedOrder.quantity * order.customerPrice : order.customerPrice)}
                        </p>
                      </div>
                    </div>
                  )
                })}
              </div>
            )}
          </Card>
        )}

        {/* Summary */}
        {selectedOrders.length > 0 && (
          <Card className="p-6">
            <h3 className="text-base font-medium mb-4">Invoice Summary</h3>
            <div className="space-y-2">
              <div className="flex justify-between">
                <span>Orders:</span>
                <span>{selectedOrders.length}</span>
              </div>
              <div className="flex justify-between text-lg font-medium">
                <span>Total:</span>
                <span>{formatCurrency(calculateTotal())}</span>
              </div>
            </div>
          </Card>
        )}

        {/* Actions */}
        <div className="flex flex-col sm:flex-row gap-4">
          <Link href="/invoices" className="flex-1">
            <Button variant="outline" className="w-full min-h-[44px]">
              Cancel
            </Button>
          </Link>
          <Button
            type="submit"
            disabled={isLoading || !selectedCustomerId || selectedOrders.length === 0}
            className="flex-1 min-h-[44px]"
          >
            {isLoading ? 'Creating...' : 'Create Invoice'}
          </Button>
        </div>
      </form>
    </div>
  )
}

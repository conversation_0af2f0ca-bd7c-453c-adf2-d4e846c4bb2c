'use client'

import { useEffect, useState } from 'react'
import { usePara<PERSON>, useRouter } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { Card } from '@/components/ui/card'
import { OrderForm } from '@/components/forms/order-form'
import Link from 'next/link'
import { LuArrowLeft } from 'react-icons/lu'
import { useScrollToTop } from '@/hooks/use-scroll'

type Order = {
  id: number
  productName: string
  quantity: number
  usageUnit?: string | null
  comment?: string | null
  imageFilename: string | null
  storePrice: number
  pasabuyFee: number
  customerPrice: number
  isBought: boolean
  packingStatus: string
  storeCodeId: number | null
  customerId: number | null
  storeCode?: {
    id: number
    code: string
    name: string | null
  } | null
  customer?: {
    id: number
    name: string
  } | null
  createdAt: string
  updatedAt: string
}

export default function EditOrderPage() {
  const params = useParams()
  const router = useRouter()
  const orderId = params.id as string

  const [order, setOrder] = useState<Order | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [isSaving, setIsSaving] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // Automatically scroll to top when page loads
  useScrollToTop()

  useEffect(() => {
    async function fetchOrder() {
      try {
        setIsLoading(true)
        setError(null)

        const response = await fetch(`/api/orders/${orderId}`)
        if (!response.ok) {
          if (response.status === 404) {
            throw new Error('Order not found')
          }
          throw new Error('Failed to fetch order')
        }

        const data = await response.json()
        setOrder(data)
      } catch (err) {
        setError(err instanceof Error ? err.message : 'An error occurred')
      } finally {
        setIsLoading(false)
      }
    }

    if (orderId) {
      fetchOrder()
    }
  }, [orderId])

  const handleSubmit = async (formData: {
    productName: string
    quantity: number
    usageUnit?: string
    comment?: string
    storePrice: number
    pasabuyFee: number
    customerPrice: number
    storeCodeId?: string
    customerId?: string
    imageFile?: File
  }) => {
    try {
      setIsSaving(true)
      setError(null)

      // Handle image upload if there's a new image
      let imageFilename = order?.imageFilename
      if (formData.imageFile) {
        const imageFormData = new FormData()
        imageFormData.append('image', formData.imageFile)

        const uploadResponse = await fetch('/api/upload', {
          method: 'POST',
          body: imageFormData,
        })

        if (!uploadResponse.ok) {
          throw new Error('Failed to upload image')
        }

        const uploadResult = await uploadResponse.json()
        imageFilename = uploadResult.filename
      }

      // Update the order
      const updateData = {
        productName: formData.productName,
        quantity: formData.quantity,
        usageUnit: formData.usageUnit || null,
        comment: formData.comment || null,
        storePrice: formData.storePrice,
        pasabuyFee: formData.pasabuyFee,
        customerPrice: formData.customerPrice,
        storeCodeId: formData.storeCodeId ? parseInt(formData.storeCodeId) : null,
        customerId: formData.customerId ? parseInt(formData.customerId) : null,
        ...(imageFilename && { imageFilename }),
      }

      const response = await fetch(`/api/orders/${orderId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updateData),
      })

      if (!response.ok) {
        throw new Error('Failed to update order')
      }

      // Redirect back to orders page
      router.push('/orders')
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred')
    } finally {
      setIsSaving(false)
    }
  }

  const handleCancel = () => {
    router.back()
  }

  const handleDelete = async (orderIdToDelete: number) => {
    try {
      setError(null)

      const response = await fetch(`/api/orders/${orderIdToDelete}`, {
        method: 'DELETE',
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to delete order')
      }

      // Redirect to orders page after successful deletion
      router.push('/orders')
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to delete order')
      // Re-throw to let the form handle the error state
      throw err
    }
  }

  if (isLoading) {
    return (
      <div className="space-y-6 py-4">
        <div className="flex items-center gap-4">
          <Button variant="outline" size="icon" asChild>
            <Link href="/orders">
              <LuArrowLeft className="h-4 w-4" />
            </Link>
          </Button>
          <h1 className="text-2xl font-semibold tracking-tight">Edit Order</h1>
        </div>

        <Card className="p-6">
          <div className="animate-pulse">
            <div className="h-4 bg-muted/50 rounded w-3/4 mb-4"></div>
            <div className="h-4 bg-muted/50 rounded w-1/2 mb-4"></div>
            <div className="h-4 bg-muted/50 rounded w-2/3"></div>
          </div>
        </Card>
      </div>
    )
  }

  if (error || !order) {
    return (
      <div className="space-y-6 py-4">
        <div className="flex items-center gap-4">
          <Button variant="outline" size="icon" asChild>
            <Link href="/orders">
              <LuArrowLeft className="h-4 w-4" />
            </Link>
          </Button>
          <h1 className="text-2xl font-semibold tracking-tight">Edit Order</h1>
        </div>

        <Card className="p-6">
          <div className="text-center">
            <h2 className="text-sm font-medium text-red-600">Error</h2>
            <p className="text-sm text-muted-foreground mt-1">{error}</p>
            <div className="flex gap-2 justify-center mt-4">
              <Button variant="outline" asChild>
                <Link href="/orders">Back to Orders</Link>
              </Button>
              <Button onClick={() => window.location.reload()}>
                Try Again
              </Button>
            </div>
          </div>
        </Card>
      </div>
    )
  }

  return (
    <div className="space-y-6 py-4">
      <div className="flex items-center gap-4">
        <Button variant="outline" size="icon" asChild>
          <Link href="/orders">
            <LuArrowLeft className="h-4 w-4" />
          </Link>
        </Button>
        <div>
          <h1 className="text-2xl font-semibold tracking-tight">Edit Order</h1>
          <p className="text-muted-foreground">Update order details and pricing</p>
        </div>
      </div>

      <OrderForm
        onSubmit={handleSubmit}
        onCancel={handleCancel}
        onDelete={handleDelete}
        initialData={{
          productName: order.productName,
          quantity: order.quantity,
          usageUnit: order.usageUnit || '',
          comment: order.comment || '',
          storePrice: order.storePrice,
          pasabuyFee: order.pasabuyFee,
          customerPrice: order.customerPrice,
          storeCodeId: order.storeCodeId?.toString() || '',
          customerId: order.customerId?.toString() || '',
        }}
        existingImageFilename={order.imageFilename}
        isLoading={isSaving}
        orderId={order.id}
        isEditing={true}
      />
    </div>
  )
}

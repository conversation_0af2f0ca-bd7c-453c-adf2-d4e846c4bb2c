import React, { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'

import { Card } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { FilterConfig, SortConfig } from '@/lib/filter-types'
import { Save, Trash2, Download, X } from 'lucide-react'

interface FilterPreset {
  id: string
  name: string
  description?: string
  filters: FilterConfig
  sort: SortConfig
  createdAt: string
  updatedAt: string
}

interface FilterPresetManagerProps {
  currentFilters: FilterConfig
  currentSort: SortConfig
  onLoadPreset: (filters: FilterConfig, sort: SortConfig) => void
  className?: string
}

export function FilterPresetManager({
  currentFilters,
  currentSort,
  onLoadPreset,
  className
}: FilterPresetManagerProps) {
  const [presets, setPresets] = useState<FilterPreset[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [showSaveForm, setShowSaveForm] = useState(false)
  const [presetName, setPresetName] = useState('')
  const [presetDescription, setPresetDescription] = useState('')

  // Load presets on mount
  useEffect(() => {
    loadPresets()
  }, [])

  const loadPresets = async () => {
    setIsLoading(true)
    setError(null)

    try {
      const response = await fetch('/api/filters')
      if (!response.ok) {
        throw new Error('Failed to load filter presets')
      }

      const data = await response.json()
      setPresets(data.presets || [])
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred')
    } finally {
      setIsLoading(false)
    }
  }

  const savePreset = async () => {
    if (!presetName.trim()) {
      setError('Preset name is required')
      return
    }

    setIsLoading(true)
    setError(null)

    try {
      const response = await fetch('/api/filters', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          name: presetName.trim(),
          description: presetDescription.trim() || undefined,
          filters: currentFilters,
          sort: currentSort
        })
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.details || 'Failed to save preset')
      }

      const newPreset = await response.json()
      setPresets(prev => [...prev, newPreset])
      setShowSaveForm(false)
      setPresetName('')
      setPresetDescription('')
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred')
    } finally {
      setIsLoading(false)
    }
  }

  const deletePreset = async (presetId: string) => {
    if (!confirm('Are you sure you want to delete this preset?')) {
      return
    }

    setIsLoading(true)
    setError(null)

    try {
      const response = await fetch(`/api/filters?id=${presetId}`, {
        method: 'DELETE'
      })

      if (!response.ok) {
        throw new Error('Failed to delete preset')
      }

      setPresets(prev => prev.filter(preset => preset.id !== presetId))
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred')
    } finally {
      setIsLoading(false)
    }
  }

  const loadPreset = (preset: FilterPreset) => {
    onLoadPreset(preset.filters, preset.sort)
  }

  const hasActiveFilters = Object.keys(currentFilters).length > 0 ||
    (currentSort.columns.length > 0 && currentSort.columns[0].field !== 'createdAt')

  const getPresetSummary = (preset: FilterPreset) => {
    const filterCount = Object.keys(preset.filters).length
    const sortCount = preset.sort.columns.length

    const parts = []
    if (filterCount > 0) parts.push(`${filterCount} filter${filterCount !== 1 ? 's' : ''}`)
    if (sortCount > 0) parts.push(`${sortCount} sort${sortCount !== 1 ? 's' : ''}`)

    return parts.join(', ') || 'No filters'
  }

  return (
    <Card className={`p-4 space-y-4 ${className}`}>
      <div className="flex items-center justify-between">
        <h3 className="font-medium text-sm">Filter Presets</h3>
        {hasActiveFilters && (
          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowSaveForm(true)}
            className="h-7 px-2"
          >
            <Save className="h-3 w-3 mr-1" />
            Save
          </Button>
        )}
      </div>

      {error && (
        <div className="text-xs text-destructive bg-destructive/10 p-2 rounded">
          {error}
        </div>
      )}

      {/* Save Form */}
      {showSaveForm && (
        <div className="space-y-2 p-3 border rounded-md bg-muted/50">
          <div className="flex items-center justify-between">
            <Label className="text-xs font-medium">Save Current Filters</Label>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => {
                setShowSaveForm(false)
                setPresetName('')
                setPresetDescription('')
                setError(null)
              }}
              className="h-6 w-6 p-0"
            >
              <X className="h-3 w-3" />
            </Button>
          </div>

          <Input
            placeholder="Preset name"
            value={presetName}
            onChange={(e) => setPresetName(e.target.value)}
            className="h-7 text-xs"
          />

          <Input
            placeholder="Description (optional)"
            value={presetDescription}
            onChange={(e) => setPresetDescription(e.target.value)}
            className="h-7 text-xs"
          />

          <Button
            onClick={savePreset}
            disabled={!presetName.trim() || isLoading}
            className="h-7 w-full text-xs"
          >
            {isLoading ? 'Saving...' : 'Save Preset'}
          </Button>
        </div>
      )}

      {/* Presets List */}
      <div className="space-y-2">
        {isLoading && presets.length === 0 ? (
          <div className="text-xs text-muted-foreground text-center py-2">
            Loading presets...
          </div>
        ) : presets.length === 0 ? (
          <div className="text-xs text-muted-foreground text-center py-2">
            No saved presets
          </div>
        ) : (
          presets.map((preset) => (
            <div
              key={preset.id}
              className="flex items-center justify-between p-2 border rounded-md hover:bg-muted/50 transition-colors"
            >
              <div className="flex-1 min-w-0">
                <div className="flex items-center gap-2">
                  <span className="text-xs font-medium truncate">
                    {preset.name}
                  </span>
                  <Badge variant="outline" className="text-xs px-1 py-0">
                    {getPresetSummary(preset)}
                  </Badge>
                </div>
                {preset.description && (
                  <p className="text-xs text-muted-foreground truncate mt-1">
                    {preset.description}
                  </p>
                )}
              </div>

              <div className="flex items-center gap-1">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => loadPreset(preset)}
                  className="h-6 w-6 p-0"
                  title="Load preset"
                >
                  <Download className="h-3 w-3" />
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => deletePreset(preset.id)}
                  className="h-6 w-6 p-0 text-destructive hover:text-destructive"
                  title="Delete preset"
                >
                  <Trash2 className="h-3 w-3" />
                </Button>
              </div>
            </div>
          ))
        )}
      </div>

      {!hasActiveFilters && !showSaveForm && (
        <div className="text-xs text-muted-foreground text-center py-2">
          Apply some filters to save a preset
        </div>
      )}
    </Card>
  )
}

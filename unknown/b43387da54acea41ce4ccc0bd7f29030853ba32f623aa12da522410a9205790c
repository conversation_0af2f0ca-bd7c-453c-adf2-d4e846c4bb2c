import { NextRequest, NextResponse } from 'next/server'
import { PricingService } from '@/lib/pricing-service'

export async function GET() {
  try {
    const settings = await PricingService.getDefaultPricing()
    return NextResponse.json(settings)
  } catch (error) {
    console.error('Error fetching pricing settings:', error)
    return NextResponse.json(
      { error: 'Error fetching pricing settings' },
      { status: 500 }
    )
  }
}

export async function PUT(request: NextRequest) {
  try {
    const body = await request.json()
    const {
      markupType,
      markupValue,
      serviceFee
    } = body

    // Validation
    if (!markupType || !['PERCENTAGE', 'FIXED_AMOUNT'].includes(markupType)) {
      return NextResponse.json(
        { error: 'Invalid markup type. Must be PERCENTAGE or FIXED_AMOUNT' },
        { status: 400 }
      )
    }

    if (markupValue === undefined || markupValue === null || markupValue < 0) {
      return NextResponse.json(
        { error: 'Markup value must be a non-negative number' },
        { status: 400 }
      )
    }

    if (serviceFee === undefined || serviceFee === null || serviceFee < 0) {
      return NextResponse.json(
        { error: 'Service fee must be a non-negative number' },
        { status: 400 }
      )
    }

    const settings = await PricingService.updateDefaultPricing({
      serviceFee: parseFloat(serviceFee),
      pricingTiers: [
        {
          minPrice: 0,
          maxPrice: null,
          markupType: markupType as 'PERCENTAGE' | 'FIXED_AMOUNT',
          markupValue: parseFloat(markupValue),
          sortOrder: 0
        }
      ]
    })

    return NextResponse.json(settings)
  } catch (error) {
    console.error('Error updating pricing settings:', error)
    return NextResponse.json(
      { error: 'Error updating pricing settings' },
      { status: 500 }
    )
  }
}

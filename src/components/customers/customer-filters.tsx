'use client'

import { useState } from 'react'
import { Card } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Checkbox } from '@/components/ui/checkbox'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { LuX, LuFilter } from 'react-icons/lu'

type CustomerFilters = {
  searchTerm: string
  customerType: string[]
  status: string[]
  segment: string[]
  loyaltyTier: string[]
  city: string
  totalSpentMin: string
  totalSpentMax: string
  hasOrders: boolean | null
}

interface CustomerFiltersProps {
  filters: CustomerFilters
  onFiltersChange: (filters: Partial<CustomerFilters>) => void
  onClose: () => void
}

const customerTypes = [
  { value: 'INDIVIDUAL', label: 'Individual' },
  { value: 'BUSINESS', label: 'Business' },
  { value: 'RESELLER', label: 'Reseller' },
  { value: 'WHOLESALE', label: 'Wholesale' },
  { value: 'GOVERNMENT', label: 'Government' },
  { value: 'NON_PROFIT', label: 'Non-Profit' }
]

const statuses = [
  { value: 'ACTIVE', label: 'Active' },
  { value: 'INACTIVE', label: 'Inactive' },
  { value: 'SUSPENDED', label: 'Suspended' },
  { value: 'PROSPECT', label: 'Prospect' },
  { value: 'ARCHIVED', label: 'Archived' }
]

const segments = [
  { value: 'REGULAR', label: 'Regular' },
  { value: 'VIP', label: 'VIP' },
  { value: 'PREMIUM', label: 'Premium' },
  { value: 'ENTERPRISE', label: 'Enterprise' },
  { value: 'SMALL_BUSINESS', label: 'Small Business' },
  { value: 'BULK_BUYER', label: 'Bulk Buyer' }
]

const loyaltyTiers = [
  { value: 'BRONZE', label: 'Bronze' },
  { value: 'SILVER', label: 'Silver' },
  { value: 'GOLD', label: 'Gold' },
  { value: 'PLATINUM', label: 'Platinum' },
  { value: 'DIAMOND', label: 'Diamond' }
]

export function CustomerFilters({ filters, onFiltersChange, onClose }: CustomerFiltersProps) {
  const [localFilters, setLocalFilters] = useState(filters)

  const handleCheckboxChange = (filterKey: keyof CustomerFilters, value: string, checked: boolean) => {
    const currentArray = localFilters[filterKey] as string[]
    let newArray: string[]
    
    if (checked) {
      newArray = [...currentArray, value]
    } else {
      newArray = currentArray.filter(item => item !== value)
    }
    
    const newFilters = { ...localFilters, [filterKey]: newArray }
    setLocalFilters(newFilters)
    onFiltersChange({ [filterKey]: newArray })
  }

  const handleInputChange = (filterKey: keyof CustomerFilters, value: string) => {
    const newFilters = { ...localFilters, [filterKey]: value }
    setLocalFilters(newFilters)
    onFiltersChange({ [filterKey]: value })
  }

  const handleSelectChange = (filterKey: keyof CustomerFilters, value: string) => {
    const newValue = value === 'all' ? null : value === 'true'
    const newFilters = { ...localFilters, [filterKey]: newValue }
    setLocalFilters(newFilters)
    onFiltersChange({ [filterKey]: newValue })
  }

  const clearAllFilters = () => {
    const clearedFilters: CustomerFilters = {
      searchTerm: '',
      customerType: [],
      status: [],
      segment: [],
      loyaltyTier: [],
      city: '',
      totalSpentMin: '',
      totalSpentMax: '',
      hasOrders: null
    }
    setLocalFilters(clearedFilters)
    onFiltersChange(clearedFilters)
  }

  return (
    <Card className="p-6">
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center gap-2">
          <LuFilter className="h-4 w-4" />
          <h3 className="font-medium">Filter Customers</h3>
        </div>
        <Button variant="ghost" size="sm" onClick={onClose}>
          <LuX className="h-4 w-4" />
        </Button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {/* Customer Type */}
        <div className="space-y-3">
          <Label className="text-sm font-medium">Customer Type</Label>
          <div className="space-y-2">
            {customerTypes.map((type) => (
              <div key={type.value} className="flex items-center space-x-2">
                <Checkbox
                  id={`type-${type.value}`}
                  checked={localFilters.customerType.includes(type.value)}
                  onCheckedChange={(checked) => 
                    handleCheckboxChange('customerType', type.value, checked as boolean)
                  }
                />
                <Label 
                  htmlFor={`type-${type.value}`} 
                  className="text-sm font-normal cursor-pointer"
                >
                  {type.label}
                </Label>
              </div>
            ))}
          </div>
        </div>

        {/* Status */}
        <div className="space-y-3">
          <Label className="text-sm font-medium">Status</Label>
          <div className="space-y-2">
            {statuses.map((status) => (
              <div key={status.value} className="flex items-center space-x-2">
                <Checkbox
                  id={`status-${status.value}`}
                  checked={localFilters.status.includes(status.value)}
                  onCheckedChange={(checked) => 
                    handleCheckboxChange('status', status.value, checked as boolean)
                  }
                />
                <Label 
                  htmlFor={`status-${status.value}`} 
                  className="text-sm font-normal cursor-pointer"
                >
                  {status.label}
                </Label>
              </div>
            ))}
          </div>
        </div>

        {/* Segment */}
        <div className="space-y-3">
          <Label className="text-sm font-medium">Segment</Label>
          <div className="space-y-2">
            {segments.map((segment) => (
              <div key={segment.value} className="flex items-center space-x-2">
                <Checkbox
                  id={`segment-${segment.value}`}
                  checked={localFilters.segment.includes(segment.value)}
                  onCheckedChange={(checked) => 
                    handleCheckboxChange('segment', segment.value, checked as boolean)
                  }
                />
                <Label 
                  htmlFor={`segment-${segment.value}`} 
                  className="text-sm font-normal cursor-pointer"
                >
                  {segment.label}
                </Label>
              </div>
            ))}
          </div>
        </div>

        {/* Loyalty Tier */}
        <div className="space-y-3">
          <Label className="text-sm font-medium">Loyalty Tier</Label>
          <div className="space-y-2">
            {loyaltyTiers.map((tier) => (
              <div key={tier.value} className="flex items-center space-x-2">
                <Checkbox
                  id={`tier-${tier.value}`}
                  checked={localFilters.loyaltyTier.includes(tier.value)}
                  onCheckedChange={(checked) => 
                    handleCheckboxChange('loyaltyTier', tier.value, checked as boolean)
                  }
                />
                <Label 
                  htmlFor={`tier-${tier.value}`} 
                  className="text-sm font-normal cursor-pointer"
                >
                  {tier.label}
                </Label>
              </div>
            ))}
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-6 pt-6 border-t">
        {/* City */}
        <div className="space-y-2">
          <Label htmlFor="city" className="text-sm font-medium">City</Label>
          <Input
            id="city"
            placeholder="Filter by city..."
            value={localFilters.city}
            onChange={(e) => handleInputChange('city', e.target.value)}
          />
        </div>

        {/* Spending Range */}
        <div className="space-y-2">
          <Label className="text-sm font-medium">Total Spent (Min)</Label>
          <Input
            type="number"
            placeholder="0"
            value={localFilters.totalSpentMin}
            onChange={(e) => handleInputChange('totalSpentMin', e.target.value)}
          />
        </div>

        <div className="space-y-2">
          <Label className="text-sm font-medium">Total Spent (Max)</Label>
          <Input
            type="number"
            placeholder="No limit"
            value={localFilters.totalSpentMax}
            onChange={(e) => handleInputChange('totalSpentMax', e.target.value)}
          />
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
        {/* Has Orders */}
        <div className="space-y-2">
          <Label className="text-sm font-medium">Order History</Label>
          <Select 
            value={localFilters.hasOrders === null ? 'all' : localFilters.hasOrders.toString()}
            onValueChange={(value) => handleSelectChange('hasOrders', value)}
          >
            <SelectTrigger>
              <SelectValue placeholder="All customers" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All customers</SelectItem>
              <SelectItem value="true">Has orders</SelectItem>
              <SelectItem value="false">No orders</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Clear Filters */}
        <div className="flex items-end">
          <Button 
            variant="outline" 
            onClick={clearAllFilters}
            className="w-full"
          >
            Clear All Filters
          </Button>
        </div>
      </div>
    </Card>
  )
}

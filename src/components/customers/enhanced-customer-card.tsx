'use client'

import Link from 'next/link'
import { Card } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Avatar } from '@/components/ui/avatar'
import { Button } from '@/components/ui/button'
import { LuStar, LuTrendingUp, LuMail, LuPhone, LuPencil } from 'react-icons/lu'

type EnhancedCustomer = {
  id: number
  name: string
  customerNumber?: string
  customerType: string
  status: string
  segment: string
  loyaltyTier: string
  email?: string
  phone?: string
  city?: string
  totalOrders: number
  totalSpent: number
  averageOrderValue: number
  lastOrderDate?: string
  _count?: {
    orders: number
    toBuy: number
    toPack: number
  }
}

interface EnhancedCustomerCardProps {
  customer: EnhancedCustomer
  getLoyaltyTierColor: (tier: string) => string
  getStatusColor: (status: string) => string
}

export function EnhancedCustomerCard({ 
  customer, 
  getLoyaltyTierColor, 
  getStatusColor 
}: EnhancedCustomerCardProps) {
  const getStatusDotColor = (customer: { _count?: { orders?: number; toBuy?: number; toPack?: number } }) => {
    const { toBuy = 0, toPack = 0, orders = 0 } = customer._count || {}

    if (orders === 0) return 'bg-gray-400'

    const toBuyPercentage = (toBuy / orders) * 100
    const toPackPercentage = (toPack / orders) * 100

    // Red: Majority not bought yet
    if (toBuyPercentage > 75) return 'bg-red-500'

    // Green: Majority bought but not packed
    if (toPackPercentage > 75) return 'bg-green-500'

    // Blue: Majority packed
    if ((orders - toBuy - toPack) / orders > 0.75) return 'bg-blue-500'

    // Default: Mixed status
    return 'bg-yellow-500'
  }

  return (
    <div className="relative group">
      <Link href={`/customers/${customer.id}`} className="block">
        <Card className="p-4 hover:shadow-md hover:bg-accent/50 transition-all duration-200 cursor-pointer focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2">
          <div className="space-y-3">
          {/* Header with Avatar and Status */}
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="relative">
                <Avatar className="h-10 w-10">
                  <div className="w-full h-full bg-gradient-to-br from-blue-400 to-purple-500 flex items-center justify-center text-white text-sm font-medium">
                    {customer.name.charAt(0).toUpperCase()}
                  </div>
                </Avatar>
                <div
                  className={`absolute -bottom-0.5 -right-0.5 w-3 h-3 rounded-full border-2 border-white ${getStatusDotColor(customer)}`}
                  title="Status indicator"
                />
              </div>
              <div className="flex-1 min-w-0">
                <h3 className="font-medium text-base truncate" title={customer.name}>
                  {customer.name}
                </h3>
                {customer.customerNumber && (
                  <p className="text-xs text-muted-foreground">
                    {customer.customerNumber}
                  </p>
                )}
              </div>
            </div>
            {customer.loyaltyTier && (
              <Badge 
                variant="outline" 
                className={`text-xs ${getLoyaltyTierColor(customer.loyaltyTier)}`}
              >
                <LuStar className="h-3 w-3 mr-1" />
                {customer.loyaltyTier}
              </Badge>
            )}
          </div>

          {/* Customer Type and Location */}
          <div className="flex items-center justify-between text-xs text-muted-foreground">
            <span>{customer.customerType.replace('_', ' ')}</span>
            {customer.city && <span>{customer.city}</span>}
          </div>

          {/* Contact Information */}
          <div className="flex items-center gap-3 text-xs">
            {customer.email && (
              <div className="flex items-center gap-1 text-muted-foreground">
                <LuMail className="h-3 w-3" />
                <span className="truncate max-w-[120px]">{customer.email}</span>
              </div>
            )}
            {customer.phone && (
              <div className="flex items-center gap-1 text-muted-foreground">
                <LuPhone className="h-3 w-3" />
                <span>{customer.phone}</span>
              </div>
            )}
          </div>

          {/* Customer Metrics */}
          <div className="space-y-2">
            <div className="flex items-center justify-between text-sm">
              <span className="text-muted-foreground">Orders</span>
              <span className="font-medium">{customer.totalOrders || 0}</span>
            </div>
            <div className="flex items-center justify-between text-sm">
              <span className="text-muted-foreground">Total Spent</span>
              <span className="font-medium">₱{(customer.totalSpent || 0).toLocaleString()}</span>
            </div>
            {customer.averageOrderValue > 0 && (
              <div className="flex items-center justify-between text-sm">
                <span className="text-muted-foreground">Avg Order</span>
                <span className="font-medium">₱{customer.averageOrderValue.toLocaleString()}</span>
              </div>
            )}
          </div>

          {/* Status Badges */}
          <div className="flex items-center gap-2 flex-wrap">
            {customer.status && (
              <Badge 
                variant="outline" 
                className={`text-xs ${getStatusColor(customer.status)}`}
              >
                {customer.status}
              </Badge>
            )}
            {customer.segment && customer.segment !== 'REGULAR' && (
              <Badge variant="secondary" className="text-xs">
                {customer.segment.replace('_', ' ')}
              </Badge>
            )}
            {(customer._count?.toBuy || 0) > 0 && (
              <Badge variant="destructive" className="text-xs">
                {customer._count?.toBuy} to buy
              </Badge>
            )}
            {(customer._count?.toPack || 0) > 0 && (
              <Badge variant="default" className="text-xs">
                {customer._count?.toPack} to pack
              </Badge>
            )}
          </div>

          {/* Performance Indicators */}
          {customer.lastOrderDate && (
            <div className="flex items-center gap-2 text-xs text-muted-foreground pt-2 border-t">
              <LuTrendingUp className="h-3 w-3" />
              <span>Last order: {new Date(customer.lastOrderDate).toLocaleDateString()}</span>
            </div>
          )}
        </div>
      </Card>
    </Link>

    {/* Edit Button Overlay */}
    <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
      <Link href={`/customers/${customer.id}/edit`} onClick={(e) => e.stopPropagation()}>
        <Button
          variant="secondary"
          size="sm"
          className="h-8 w-8 p-0 shadow-md"
          title="Edit customer"
        >
          <LuPencil className="h-3 w-3" />
        </Button>
      </Link>
    </div>
  </div>
  )
}

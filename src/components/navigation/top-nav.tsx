'use client'

import { usePathname } from 'next/navigation'
import { GlobalSearch } from '@/components/search/global-search'
import { Sidebar } from '@/components/navigation/sidebar'
import { Button } from '@/components/ui/button'
import { ThemeToggle } from '@/components/ui/theme-toggle'
import { cn } from '@/lib/utils'
import { LuMenu } from 'react-icons/lu'
import { useHeaderScroll } from '@/hooks/use-scroll'

interface TopNavProps {
  className?: string
}

// Map routes to page titles
const getPageTitle = (pathname: string): string => {
  if (pathname.startsWith('/orders')) return 'Orders'
  if (pathname.startsWith('/buy-list')) return 'Buy List'
  if (pathname.startsWith('/packing')) return 'Packing'
  if (pathname.startsWith('/customers')) return 'Customers'
  if (pathname.startsWith('/stores')) return 'Stores'
  if (pathname.startsWith('/invoices')) return 'Invoices'
  return 'Pasabuy Pal'
}

export function TopNav({ className }: TopNavProps) {
  const pathname = usePathname()
  const { isScrolled, scrollY } = useHeaderScroll()

  // Determine which title to show based on scroll position
  const pageTitle = getPageTitle(pathname)
  const shouldShowPageTitle = isScrolled && scrollY > 100 && pageTitle !== 'Pasabuy Pal'
  const displayTitle = shouldShowPageTitle ? pageTitle : 'Pasabuy Pal'

  return (
    <nav className={cn(
      "sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 transition-all duration-300 ease-in-out",
      isScrolled ? "shadow-sm" : "",
      className
    )}>
      <div className="container mx-auto px-4">
        <div className={cn(
          "flex items-center justify-between transition-all duration-300 ease-in-out",
          isScrolled ? "h-12" : "h-14"
        )}>
          {/* Left side - Hamburger menu and Logo/Title */}
          <div className="flex items-center gap-3">
            <Sidebar>
              <Button variant="ghost" size="icon" className={cn(
                "transition-all duration-300 ease-in-out",
                isScrolled ? "h-8 w-8" : "h-9 w-9"
              )}>
                <LuMenu className={cn(
                  "transition-all duration-300 ease-in-out",
                  isScrolled ? "h-4 w-4" : "h-5 w-5"
                )} />
                <span className="sr-only">Open navigation menu</span>
              </Button>
            </Sidebar>
            <h1 className={cn(
              "font-semibold transition-all duration-300 ease-in-out",
              isScrolled ? "text-base" : "text-lg"
            )}>
              <span className="transition-all duration-300 ease-in-out">
                {displayTitle}
              </span>
            </h1>
          </div>

          {/* Search */}
          <div className={cn(
            "flex-1 mx-4 transition-all duration-300 ease-in-out",
            isScrolled ? "max-w-sm" : "max-w-md"
          )}>
            <GlobalSearch />
          </div>

          {/* Right side - Theme toggle and future features */}
          <div className="flex items-center gap-2">
            <ThemeToggle />
          </div>
        </div>
      </div>
    </nav>
  )
}

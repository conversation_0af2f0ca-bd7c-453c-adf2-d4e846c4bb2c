'use client'

import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { cn } from '@/lib/utils'
import { LucideIcon } from 'lucide-react'

interface NavigationItemProps {
  href: string
  label: string
  icon: LucideIcon
  description?: string
  variant: 'bottom-nav' | 'sidebar'
  isActive?: boolean
  onClick?: () => void
  className?: string
}

export function NavigationItem({
  href,
  label,
  icon: Icon,
  description,
  variant,
  isActive: providedIsActive,
  onClick,
  className
}: NavigationItemProps) {
  const pathname = usePathname()
  const isActive = providedIsActive ?? pathname.startsWith(href)

  const getVariantStyles = () => {
    switch (variant) {
      case 'bottom-nav':
        return {
          container: cn(
            'flex flex-col items-center justify-center py-2 px-3 min-w-[44px] min-h-[44px] rounded-lg transition-colors duration-200',
            'hover:bg-accent/50 active:bg-accent/70 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring',
            isActive
              ? 'text-primary bg-primary/10'
              : 'text-muted-foreground hover:text-foreground'
          ),
          icon: 'h-6 w-6 mb-1',
          label: 'text-xs text-center leading-tight font-medium'
        }
      case 'sidebar':
        return {
          container: cn(
            'flex items-center gap-3 rounded-lg px-3 py-3 text-sm transition-all hover:bg-accent',
            isActive
              ? 'bg-accent text-accent-foreground font-medium'
              : 'text-muted-foreground hover:text-foreground'
          ),
          icon: 'h-5 w-5',
          label: 'font-medium'
        }
      default:
        return {
          container: '',
          icon: 'h-5 w-5',
          label: ''
        }
    }
  }

  const styles = getVariantStyles()

  const content = (
    <>
      <Icon className={styles.icon} />
      {variant === 'bottom-nav' ? (
        <span className={styles.label}>{label}</span>
      ) : (
        <div className="flex flex-col">
          <span className={styles.label}>{label}</span>
          {description && (
            <span className="text-xs text-muted-foreground">
              {description}
            </span>
          )}
        </div>
      )}
    </>
  )

  return (
    <Link
      href={href}
      onClick={onClick}
      className={cn(styles.container, className)}
      title={description || label}
      aria-label={label}
    >
      {content}
    </Link>
  )
}

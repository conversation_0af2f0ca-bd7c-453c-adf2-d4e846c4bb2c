'use client'

import { useState, useEffect } from 'react'
import { SearchIcon } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { SearchInput } from '@/components/ui/search-input'
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandList,
} from '@/components/ui/command'
import { useSearch } from '@/hooks/use-search'
import { SearchResultItem } from './search-result-item'
import { cn } from '@/lib/utils'

interface GlobalSearchProps {
  className?: string
}

export function GlobalSearch({ className }: GlobalSearchProps) {
  const [isOpen, setIsOpen] = useState(false)
  const { query, setQuery, results, isLoading, error, clearResults } = useSearch()

  // Keyboard shortcut to open search (Ctrl/Cmd + K)
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
        e.preventDefault()
        setIsOpen(true)
      }
    }

    document.addEventListener('keydown', handleKeyDown)
    return () => document.removeEventListener('keydown', handleKeyDown)
  }, [])

  const handleOpen = () => {
    setIsOpen(true)
  }

  const handleClose = () => {
    setIsOpen(false)
    clearResults()
  }

  const handleSelect = () => {
    handleClose()
  }

  const hasResults = results && results.total > 0

  return (
    <>
      {/* Search Trigger Button */}
      <Button
        variant="outline"
        className={cn(
          "w-full justify-between text-muted-foreground font-normal",
          className
        )}
        onClick={handleOpen}
      >
        <div className="flex items-center">
          <SearchIcon className="mr-2 h-4 w-4" />
          <span className="hidden sm:inline">Search orders, customers, stores...</span>
          <span className="sm:hidden">Search...</span>
        </div>
        <kbd className="pointer-events-none hidden h-5 select-none items-center gap-1 rounded border bg-muted px-1.5 font-mono text-[10px] font-medium opacity-100 sm:flex">
          <span className="text-xs">⌘</span>K
        </kbd>
      </Button>

      {/* Search Dialog */}
      <Dialog open={isOpen} onOpenChange={setIsOpen}>
        <DialogContent className="max-w-2xl p-0">
          <DialogHeader className="px-6 pt-6 pb-0">
            <DialogTitle className="sr-only">Global Search</DialogTitle>
          </DialogHeader>

          <Command className="rounded-lg border-0 shadow-none">
            {/* Search Input */}
            <SearchInput
              value={query}
              onChange={setQuery}
              placeholder="Search orders, customers, stores..."
              variant="command"
              autoFocus
            />

            <CommandList className="max-h-[400px] overflow-y-auto">
              {/* Loading State */}
              {isLoading && (
                <div className="flex items-center justify-center py-8">
                  <div className="text-sm text-muted-foreground">Searching...</div>
                </div>
              )}

              {/* Error State */}
              {error && (
                <div className="flex items-center justify-center py-8">
                  <div className="text-sm text-destructive">
                    Error: {error}
                  </div>
                </div>
              )}

              {/* Empty State */}
              {!isLoading && !error && query && !hasResults && (
                <CommandEmpty>
                  <div className="text-center py-8">
                    <div className="text-sm text-muted-foreground">
                      No orders found for &quot;{query}&quot;
                    </div>
                  </div>
                </CommandEmpty>
              )}

              {/* No Query State */}
              {!query && !isLoading && (
                <div className="flex items-center justify-center py-8">
                  <div className="text-center">
                    <div className="text-sm text-muted-foreground mb-2">
                      Start typing to search orders, customers, and stores
                    </div>
                    <div className="text-xs text-muted-foreground">
                      • Product matches → Order details
                    </div>
                    <div className="text-xs text-muted-foreground">
                      • Customer matches → Customer details
                    </div>
                    <div className="text-xs text-muted-foreground">
                      • Store matches → Store buy list
                    </div>
                  </div>
                </div>
              )}

              {/* Results */}
              {hasResults && !isLoading && !error && (
                <>
                  {/* Product Name Results */}
                  {results.productName.length > 0 && (
                    <CommandGroup heading={`Products (${results.productName.length})`}>
                      {results.productName.map((item) => (
                        <SearchResultItem
                          key={`product-${item.id}`}
                          item={item}
                          searchQuery={query}
                          onSelect={handleSelect}
                        />
                      ))}
                    </CommandGroup>
                  )}

                  {/* Customer Results */}
                  {results.customer.length > 0 && (
                    <CommandGroup heading={`Customers (${results.customer.length})`}>
                      {results.customer.map((item) => (
                        <SearchResultItem
                          key={`customer-${item.id}`}
                          item={item}
                          searchQuery={query}
                          onSelect={handleSelect}
                        />
                      ))}
                    </CommandGroup>
                  )}

                  {/* Store Code Results */}
                  {results.storeCode.length > 0 && (
                    <CommandGroup heading={`Stores (${results.storeCode.length})`}>
                      {results.storeCode.map((item) => (
                        <SearchResultItem
                          key={`store-${item.id}`}
                          item={item}
                          searchQuery={query}
                          onSelect={handleSelect}
                        />
                      ))}
                    </CommandGroup>
                  )}

                  {/* Usage Unit Results */}
                  {results.usageUnit.length > 0 && (
                    <CommandGroup heading={`Usage Units (${results.usageUnit.length})`}>
                      {results.usageUnit.map((item) => (
                        <SearchResultItem
                          key={`usage-${item.id}`}
                          item={item}
                          searchQuery={query}
                          onSelect={handleSelect}
                        />
                      ))}
                    </CommandGroup>
                  )}

                  {/* Comment Results */}
                  {results.comment.length > 0 && (
                    <CommandGroup heading={`Comments (${results.comment.length})`}>
                      {results.comment.map((item) => (
                        <SearchResultItem
                          key={`comment-${item.id}`}
                          item={item}
                          searchQuery={query}
                          onSelect={handleSelect}
                        />
                      ))}
                    </CommandGroup>
                  )}
                </>
              )}
            </CommandList>
          </Command>

          {/* Results Summary */}
          {hasResults && (
            <div className="border-t px-6 py-3">
              <div className="text-xs text-muted-foreground">
                Found {results.total} result{results.total !== 1 ? 's' : ''} matching &quot;{query}&quot;
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </>
  )
}

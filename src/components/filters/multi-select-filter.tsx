import React, { useState } from 'react'
import { Label } from '@/components/ui/label'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Checkbox } from '@/components/ui/checkbox'
import { ScrollArea } from '@/components/ui/scroll-area'
import { SearchInput } from '@/components/ui/search-input'
import { EntityFilter } from '@/lib/filter-types'
import { X, ChevronDown, ChevronUp } from 'lucide-react'

interface MultiSelectFilterProps {
  label: string
  value?: EntityFilter
  onChange: (value: EntityFilter | undefined) => void
  options: Array<{ id: number; name: string; count?: number }>
  placeholder?: string
  allowNull?: boolean
  nullLabel?: string
}

export function MultiSelectFilter({
  label,
  value,
  onChange,
  options,
  placeholder = "Search...",
  allowNull = true,
  nullLabel = "No assignment"
}: MultiSelectFilterProps) {
  const [isExpanded, setIsExpanded] = useState(false)
  const [searchTerm, setSearchTerm] = useState('')

  const filteredOptions = options.filter(option =>
    option.name.toLowerCase().includes(searchTerm.toLowerCase())
  )

  const selectedIds = value?.include || []
  const excludedIds = value?.exclude || []
  const includeNull = value?.includeNull || false

  const handleIncludeToggle = (id: number) => {
    const newInclude = selectedIds.includes(id)
      ? selectedIds.filter(selectedId => selectedId !== id)
      : [...selectedIds, id]

    const newExclude = excludedIds.filter(excludedId => excludedId !== id)

    updateFilter(newInclude, newExclude, includeNull)
  }

  const handleExcludeToggle = (id: number) => {
    const newExclude = excludedIds.includes(id)
      ? excludedIds.filter(excludedId => excludedId !== id)
      : [...excludedIds, id]

    const newInclude = selectedIds.filter(selectedId => selectedId !== id)

    updateFilter(newInclude, newExclude, includeNull)
  }

  const handleNullToggle = () => {
    updateFilter(selectedIds, excludedIds, !includeNull)
  }

  const updateFilter = (include: number[], exclude: number[], includeNullValue: boolean) => {
    if (include.length === 0 && exclude.length === 0 && !includeNullValue) {
      onChange(undefined)
    } else {
      onChange({
        include,
        exclude,
        includeNull: includeNullValue
      })
    }
  }

  const clearFilter = () => {
    onChange(undefined)
  }

  const getSelectedOption = (id: number) => {
    return options.find(option => option.id === id)
  }

  const hasActiveFilters = selectedIds.length > 0 || excludedIds.length > 0 || includeNull

  const getFilterSummary = () => {
    const parts = []

    if (selectedIds.length > 0) {
      parts.push(`${selectedIds.length} included`)
    }

    if (excludedIds.length > 0) {
      parts.push(`${excludedIds.length} excluded`)
    }

    if (includeNull) {
      parts.push('includes unassigned')
    }

    return parts.join(', ')
  }

  return (
    <div className="space-y-2">
      <div className="flex items-center justify-between">
        <Label className="text-xs font-medium">{label}</Label>
        {hasActiveFilters && (
          <button
            onClick={clearFilter}
            className="text-xs text-muted-foreground hover:text-foreground"
          >
            Clear
          </button>
        )}
      </div>

      <div className="space-y-2">
        <Button
          variant="outline"
          onClick={() => setIsExpanded(!isExpanded)}
          className="w-full h-10 md:h-8 justify-between text-xs touch-target"
        >
          <span>
            {hasActiveFilters ? getFilterSummary() : `Select ${label.toLowerCase()}...`}
          </span>
          {isExpanded ? <ChevronUp className="h-4 w-4 md:h-3 md:w-3" /> : <ChevronDown className="h-4 w-4 md:h-3 md:w-3" />}
        </Button>

        {isExpanded && (
          <div className="border rounded-md p-2 space-y-2 bg-background">
            {/* Search */}
            <SearchInput
              value={searchTerm}
              onChange={setSearchTerm}
              placeholder={placeholder}
              variant="filter"
              size="sm"
            />

            {/* Null option */}
            {allowNull && (
              <div className="flex items-center space-x-2 p-2 md:p-1 touch-target">
                <Checkbox
                  id="null-option"
                  checked={includeNull}
                  onCheckedChange={handleNullToggle}
                  className="h-5 w-5 md:h-4 md:w-4"
                />
                <label htmlFor="null-option" className="text-sm md:text-xs flex-1 cursor-pointer">
                  {nullLabel}
                </label>
              </div>
            )}

            {/* Options */}
            <ScrollArea className="max-h-32">
              <div className="space-y-1">
                {filteredOptions.map((option) => {
                  const isIncluded = selectedIds.includes(option.id)
                  const isExcluded = excludedIds.includes(option.id)

                  return (
                    <div key={option.id} className="flex items-center space-x-2 p-2 md:p-1 touch-target">
                      <div className="flex items-center space-x-1">
                        <Checkbox
                          id={`include-${option.id}`}
                          checked={isIncluded}
                          onCheckedChange={() => handleIncludeToggle(option.id)}
                          className="h-5 w-5 md:h-4 md:w-4"
                        />
                        <label
                          htmlFor={`include-${option.id}`}
                          className="text-sm md:text-xs cursor-pointer text-green-600"
                          title="Include"
                        >
                          ✓
                        </label>
                      </div>

                      <div className="flex items-center space-x-1">
                        <Checkbox
                          id={`exclude-${option.id}`}
                          checked={isExcluded}
                          onCheckedChange={() => handleExcludeToggle(option.id)}
                          className="h-5 w-5 md:h-4 md:w-4"
                        />
                        <label
                          htmlFor={`exclude-${option.id}`}
                          className="text-sm md:text-xs cursor-pointer text-red-600"
                          title="Exclude"
                        >
                          ✗
                        </label>
                      </div>

                      <span className="text-sm md:text-xs flex-1">
                        {option.name}
                        {option.count !== undefined && (
                          <span className="text-muted-foreground ml-1">({option.count})</span>
                        )}
                      </span>
                    </div>
                  )
                })}

                {filteredOptions.length === 0 && (
                  <div className="text-xs text-muted-foreground text-center py-2">
                    No options found
                  </div>
                )}
              </div>
            </ScrollArea>
          </div>
        )}

        {/* Selected items display */}
        {hasActiveFilters && (
          <div className="space-y-1">
            {selectedIds.length > 0 && (
              <div className="space-y-1">
                <Label className="text-xs text-green-600">Included:</Label>
                <div className="flex flex-wrap gap-1">
                  {selectedIds.map((id) => {
                    const option = getSelectedOption(id)
                    return option ? (
                      <Badge key={id} variant="outline" className="text-xs px-2 py-0 bg-green-50 border-green-200">
                        {option.name}
                        <button
                          onClick={() => handleIncludeToggle(id)}
                          className="ml-1 hover:text-destructive"
                        >
                          <X className="h-2 w-2" />
                        </button>
                      </Badge>
                    ) : null
                  })}
                </div>
              </div>
            )}

            {excludedIds.length > 0 && (
              <div className="space-y-1">
                <Label className="text-xs text-red-600">Excluded:</Label>
                <div className="flex flex-wrap gap-1">
                  {excludedIds.map((id) => {
                    const option = getSelectedOption(id)
                    return option ? (
                      <Badge key={id} variant="outline" className="text-xs px-2 py-0 bg-red-50 border-red-200">
                        {option.name}
                        <button
                          onClick={() => handleExcludeToggle(id)}
                          className="ml-1 hover:text-destructive"
                        >
                          <X className="h-2 w-2" />
                        </button>
                      </Badge>
                    ) : null
                  })}
                </div>
              </div>
            )}

            {includeNull && (
              <div className="space-y-1">
                <Badge variant="outline" className="text-xs px-2 py-0 bg-blue-50 border-blue-200">
                  {nullLabel}
                  <button
                    onClick={handleNullToggle}
                    className="ml-1 hover:text-destructive"
                  >
                    <X className="h-2 w-2" />
                  </button>
                </Badge>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  )
}

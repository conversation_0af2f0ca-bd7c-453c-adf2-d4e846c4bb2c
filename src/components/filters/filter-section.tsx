import React from 'react'
import { Card } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { ChevronDown, ChevronUp } from 'lucide-react'
import { cn } from '@/lib/utils'

interface FilterSectionProps {
  title: string
  children: React.ReactNode
  defaultOpen?: boolean
  className?: string
  onClear?: () => void
  hasActiveFilters?: boolean
}

export function FilterSection({
  title,
  children,
  defaultOpen = true,
  className,
  onClear,
  hasActiveFilters = false
}: FilterSectionProps) {
  const [isOpen, setIsOpen] = React.useState(defaultOpen)

  return (
    <Card className={cn("p-3 md:p-4", className)}>
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center gap-2">
          <h3 className="font-medium text-base md:text-sm">{title}</h3>
          {hasActiveFilters && (
            <div className="w-2 h-2 bg-primary rounded-full" />
          )}
        </div>
        <div className="flex items-center gap-1">
          {onClear && hasActiveFilters && (
            <Button
              variant="ghost"
              size="sm"
              onClick={onClear}
              className="h-8 md:h-6 px-3 md:px-2 text-sm md:text-xs text-muted-foreground hover:text-foreground touch-target"
            >
              Clear
            </Button>
          )}
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setIsOpen(!isOpen)}
            className="h-8 w-8 md:h-6 md:w-6 p-0 touch-target"
          >
            {isOpen ? (
              <ChevronUp className="h-4 w-4 md:h-3 md:w-3" />
            ) : (
              <ChevronDown className="h-4 w-4 md:h-3 md:w-3" />
            )}
          </Button>
        </div>
      </div>

      {isOpen && (
        <div className="space-y-3">
          {children}
        </div>
      )}
    </Card>
  )
}

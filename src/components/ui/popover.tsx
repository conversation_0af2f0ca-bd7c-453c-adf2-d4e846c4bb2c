"use client"

import * as React from "react"
import * as PopoverPrimitive from "@radix-ui/react-popover"
import { useScroll } from "@/hooks/use-scroll"

import { cn } from "@/lib/utils"

function Popover({
  onOpenChange,
  open,
  ...props
}: React.ComponentProps<typeof PopoverPrimitive.Root>) {
  const scroll = useScroll({
    threshold: 15, // Increased from 5 to be less sensitive
    throttleMs: 16,
    directionThreshold: 10, // Increased from 3 to require more movement
    debounceMs: 100 // Increased from 50 for more stability
  })

  // Track when popover was opened to prevent immediate closure
  const openedAtRef = React.useRef<number>(0)

  React.useEffect(() => {
    if (open) {
      openedAtRef.current = Date.now()
    }
  }, [open])

  // Auto-close popover when scrolling starts (only if controlled and after delay)
  React.useEffect(() => {
    if (open !== undefined && open && (scroll.isScrollingDown || scroll.isScrollingUp)) {
      const timeSinceOpened = Date.now() - openedAtRef.current
      const MIN_OPEN_TIME = 300 // Minimum time before auto-close can trigger

      if (timeSinceOpened > MIN_OPEN_TIME) {
        onOpenChange?.(false)
      }
    }
  }, [open, scroll.isScrollingDown, scroll.isScrollingUp, onOpenChange])

  // Pass through all props, including open and onOpenChange if provided
  const popoverProps = {
    ...props,
    ...(open !== undefined && { open }),
    ...(onOpenChange !== undefined && { onOpenChange })
  }

  return <PopoverPrimitive.Root data-slot="popover" {...popoverProps} />
}

function PopoverTrigger({
  ...props
}: React.ComponentProps<typeof PopoverPrimitive.Trigger>) {
  return <PopoverPrimitive.Trigger data-slot="popover-trigger" {...props} />
}

function PopoverContent({
  className,
  align = "center",
  sideOffset = 4,
  ...props
}: React.ComponentProps<typeof PopoverPrimitive.Content>) {
  return (
    <PopoverPrimitive.Portal>
      <PopoverPrimitive.Content
        data-slot="popover-content"
        align={align}
        sideOffset={sideOffset}
        className={cn(
          "bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-72 origin-(--radix-popover-content-transform-origin) rounded-md border p-4 shadow-md outline-hidden",
          className
        )}
        {...props}
      />
    </PopoverPrimitive.Portal>
  )
}

function PopoverAnchor({
  ...props
}: React.ComponentProps<typeof PopoverPrimitive.Anchor>) {
  return <PopoverPrimitive.Anchor data-slot="popover-anchor" {...props} />
}

export { Popover, PopoverTrigger, PopoverContent, PopoverAnchor }

'use client'

import * as React from 'react'
import { Monitor, Moon, Sun } from 'lucide-react'
import { useTheme } from 'next-themes'

import { Button } from '@/components/ui/button'
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover'
import { cn } from '@/lib/utils'

interface ThemeToggleProps {
  className?: string
  size?: 'default' | 'sm' | 'lg' | 'icon'
  variant?: 'button' | 'dropdown'
  buttonVariant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link'
  align?: 'start' | 'center' | 'end'
}

export function ThemeToggle({
  className,
  size = 'icon',
  variant = 'button',
  buttonVariant = 'ghost',
  align = 'end'
}: ThemeToggleProps) {
  const { theme, setTheme } = useTheme()
  const [mounted, setMounted] = React.useState(false)

  // useEffect only runs on the client, so now we can safely show the UI
  React.useEffect(() => {
    setMounted(true)
  }, [])

  if (!mounted) {
    // Return a placeholder with the same dimensions to prevent layout shift
    return (
      <Button
        variant={buttonVariant}
        size={size}
        className={cn('h-9 w-9', className)}
        disabled
      >
        <span className="sr-only">Loading theme toggle</span>
      </Button>
    )
  }

  // Button variant - simple toggle between light/dark
  if (variant === 'button') {
    const toggleTheme = () => {
      setTheme(theme === 'light' ? 'dark' : 'light')
    }

    return (
      <Button
        variant={buttonVariant}
        size={size}
        onClick={toggleTheme}
        className={cn('h-9 w-9', className)}
        title={`Switch to ${theme === 'light' ? 'dark' : 'light'} mode`}
        aria-label={`Switch to ${theme === 'light' ? 'dark' : 'light'} mode`}
      >
        <Sun className="h-[1.2rem] w-[1.2rem] rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0" />
        <Moon className="absolute h-[1.2rem] w-[1.2rem] rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100" />
        <span className="sr-only">Toggle theme</span>
      </Button>
    )
  }

  // Dropdown variant - shows all theme options
  const getCurrentIcon = () => {
    switch (theme) {
      case 'light':
        return <Sun className="h-[1.2rem] w-[1.2rem]" />
      case 'dark':
        return <Moon className="h-[1.2rem] w-[1.2rem]" />
      default:
        return <Monitor className="h-[1.2rem] w-[1.2rem]" />
    }
  }

  const themeOptions = [
    {
      value: 'light',
      label: 'Light',
      icon: Sun,
    },
    {
      value: 'dark',
      label: 'Dark',
      icon: Moon,
    },
    {
      value: 'system',
      label: 'System',
      icon: Monitor,
    },
  ]

  return (
    <Popover>
      <PopoverTrigger asChild>
        <Button
          variant={buttonVariant}
          size={size}
          className={cn('h-9 w-9', className)}
          title="Change theme"
          aria-label="Change theme"
        >
          {getCurrentIcon()}
          <span className="sr-only">Toggle theme</span>
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-40 p-1" align={align}>
        <div className="grid gap-1">
          {themeOptions.map((option) => {
            const Icon = option.icon
            const isActive = theme === option.value

            return (
              <Button
                key={option.value}
                variant={isActive ? 'secondary' : 'ghost'}
                size="sm"
                onClick={() => setTheme(option.value)}
                className="justify-start gap-2"
              >
                <Icon className="h-4 w-4" />
                {option.label}
                {isActive && (
                  <span className="ml-auto text-xs text-muted-foreground">
                    ✓
                  </span>
                )}
              </Button>
            )
          })}
        </div>
      </PopoverContent>
    </Popover>
  )
}

// Legacy export for backward compatibility
export const ThemeToggleDropdown = (props: { className?: string; align?: 'start' | 'center' | 'end' }) => (
  <ThemeToggle variant="dropdown" className={props.className} align={props.align} />
)

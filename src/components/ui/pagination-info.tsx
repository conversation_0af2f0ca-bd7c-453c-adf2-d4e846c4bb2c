import React from 'react'

interface PaginationInfoProps {
  currentPage: number
  totalPages: number
  totalItems: number
  itemsPerPage: number
  className?: string
}

export function PaginationInfo({
  currentPage,
  totalPages,
  totalItems,
  itemsPerPage,
  className = ''
}: PaginationInfoProps) {
  if (totalItems === 0) {
    return (
      <div className={`text-sm text-muted-foreground ${className}`}>
        No items found
      </div>
    )
  }

  const startItem = (currentPage - 1) * itemsPerPage + 1
  const endItem = Math.min(currentPage * itemsPerPage, totalItems)

  return (
    <div className={`text-sm text-muted-foreground ${className}`}>
      Showing {startItem}-{endItem} of {totalItems} {totalItems === 1 ? 'order' : 'orders'}
    </div>
  )
}

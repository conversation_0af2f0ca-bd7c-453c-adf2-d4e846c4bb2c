'use client'

import { ReactNode } from 'react'

interface PageWrapperProps {
  children: ReactNode
  title?: string // Made optional since we're not using it anymore
  actions?: ReactNode // Made optional since we're not using it anymore
  className?: string
  showHeader?: boolean // Kept for backward compatibility but ignored
}

export function PageWrapper({
  children,
  title,
  actions,
  className = '',
  showHeader = true
}: PageWrapperProps) {
  // Simplified: no page headers, just content
  return (
    <div className={`min-h-screen ${className}`}>
      <div className="space-y-4 py-4 px-4">
        {children}
      </div>
    </div>
  )
}

// Specialized wrapper for pages with summary cards
interface PageWithSummaryProps extends PageWrapperProps {
  summaryCard?: ReactNode
}

export function PageWithSummary({
  children,
  title,
  actions,
  summaryCard,
  className = '',
  showHeader = true
}: PageWithSummaryProps) {
  return (
    <div className={`min-h-screen ${className}`}>
      <div className="space-y-4 py-4 px-4">
        {/* Header with title and actions */}
        {(title || actions) && (
          <div className="flex items-center justify-between">
            {title && (
              <h1 className="text-2xl font-semibold tracking-tight">{title}</h1>
            )}
            {actions && (
              <div className="flex items-center gap-2">
                {actions}
              </div>
            )}
          </div>
        )}

        {summaryCard && (
          <div className="mb-4">
            {summaryCard}
          </div>
        )}
        {children}
      </div>
    </div>
  )
}

// Simple wrapper for basic pages without complex layouts
export function SimplePageWrapper({
  children,
  title,
  actions,
  className = ''
}: PageWrapperProps) {
  return (
    <PageWrapper
      title={title}
      actions={actions}
      className={className}
      showHeader={false} // No headers anymore
    >
      {children}
    </PageWrapper>
  )
}

'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { LuCalculator, LuInfo, LuRefreshCw } from 'react-icons/lu'

interface PricingCalculationResult {
  storePrice: number
  pasabuyFee: number
  customerPrice: number
  breakdown: {
    storePrice: number
    markup: number
    serviceFee: number
    total: number
  }
  appliedTier: {
    minPrice: number
    maxPrice: number | null
    markupType: string
    markupValue: number
  } | null
  serviceFee: number
}

interface PricingCalculatorProps {
  storePrice: number
  storeCodeId?: number
  onPriceCalculated?: (result: PricingCalculationResult) => void
  className?: string
  autoCalculate?: boolean
}

export function PricingCalculator({
  storePrice,
  storeCodeId,
  onPriceCalculated,
  className = '',
  autoCalculate = true
}: PricingCalculatorProps) {
  const [result, setResult] = useState<PricingCalculationResult | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    if (autoCalculate && storePrice > 0) {
      calculatePrice()
    }
  }, [storePrice, storeCodeId, autoCalculate])

  const calculatePrice = async () => {
    if (storePrice <= 0) {
      setResult(null)
      setError('Store price must be greater than 0')
      return
    }

    try {
      setLoading(true)
      setError(null)

      const response = await fetch('/api/pricing/calculate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          storePrice,
          storeCodeId
        })
      })

      if (!response.ok) {
        throw new Error('Failed to calculate price')
      }

      const calculationResult = await response.json()
      setResult(calculationResult)
      
      if (onPriceCalculated) {
        onPriceCalculated(calculationResult)
      }
    } catch (error) {
      console.error('Error calculating price:', error)
      setError('Failed to calculate price')
    } finally {
      setLoading(false)
    }
  }

  const formatCurrency = (amount: number) => {
    return `₱${amount.toFixed(2)}`
  }

  const formatMarkupDisplay = (markupType: string, markupValue: number) => {
    switch (markupType) {
      case 'PERCENTAGE':
        return `${markupValue}%`
      case 'FIXED_AMOUNT':
        return formatCurrency(markupValue)
      default:
        return markupValue.toString()
    }
  }

  if (storePrice <= 0) {
    return (
      <Card className={`border-muted ${className}`}>
        <CardContent className="p-4 text-center text-muted-foreground">
          <LuCalculator className="h-8 w-8 mx-auto mb-2 opacity-50" />
          <p className="text-sm">Enter a store price to see automatic pricing calculation</p>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className={`${className}`}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-base flex items-center gap-2">
            <LuCalculator className="h-4 w-4" />
            Pricing Calculator
          </CardTitle>
          {!autoCalculate && (
            <Button
              variant="outline"
              size="sm"
              onClick={calculatePrice}
              disabled={loading}
              className="h-8"
            >
              {loading ? (
                <LuRefreshCw className="h-3 w-3 animate-spin" />
              ) : (
                <LuCalculator className="h-3 w-3" />
              )}
            </Button>
          )}
        </div>
      </CardHeader>
      <CardContent className="pt-0">
        {loading && (
          <div className="text-center py-4">
            <LuRefreshCw className="h-6 w-6 animate-spin mx-auto mb-2 text-muted-foreground" />
            <p className="text-sm text-muted-foreground">Calculating price...</p>
          </div>
        )}

        {error && (
          <div className="text-center py-4">
            <div className="text-destructive text-sm">{error}</div>
          </div>
        )}

        {result && !loading && (
          <div className="space-y-4">
            {/* Applied Tier Info */}
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <Badge variant="outline" className="text-xs">
                  {result.appliedTier?.markupType === 'PERCENTAGE' ? 'Percentage Markup' : 'Fixed Markup'}
                </Badge>
                {result.appliedTier && (
                  <Badge variant="secondary" className="text-xs">
                    {result.appliedTier.maxPrice
                      ? `₱${result.appliedTier.minPrice} - ₱${result.appliedTier.maxPrice}`
                      : `₱${result.appliedTier.minPrice}+`
                    }
                  </Badge>
                )}
              </div>
              <div className="text-xs text-muted-foreground">
                Markup: {result.appliedTier ? formatMarkupDisplay(result.appliedTier.markupType, result.appliedTier.markupValue) : 'N/A'}
                {result.serviceFee > 0 && (
                  <span> + {formatCurrency(result.serviceFee)} Pasabuy fee</span>
                )}
              </div>
            </div>

            <Separator />

            {/* Price Breakdown */}
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>Store Price:</span>
                <span>{formatCurrency(result.breakdown.storePrice)}</span>
              </div>

              {result.breakdown.markup > 0 && (
                <div className="flex justify-between text-sm">
                  <span>Markup:</span>
                  <span className="text-green-600">+{formatCurrency(result.breakdown.markup)}</span>
                </div>
              )}

              {result.breakdown.serviceFee !== 0 && (
                <div className="flex justify-between text-sm">
                  <span>Pasabuy Fee:</span>
                  <span className="text-blue-600">{formatCurrency(result.breakdown.serviceFee)}</span>
                </div>
              )}

              <Separator />

              <div className="flex justify-between font-bold text-lg">
                <span>Customer Price:</span>
                <span className="text-primary">{formatCurrency(result.customerPrice)}</span>
              </div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}

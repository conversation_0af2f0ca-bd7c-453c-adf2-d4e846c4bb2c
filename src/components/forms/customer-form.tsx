'use client'

import { z } from 'zod'
import { Card } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Textarea } from '@/components/ui/textarea'
import { FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form'
import { BaseForm } from './base-form'

const customerSchema = z.object({
  // Basic information
  name: z.string()
    .min(1, 'Customer name is required')
    .max(100, 'Customer name must be 100 characters or less'),
  customerType: z.enum(['INDIVIDUAL', 'BUSINESS', 'RESELLER', 'WHOLESALE', 'GOVERNMENT', 'NON_PROFIT']).optional(),
  status: z.enum(['ACTIVE', 'INACTIVE', 'SUSPENDED', 'PROSPECT', 'ARCHIVED']).optional(),

  // Contact information
  email: z.string().email('Invalid email address').optional().or(z.literal('')),
  phone: z.string().optional(),
  alternatePhone: z.string().optional(),
  website: z.string().url('Invalid website URL').optional().or(z.literal('')),

  // Address information
  address: z.string().optional(),
  city: z.string().optional(),
  state: z.string().optional(),
  postalCode: z.string().optional(),
  country: z.string().optional(),

  // Business information
  businessName: z.string().optional(),
  taxId: z.string().optional(),
  businessType: z.string().optional(),

  // Customer preferences
  preferredDeliveryMethod: z.string().optional(),
  preferredPaymentMethod: z.string().optional(),
  creditLimit: z.number().min(0, 'Credit limit must be positive').optional(),
  paymentTerms: z.number().min(1, 'Payment terms must be at least 1 day').optional(),
  discountRate: z.number().min(0, 'Discount rate must be positive').max(100, 'Discount rate cannot exceed 100%').optional(),

  // Customer segmentation
  segment: z.enum(['REGULAR', 'VIP', 'PREMIUM', 'ENTERPRISE', 'SMALL_BUSINESS', 'BULK_BUYER']).optional(),
  loyaltyTier: z.enum(['BRONZE', 'SILVER', 'GOLD', 'PLATINUM', 'DIAMOND']).optional(),

  // Relationship management
  assignedSalesRep: z.string().optional(),
  accountManager: z.string().optional(),
  referredBy: z.string().optional(),

  // Notes
  notes: z.string().optional(),
  internalNotes: z.string().optional(),
})

type CustomerFormData = z.infer<typeof customerSchema>

interface CustomerFormProps {
  onSubmit: (data: CustomerFormData) => Promise<void>
  onCancel: () => void
  initialData?: Partial<CustomerFormData>
  isLoading?: boolean
}

export function CustomerForm({ onSubmit, onCancel, initialData, isLoading = false }: CustomerFormProps) {
  // Helper function to convert null values to empty strings
  const sanitizeValue = (value: unknown): string => {
    if (value === null || value === undefined) return ''
    return String(value)
  }

  const defaultData = {
    name: sanitizeValue(initialData?.name) || '',
    customerType: (initialData?.customerType as any) || 'INDIVIDUAL',
    status: (initialData?.status as any) || 'ACTIVE',
    email: sanitizeValue(initialData?.email),
    phone: sanitizeValue(initialData?.phone),
    alternatePhone: sanitizeValue(initialData?.alternatePhone),
    website: sanitizeValue(initialData?.website),
    address: sanitizeValue(initialData?.address),
    city: sanitizeValue(initialData?.city),
    state: sanitizeValue(initialData?.state),
    postalCode: sanitizeValue(initialData?.postalCode),
    country: sanitizeValue(initialData?.country) || 'Philippines',
    businessName: sanitizeValue(initialData?.businessName),
    taxId: sanitizeValue(initialData?.taxId),
    businessType: sanitizeValue(initialData?.businessType),
    preferredDeliveryMethod: sanitizeValue(initialData?.preferredDeliveryMethod),
    preferredPaymentMethod: sanitizeValue(initialData?.preferredPaymentMethod),
    creditLimit: initialData?.creditLimit ?? 0,
    paymentTerms: initialData?.paymentTerms ?? 30,
    discountRate: initialData?.discountRate ?? 0,
    segment: (initialData?.segment as any) || 'REGULAR',
    loyaltyTier: (initialData?.loyaltyTier as any) || 'BRONZE',
    assignedSalesRep: sanitizeValue(initialData?.assignedSalesRep),
    accountManager: sanitizeValue(initialData?.accountManager),
    referredBy: sanitizeValue(initialData?.referredBy),
    notes: sanitizeValue(initialData?.notes),
    internalNotes: sanitizeValue(initialData?.internalNotes),
  }

  return (
    <BaseForm
      schema={customerSchema}
      onSubmit={onSubmit}
      onCancel={onCancel}
      initialData={defaultData}
      isLoading={isLoading}
      submitButtonText="Save Customer"
    >
      {(form) => (
        <div className="space-y-6">
          {/* Basic Information */}
          <Card className="p-6">
            <h3 className="text-base font-medium mb-4">Basic Information</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Customer Name *</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="Customer name..."
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="customerType"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Customer Type</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select customer type" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="INDIVIDUAL">Individual</SelectItem>
                        <SelectItem value="BUSINESS">Business</SelectItem>
                        <SelectItem value="RESELLER">Reseller</SelectItem>
                        <SelectItem value="WHOLESALE">Wholesale</SelectItem>
                        <SelectItem value="GOVERNMENT">Government</SelectItem>
                        <SelectItem value="NON_PROFIT">Non-Profit</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="status"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Status</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select status" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="ACTIVE">Active</SelectItem>
                        <SelectItem value="INACTIVE">Inactive</SelectItem>
                        <SelectItem value="SUSPENDED">Suspended</SelectItem>
                        <SelectItem value="PROSPECT">Prospect</SelectItem>
                        <SelectItem value="ARCHIVED">Archived</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="segment"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Customer Segment</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select segment" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="REGULAR">Regular</SelectItem>
                        <SelectItem value="VIP">VIP</SelectItem>
                        <SelectItem value="PREMIUM">Premium</SelectItem>
                        <SelectItem value="ENTERPRISE">Enterprise</SelectItem>
                        <SelectItem value="SMALL_BUSINESS">Small Business</SelectItem>
                        <SelectItem value="BULK_BUYER">Bulk Buyer</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          </Card>

          {/* Contact Information */}
          <Card className="p-6">
            <h3 className="text-base font-medium mb-4">Contact Information</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="email"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Email</FormLabel>
                    <FormControl>
                      <Input
                        type="email"
                        placeholder="<EMAIL>"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="phone"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Phone</FormLabel>
                    <FormControl>
                      <Input
                        type="tel"
                        placeholder="+63 ************"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="alternatePhone"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Alternate Phone</FormLabel>
                    <FormControl>
                      <Input
                        type="tel"
                        placeholder="+63 ************"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="website"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Website</FormLabel>
                    <FormControl>
                      <Input
                        type="url"
                        placeholder="https://example.com"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          </Card>

          {/* Address Information */}
          <Card className="p-6">
            <h3 className="text-base font-medium mb-4">Address Information</h3>
            <div className="space-y-4">
              <FormField
                control={form.control}
                name="address"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Address</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Street address..."
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <FormField
                  control={form.control}
                  name="city"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>City</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="City"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="state"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>State/Province</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="State or Province"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="postalCode"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Postal Code</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="Postal Code"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={form.control}
                name="country"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Country</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="Country"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          </Card>

          {/* Business Information (conditional) */}
          {form.watch('customerType') !== 'INDIVIDUAL' && (
            <Card className="p-6">
              <h3 className="text-base font-medium mb-4">Business Information</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="businessName"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Business Name</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="Business name..."
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="taxId"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Tax ID</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="Tax identification number"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="businessType"
                  render={({ field }) => (
                    <FormItem className="md:col-span-2">
                      <FormLabel>Business Type</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="Type of business..."
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </Card>
          )}

          {/* Notes */}
          <Card className="p-6">
            <h3 className="text-base font-medium mb-4">Notes</h3>
            <div className="space-y-4">
              <FormField
                control={form.control}
                name="notes"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Customer Notes</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Notes about this customer..."
                        {...field}
                      />
                    </FormControl>
                    <FormDescription>
                      General notes about the customer (visible to customer)
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="internalNotes"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Internal Notes</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Internal notes..."
                        {...field}
                      />
                    </FormControl>
                    <FormDescription>
                      Internal notes for staff only (not visible to customer)
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          </Card>
        </div>
      )}
    </BaseForm>
  )
}

'use client'

import Link from 'next/link'
import { Button } from '@/components/ui/button'
import { LuPencil } from 'react-icons/lu'
import { BaseOrderCard } from './base-order-card'

interface Order {
  id: number
  productName: string
  quantity: number
  usageUnit?: string | null
  comment?: string | null
  storePrice: number
  pasabuyFee: number
  customerPrice: number
  resellerPrice: number // For compatibility
  isBought: boolean
  packingStatus: string
  imageFilename?: string
  storeCode?: {
    id: number
    code: string
    name: string
  }
  customer?: {
    id: number
    name: string
  }
}

interface OrderCardProps {
  item: Order
  isSelected: boolean
  isBulkMode: boolean
  onToggleSelection: (orderId: number) => void
  onLongPress: (orderId: number) => void
  onStatusUpdate: (orderId: number, field: 'isBought' | 'packingStatus', value: boolean | string) => void
  onCardClick: (orderId: number, event: React.MouseEvent) => void
  showBoughtToggle?: boolean
  showPackingToggle?: boolean
  showEditButton?: boolean
}

export function OrderCard({
  item,
  isSelected,
  isBulkMode,
  onToggleSelection,
  onLongPress,
  onStatusUpdate,
  onCardClick,
  showEditButton = true
}: OrderCardProps) {
  const actions = showEditButton ? (
    <div className="flex gap-1 flex-shrink-0">
      <Button
        variant="outline"
        size="sm"
        className="h-7 px-2 text-xs min-w-[1.75rem]"
        asChild
        title="Edit order"
        aria-label="Edit order"
      >
        <Link href={`/orders/${item.id}/edit`}>
          <LuPencil className="h-3 w-3" />
          <span className="sr-only">Edit</span>
        </Link>
      </Button>
    </div>
  ) : null

  return (
    <BaseOrderCard
      item={item}
      variant="default"
      isSelected={isSelected}
      isBulkMode={isBulkMode}
      onToggleSelection={onToggleSelection}
      onLongPress={onLongPress}
      onCardClick={onCardClick}
      onStatusUpdate={onStatusUpdate}
      actions={actions}
    />
  )
}

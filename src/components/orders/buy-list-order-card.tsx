'use client'

import Link from 'next/link'
import { Button } from '@/components/ui/button'
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from 'react-icons/lu'
import { BaseOrderCard } from './base-order-card'

interface Order {
  id: number
  productName: string
  quantity: number
  usageUnit?: string | null
  comment?: string | null
  storePrice: number
  pasabuyFee: number
  customerPrice: number
  resellerPrice: number // For compatibility
  isBought: boolean
  packingStatus: string
  imageFilename?: string | null
  storeCode?: {
    id: number
    code: string
    name: string | null
  } | null
  customer?: {
    id: number
    name: string
  } | null
}

interface BuyListItemCardProps {
  item: Order
  isSelected: boolean
  isBulkMode: boolean
  onToggleSelection: (orderId: number) => void
  onLongPress: (orderId: number) => void
  onMarkAsBought: (orderId: number) => void
  onCardClick: (orderId: number, event: React.MouseEvent) => void
}

export function BuyListItemCard({
  item,
  isSelected,
  isBulkMode,
  onToggleSelection,
  onLongPress,
  onMarkAsBought,
  onCardClick
}: BuyListItemCardProps) {
  const handleBoughtClick = (e: React.MouseEvent) => {
    e.preventDefault()
    e.stopPropagation()
    onMarkAsBought(item.id)
  }

  const actions = (
    <div className="flex gap-1 flex-shrink-0">
      <Button
        variant="outline"
        size="sm"
        className="h-6 px-1.5 text-xs min-w-[1.5rem]"
        asChild
        title="Edit order"
        aria-label="Edit order"
      >
        <Link href={`/orders/${item.id}/edit`}>
          <LuPencil className="h-2.5 w-2.5" />
          <span className="sr-only">Edit</span>
        </Link>
      </Button>
      <Button
        onClick={handleBoughtClick}
        className="bg-emerald-600 hover:bg-emerald-700 dark:bg-emerald-700 dark:hover:bg-emerald-600 h-6 px-1.5 text-xs whitespace-nowrap"
        size="sm"
        title="Mark as bought"
        aria-label="Mark as bought"
      >
        <LuCheck className="h-2.5 w-2.5 mr-1" />
        <span className="hidden sm:inline">Bought</span>
      </Button>
    </div>
  )

  return (
    <BaseOrderCard
      item={item}
      variant="buy-list"
      isSelected={isSelected}
      isBulkMode={isBulkMode}
      onToggleSelection={onToggleSelection}
      onLongPress={onLongPress}
      onCardClick={onCardClick}
      actions={actions}
    />
  )
}

import { useState, useEffect } from 'react'

/**
 * Custom hook for debouncing values
 * Consolidates debouncing logic used across multiple hooks
 */
export function useDebounce<T>(value: T, delay: number): T {
  const [debouncedValue, setDebouncedValue] = useState<T>(value)

  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedValue(value)
    }, delay)

    return () => clearTimeout(timer)
  }, [value, delay])

  return debouncedValue
}

/**
 * Custom hook for debounced callbacks
 * Useful for debouncing function calls
 */
export function useDebouncedCallback<T extends (...args: unknown[]) => unknown>(
  callback: T,
  delay: number
): T {
  const [debouncedCallback, setDebouncedCallback] = useState<T | null>(null)

  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedCallback(() => callback)
    }, delay)

    return () => clearTimeout(timer)
  }, [callback, delay])

  return (debouncedCallback || callback) as T
}

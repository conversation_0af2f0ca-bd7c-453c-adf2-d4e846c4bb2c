import { useState, useEffect, useCallback, useRef } from 'react'

interface ScrollState {
  scrollY: number
  scrollDirection: 'up' | 'down' | null
  isScrolled: boolean
  isScrollingDown: boolean
  isScrollingUp: boolean
}

interface UseScrollOptions {
  threshold?: number
  throttleMs?: number
  directionThreshold?: number
  debounceMs?: number
}

export function useScroll(options: UseScrollOptions = {}): ScrollState {
  const {
    threshold = 10,
    throttleMs = 16,
    directionThreshold = 5,
    debounceMs = 150
  } = options

  const [scrollState, setScrollState] = useState<ScrollState>({
    scrollY: 0,
    scrollDirection: null,
    isScrolled: false,
    isScrollingDown: false,
    isScrollingUp: false
  })

  // Refs to track scroll behavior and prevent flickering
  const lastScrollY = useRef(0)
  const scrollAccumulator = useRef(0)
  const directionDebounceTimer = useRef<NodeJS.Timeout | null>(null)
  const lastDirection = useRef<'up' | 'down' | null>(null)

  const updateScrollState = useCallback((currentScrollY: number) => {
    setScrollState(prevState => {
      const deltaY = currentScrollY - lastScrollY.current

      // Accumulate scroll distance to prevent micro-movements from changing direction
      scrollAccumulator.current += deltaY

      let newDirection: 'up' | 'down' | null = prevState.scrollDirection

      // Only change direction if we've accumulated enough scroll distance
      if (Math.abs(scrollAccumulator.current) > directionThreshold) {
        const detectedDirection = scrollAccumulator.current > 0 ? 'down' : 'up'

        // Only update direction if it's different from the last stable direction
        if (detectedDirection !== lastDirection.current) {
          // Clear any existing debounce timer
          if (directionDebounceTimer.current) {
            clearTimeout(directionDebounceTimer.current)
          }

          // Set a debounce timer to confirm the direction change
          directionDebounceTimer.current = setTimeout(() => {
            lastDirection.current = detectedDirection
            setScrollState(currentState => ({
              ...currentState,
              scrollDirection: detectedDirection,
              isScrollingDown: detectedDirection === 'down',
              isScrollingUp: detectedDirection === 'up'
            }))
          }, debounceMs)
        }

        newDirection = detectedDirection
        scrollAccumulator.current = 0 // Reset accumulator after direction change
      }

      lastScrollY.current = currentScrollY

      return {
        scrollY: currentScrollY,
        scrollDirection: newDirection,
        isScrolled: currentScrollY > threshold,
        isScrollingDown: newDirection === 'down',
        isScrollingUp: newDirection === 'up'
      }
    })
  }, [threshold, directionThreshold, debounceMs])

  useEffect(() => {
    let timeoutId: NodeJS.Timeout | null = null

    const handleScroll = () => {
      if (timeoutId) {
        clearTimeout(timeoutId)
      }

      timeoutId = setTimeout(() => {
        const currentScrollY = window.scrollY
        updateScrollState(currentScrollY)
      }, throttleMs)
    }

    // Set initial scroll position
    updateScrollState(window.scrollY)
    lastScrollY.current = window.scrollY

    window.addEventListener('scroll', handleScroll, { passive: true })

    return () => {
      window.removeEventListener('scroll', handleScroll)
      if (timeoutId) {
        clearTimeout(timeoutId)
      }
      if (directionDebounceTimer.current) {
        clearTimeout(directionDebounceTimer.current)
      }
    }
  }, [updateScrollState, throttleMs])

  return scrollState
}

// Hook specifically for header animations with hysteresis and stability
export function useHeaderScroll() {
  const scroll = useScroll({
    threshold: 20,
    throttleMs: 16,
    directionThreshold: 10, // Require more scroll distance to change direction
    debounceMs: 100 // Shorter debounce for header animations
  })

  const [isHeaderHidden, setIsHeaderHidden] = useState(false)
  const hideTimeoutRef = useRef<NodeJS.Timeout | null>(null)

  // Implement hysteresis: different thresholds for hiding vs showing
  const HIDE_THRESHOLD = 120 // Increased from 100px for more stability
  const SHOW_THRESHOLD = 30  // Decreased from 50px for better UX
  const MIN_HIDE_DELAY = 200 // Minimum delay before hiding to prevent accidental triggers

  useEffect(() => {
    // Clear any existing timeout
    if (hideTimeoutRef.current) {
      clearTimeout(hideTimeoutRef.current)
      hideTimeoutRef.current = null
    }

    // Logic for showing header (immediate)
    if (scroll.isScrollingUp || scroll.scrollY <= SHOW_THRESHOLD) {
      setIsHeaderHidden(false)
      return
    }

    // Logic for hiding header (with delay and stricter conditions)
    if (scroll.isScrollingDown && scroll.scrollY > HIDE_THRESHOLD) {
      hideTimeoutRef.current = setTimeout(() => {
        setIsHeaderHidden(true)
      }, MIN_HIDE_DELAY)
    }

    return () => {
      if (hideTimeoutRef.current) {
        clearTimeout(hideTimeoutRef.current)
      }
    }
  }, [scroll.isScrollingDown, scroll.isScrollingUp, scroll.scrollY])

  // Clean up timeout on unmount
  useEffect(() => {
    return () => {
      if (hideTimeoutRef.current) {
        clearTimeout(hideTimeoutRef.current)
      }
    }
  }, [])

  return {
    ...scroll,
    shouldHideHeader: isHeaderHidden,
    shouldShowHeader: !isHeaderHidden,
    headerOpacity: scroll.scrollY > 50 ? 0.95 : 1,
    // Debug info (can be removed in production)
    _debug: {
      isHeaderHidden,
      hideThreshold: HIDE_THRESHOLD,
      showThreshold: SHOW_THRESHOLD,
      hasHideTimeout: !!hideTimeoutRef.current
    }
  }
}

// Hook specifically for bottom navigation with simplified logic
export function useBottomNavScroll() {
  const scroll = useScroll({
    threshold: 10,
    throttleMs: 16,
    directionThreshold: 5,
    debounceMs: 50 // Faster response for bottom nav
  })

  const TOP_THRESHOLD = 30 // Show bottom nav only when very close to top

  // Simplified behavior:
  // - Bottom nav visible ONLY at top of page (scrollY <= 30px)
  // - FAB visible when scrolled away from top (scrollY > 30px)
  // - No "show on scroll up" behavior for bottom nav
  const isAtTop = scroll.scrollY <= TOP_THRESHOLD

  return {
    ...scroll,
    // Bottom navigation visibility
    shouldShowBottomNav: isAtTop,
    shouldHideBottomNav: !isAtTop,
    // FAB visibility (opposite of bottom nav)
    shouldShowFAB: !isAtTop,
    shouldHideFAB: isAtTop,
    // Debug info
    _debug: {
      scrollY: scroll.scrollY,
      topThreshold: TOP_THRESHOLD,
      isAtTop,
      bottomNavVisible: isAtTop,
      fabVisible: !isAtTop
    }
  }
}

// Hook for summary card behavior
export function useSummaryCardScroll() {
  const scroll = useScroll({
    threshold: 30,
    throttleMs: 16,
    directionThreshold: 8,
    debounceMs: 120
  })

  return {
    ...scroll,
    shouldMinimize: scroll.isScrollingDown && scroll.scrollY > 150,
    shouldExpand: scroll.isScrollingUp || scroll.scrollY <= 100,
    isSticky: scroll.scrollY > 80
  }
}

// Hook for automatic scroll-to-top behavior on page navigation
export function useScrollToTop() {
  useEffect(() => {
    // Check if user prefers reduced motion
    const prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)').matches

    // Scroll to top with appropriate behavior
    if (prefersReducedMotion) {
      // Instant scroll for users who prefer reduced motion
      window.scrollTo(0, 0)
    } else {
      // Smooth scroll for better UX
      window.scrollTo({
        top: 0,
        left: 0,
        behavior: 'smooth'
      })
    }
  }, []) // Empty dependency array means this runs once on mount
}

'use client'

import { useEffect, useState, useCallback, Suspense } from 'react'
import { useRouter } from 'next/navigation'
import { But<PERSON> } from '@/components/ui/button'
import { Card } from '@/components/ui/card'


import { LuPackage, LuFilter } from 'react-icons/lu'
import { useAppStore } from '@/lib/store'
import { FilterModal } from '@/components/filters/filter-modal'
import { useFilters } from '@/hooks/use-filters'
import { useBulkOperations, calculateSelectAllState } from '@/hooks/use-bulk-operations'
import { BulkActionsBar } from '@/components/bulk/bulk-actions-bar'
import { BulkConfirmationDialog } from '@/components/bulk/bulk-confirmation-dialog'
import { OrderCard } from '@/components/orders/order-card'
import { AnimatedSummaryCard } from '@/components/orders/animated-summary-card'
import { Pagination } from '@/components/ui/pagination'
import { PaginationInfo } from '@/components/ui/pagination-info'
import { PageWithSummary } from '@/components/layout/page-wrapper'


interface Order {
  id: number
  productName: string
  quantity: number
  usageUnit?: string | null
  comment?: string | null
  imageFilename?: string
  storePrice: number
  pasabuyFee: number
  customerPrice: number
  resellerPrice: number // For compatibility
  isBought: boolean
  packingStatus: string
  storeCode?: {
    id: number
    code: string
    name: string
  }
  customer?: {
    id: number
    name: string
  }
  createdAt: string
  updatedAt: string
}



function OrdersPageContent() {
  const router = useRouter()
  const { orders: storeOrders, setOrders, isLoadingOrders, setLoadingOrders } = useAppStore()
  const [error, setError] = useState<string | null>(null)
  const [unfileredPagination, setUnfilteredPagination] = useState({
    page: 1,
    limit: 15,
    total: 0,
    totalPages: 0,
    hasNext: false,
    hasPrev: false
  })

  // Scroll-based animations (currently unused)
  // const { isScrolled, shouldHideHeader, isScrollingDown, scrollY } = useHeaderScroll()

  // Bulk operations
  const bulkOps = useBulkOperations()
  const [confirmDialog, setConfirmDialog] = useState<{
    isOpen: boolean
    action: 'bought' | 'packed'
    isLoading: boolean
  }>({
    isOpen: false,
    action: 'bought',
    isLoading: false
  })

  // Advanced filtering
  const {
    filters,
    sort,
    isLoading: isFilterLoading,
    error: filterError,
    items: filteredOrders,
    pagination,
    updateFilter,
    updateSort,
    updatePagination,
    clearFilters,
    applyFilters
  } = useFilters({ autoApply: true })

  // Use filtered orders if filters are active, otherwise use store orders
  const hasActiveFilters = Object.keys(filters).length > 0 && Object.values(filters).some(value =>
    value !== undefined && value !== null &&
    (Array.isArray(value) ? value.length > 0 : true)
  )
  const rawDisplayOrders = hasActiveFilters ? filteredOrders : storeOrders
  // Temporarily bypass type guard to show orders
  const displayOrders = Array.isArray(rawDisplayOrders) ? rawDisplayOrders as Order[] : []
  const isLoading = hasActiveFilters ? isFilterLoading : isLoadingOrders
  const currentPagination = hasActiveFilters ? pagination : unfileredPagination



  // Fetch unfiltered orders with pagination
  const fetchUnfilteredOrders = useCallback(async (page = 1, limit = 15) => {
    try {
      setLoadingOrders(true)
      setError(null)

      const params = new URLSearchParams({
        page: page.toString(),
        limit: limit.toString()
      })

      const response = await fetch(`/api/orders?${params}`)
      if (!response.ok) {
        throw new Error('Failed to fetch orders')
      }

      const data = await response.json()

      // Handle both legacy and advanced response formats
      if (Array.isArray(data)) {
        // Legacy format - direct array
        setOrders(data)
        setUnfilteredPagination({
          page,
          limit,
          total: data.length,
          totalPages: Math.ceil(data.length / limit),
          hasNext: false,
          hasPrev: false
        })
      } else if (data && data.data && Array.isArray(data.data)) {
        // Advanced format - structured response
        setOrders(data.data)
        setUnfilteredPagination(data.pagination)
      } else {
        setOrders([])
        setUnfilteredPagination({
          page,
          limit,
          total: 0,
          totalPages: 0,
          hasNext: false,
          hasPrev: false
        })
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred')
    } finally {
      setLoadingOrders(false)
    }
  }, [setOrders, setLoadingOrders])

  useEffect(() => {
    // Only fetch unfiltered orders if no filters are active
    if (!hasActiveFilters) {
      fetchUnfilteredOrders(unfileredPagination.page, unfileredPagination.limit)
    }
  }, [hasActiveFilters, unfileredPagination.page, unfileredPagination.limit, fetchUnfilteredOrders])

  const handleStatusUpdate = async (orderId: number, field: 'isBought' | 'packingStatus', value: boolean | string) => {
    try {
      const response = await fetch(`/api/orders/${orderId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ [field]: value }),
      })

      if (!response.ok) {
        throw new Error('Failed to update order')
      }

      const updatedOrder = await response.json()

      // Update the order in the store
      setOrders(storeOrders.map(order => order.id === orderId ? updatedOrder : order))

      // If filters are active, refresh filtered results
      if (hasActiveFilters) {
        applyFilters()
      } else {
        // Refresh unfiltered orders
        fetchUnfilteredOrders(unfileredPagination.page, unfileredPagination.limit)
      }
    } catch (err) {
      console.error('Error updating order:', err)
      // You might want to show a toast notification here
    }
  }

  // Bulk operations handlers
  const handleBulkMarkAsBought = () => {
    setConfirmDialog({
      isOpen: true,
      action: 'bought',
      isLoading: false
    })
  }

  const handleBulkMarkAsPacked = () => {
    setConfirmDialog({
      isOpen: true,
      action: 'packed',
      isLoading: false
    })
  }

  const handleBulkConfirm = async () => {
    const selectedOrderIds = Array.from(bulkOps.selectedItems)
    if (selectedOrderIds.length === 0) return

    setConfirmDialog(prev => ({ ...prev, isLoading: true }))

    try {
      const updates = confirmDialog.action === 'bought'
        ? { isBought: true }
        : { packingStatus: 'Packed' }

      const response = await fetch('/api/orders/bulk', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          orderIds: selectedOrderIds,
          updates
        }),
      })

      if (!response.ok) {
        throw new Error('Failed to update orders')
      }

      const result = await response.json()

      // Update orders in store with the updated orders from the response
      if (result.updatedOrders && result.updatedOrders.length > 0) {
        setOrders(storeOrders.map(order => {
          const updatedOrder = result.updatedOrders.find((updated: Order) => updated.id === order.id)
          return updatedOrder || order
        }))
      }

      // Clear selection and close dialog
      bulkOps.clearSelection()
      setConfirmDialog({ isOpen: false, action: 'bought', isLoading: false })

      // If filters are active, refresh filtered results
      if (hasActiveFilters) {
        applyFilters()
      } else {
        // Refresh unfiltered orders
        fetchUnfilteredOrders(unfileredPagination.page, unfileredPagination.limit)
      }

      // Show success message (you might want to add a toast notification here)
      console.log(`Successfully updated ${result.updatedCount} orders`)
    } catch (err) {
      console.error('Error in bulk update:', err)
      setConfirmDialog(prev => ({ ...prev, isLoading: false }))
      // You might want to show an error toast notification here
    }
  }

  const handleBulkCancel = () => {
    setConfirmDialog({ isOpen: false, action: 'bought', isLoading: false })
  }

  // Long press handler for entering bulk mode
  const handleOrderLongPress = (orderId: number) => {
    bulkOps.selectItemAndEnterBulkMode(orderId)
  }

  // Pagination handlers
  const handlePageChange = (page: number) => {
    if (hasActiveFilters) {
      updatePagination(page)
    } else {
      setUnfilteredPagination(prev => ({ ...prev, page }))
    }
  }



  const handleCardClick = (orderId: number, event: React.MouseEvent) => {
    // Prevent navigation if clicking on interactive elements like buttons, toggles, or links
    // This ensures that existing functionality (Edit button, status toggles) continues to work
    const target = event.target as HTMLElement
    const isInteractiveElement = target.closest(
      'button, a, [role="button"], [data-radix-collection-item], [data-state], input, select, textarea'
    )

    if (!isInteractiveElement) {
      router.push(`/orders/${orderId}`)
    }
  }

  const getActiveFilterCount = () => {
    return Object.values(filters).filter(value =>
      value !== undefined && value !== null &&
      (Array.isArray(value) ? value.length > 0 : true)
    ).length
  }

  if (error || filterError) {
    return (
      <div className="space-y-6 py-4">
        <div className="flex items-center justify-between">
          <h1 className="text-2xl font-semibold tracking-tight">Orders</h1>
          <FilterModal
            filters={filters}
            sort={sort}
            onFiltersChange={(newFilters) => {
              Object.entries(newFilters).forEach(([key, value]) => {
                updateFilter(key as keyof typeof filters, value)
              })
            }}
            onSortChange={updateSort}
            onClear={clearFilters}
            hasActiveFilters={hasActiveFilters}
            activeFilterCount={getActiveFilterCount()}
          />
        </div>

        <Card className="p-6">
          <div className="text-center">
            <h2 className="text-sm font-medium text-red-600">Error</h2>
            <p className="text-sm text-muted-foreground mt-1">{error || filterError}</p>
            <Button
              onClick={() => window.location.reload()}
              className="mt-4"
            >
              Try Again
            </Button>
          </div>
        </Card>
      </div>
    )
  }

  return (
    <PageWithSummary
      title="Orders"
      actions={
        <FilterModal
          filters={filters}
          sort={sort}
          onFiltersChange={(newFilters) => {
            Object.entries(newFilters).forEach(([key, value]) => {
              updateFilter(key as keyof typeof filters, value)
            })
          }}
          onSortChange={updateSort}
          onClear={clearFilters}
          hasActiveFilters={hasActiveFilters}
          activeFilterCount={getActiveFilterCount()}
        />
      }
      summaryCard={
        displayOrders.length > 0 && !isLoading ? (
          <AnimatedSummaryCard orders={displayOrders} />
        ) : undefined
      }
    >
        {/* Main Content */}
        <div className="w-full">


          {/* Bulk Actions Bar */}
          {displayOrders.length > 0 && (bulkOps.isBulkMode || bulkOps.hasSelection) && (
            <div className="mb-3">
              <BulkActionsBar
                selectedCount={bulkOps.selectedCount}
                totalCount={displayOrders.length}
                isSelectAllChecked={calculateSelectAllState(bulkOps.selectedItems, displayOrders.map(order => order.id)).isSelectAllChecked}
                isSelectAllIndeterminate={calculateSelectAllState(bulkOps.selectedItems, displayOrders.map(order => order.id)).isSelectAllIndeterminate}
                isBulkMode={bulkOps.isBulkMode}
                onSelectAllChange={() => bulkOps.toggleSelectAll(displayOrders.map(order => order.id))}
                onClearSelection={bulkOps.clearSelection}
                onExitBulkMode={bulkOps.exitBulkMode}
                onMarkAsBought={handleBulkMarkAsBought}
                onMarkAsPacked={handleBulkMarkAsPacked}
                showBoughtAction={true}
                showPackedAction={true}
              />
            </div>
          )}

      {isLoading ? (
        <div className="grid gap-4">
          {[...Array(6)].map((_, i) => (
            <Card key={i} className="p-6">
              <div className="animate-pulse">
                <div className="flex items-start space-x-4">
                  <div className="w-16 h-16 bg-gray-200 rounded"></div>
                  <div className="flex-1">
                    <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                    <div className="h-3 bg-gray-200 rounded w-1/2 mt-2"></div>
                    <div className="h-3 bg-gray-200 rounded w-1/4 mt-2"></div>
                  </div>
                </div>
              </div>
            </Card>
          ))}
        </div>
      ) : displayOrders.length === 0 ? (
        <Card className="p-6">
          <div className="text-center">
            <LuPackage className="mx-auto h-12 w-12 text-muted-foreground" />
            <h2 className="text-sm font-medium mt-2">No orders found</h2>
            <p className="text-sm text-muted-foreground mt-1">Use the + button below to add your first order.</p>
          </div>
        </Card>
      ) : (
        <>
          <div className="grid gap-2">
            {displayOrders.map((order) => (
              <OrderCard
                key={order.id}
                item={order}
                isSelected={bulkOps.isItemSelected(order.id)}
                isBulkMode={bulkOps.isBulkMode}
                onToggleSelection={bulkOps.toggleItem}
                onLongPress={handleOrderLongPress}
                onStatusUpdate={handleStatusUpdate}
                onCardClick={handleCardClick}
                showEditButton={true}
              />
            ))}
          </div>

          {/* Pagination */}
          {currentPagination && currentPagination.totalPages > 1 && (
            <div className="mt-4 space-y-3">
              <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3">
                <PaginationInfo
                  currentPage={currentPagination.page}
                  totalPages={currentPagination.totalPages}
                  totalItems={currentPagination.total}
                  itemsPerPage={currentPagination.limit}
                  className="order-2 sm:order-1"
                />
                <Pagination
                  currentPage={currentPagination.page}
                  totalPages={currentPagination.totalPages}
                  onPageChange={handlePageChange}
                  className="order-1 sm:order-2"
                />
              </div>
            </div>
          )}
        </>
      )}
        </div>

      {/* Bulk Confirmation Dialog */}
      <BulkConfirmationDialog
        isOpen={confirmDialog.isOpen}
        onClose={handleBulkCancel}
        onConfirm={handleBulkConfirm}
        action={confirmDialog.action}
        selectedCount={bulkOps.selectedCount}
        isLoading={confirmDialog.isLoading}
      />
    </PageWithSummary>
  )
}

export default function OrdersPage() {
  return (
    <Suspense fallback={
      <PageWithSummary
        title="Orders"
        actions={
          <Button variant="outline" size="sm" disabled>
            <LuFilter className="mr-2 h-4 w-4" />
            Filters
          </Button>
        }
      >
        <div className="text-center py-8">Loading...</div>
      </PageWithSummary>
    }>
      <OrdersPageContent />
    </Suspense>
  )
}

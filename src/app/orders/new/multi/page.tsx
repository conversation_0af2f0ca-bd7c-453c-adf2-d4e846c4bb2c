'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { MultiOrderForm } from '@/components/forms/multi-order-form'
import { useAppStore } from '@/lib/store'
import { useScrollToTop } from '@/hooks/use-scroll'

type SingleOrderData = {
  productName: string
  quantity: number
  usageUnit?: string
  comment?: string
  storePrice: number
  pasabuyFee: number
  customerPrice: number
  storeCodeId?: string
  customerId?: string
  imageFile?: File
}

type MultiOrderData = {
  customerId?: string
  orders: SingleOrderData[]
}

export default function NewMultiOrderPage() {
  const router = useRouter()
  const { addOrder } = useAppStore()
  const [isLoading, setIsLoading] = useState(false)

  // Automatically scroll to top when page loads
  useScrollToTop()

  const handleSubmit = async (data: MultiOrderData) => {
    try {
      setIsLoading(true)

      // Process each order
      const createdOrders = []

      for (const [index, orderData] of data.orders.entries()) {
        // Handle image upload if present
        let imageFilename = null
        if (orderData.imageFile) {
          const formData = new FormData()
          formData.append('image', orderData.imageFile)

          const uploadResponse = await fetch('/api/upload', {
            method: 'POST',
            body: formData,
          })

          if (uploadResponse.ok) {
            const uploadResult = await uploadResponse.json()
            imageFilename = uploadResult.filename
          }
        }

        // Create the order using shared customer ID
        const processedOrderData = {
          productName: orderData.productName,
          quantity: orderData.quantity,
          usageUnit: orderData.usageUnit || null,
          comment: orderData.comment || null,
          storePrice: orderData.storePrice,
          pasabuyFee: orderData.pasabuyFee,
          customerPrice: orderData.customerPrice,
          storeCodeId: orderData.storeCodeId ? parseInt(orderData.storeCodeId) : null,
          customerId: data.customerId ? parseInt(data.customerId) : null, // Use shared customer ID
          isBought: false, // Default value for new orders
          packingStatus: 'Not Packed', // Default value for new orders
          imageFilename,
        }

        const response = await fetch('/api/orders', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(processedOrderData),
        })

        if (!response.ok) {
          throw new Error(`Failed to create order ${index + 1}`)
        }

        const newOrder = await response.json()
        createdOrders.push(newOrder)
        addOrder(newOrder)
      }

      // Navigate back to orders page
      router.push('/orders')
    } catch (error) {
      console.error('Error creating orders:', error)
      throw error // Re-throw to let the form handle the error display
    } finally {
      setIsLoading(false)
    }
  }

  const handleCancel = () => {
    router.back()
  }

  return (
    <div className="space-y-6 py-4">
      <div>
        <h1 className="text-2xl font-semibold tracking-tight">Add Multiple Orders</h1>
        <p className="text-muted-foreground">
          Create multiple orders at once using the table interface below.
        </p>
      </div>

      <MultiOrderForm
        onSubmit={handleSubmit}
        onCancel={handleCancel}
        isLoading={isLoading}
      />
    </div>
  )
}

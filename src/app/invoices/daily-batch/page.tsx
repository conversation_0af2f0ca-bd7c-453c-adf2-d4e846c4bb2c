'use client'

import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'

import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import Link from 'next/link'
import { LuArrowLeft, LuCalendar, LuPackage, LuFileText, LuUser, LuClock } from 'react-icons/lu'

interface BatchOrder {
  id: number
  productName: string
  quantity: number
  customerPrice: number
  totalPrice: number
}

interface BatchCustomer {
  customerId: number
  customerName: string
  orderCount: number
  totalValue: number
  orders: BatchOrder[]
}

interface BatchSummary {
  date: string
  eligibleCustomers: number
  totalOrders: number
  totalValue: number
  customers: BatchCustomer[]
}

interface CreatedInvoice {
  id: number
  invoiceNumber: string
  customerName: string
  total: number
  orderCount: number
}

export default function DailyBatchPage() {
  const [batchSummary, setBatchSummary] = useState<BatchSummary | null>(null)
  const [selectedDate, setSelectedDate] = useState(new Date().toISOString().split('T')[0])
  const [isLoading, setIsLoading] = useState(false)
  const [isProcessing, setIsProcessing] = useState(false)
  const [createdInvoices, setCreatedInvoices] = useState<CreatedInvoice[]>([])
  const [message, setMessage] = useState<string | null>(null)

  const fetchBatchSummary = async (date: string) => {
    setIsLoading(true)
    try {
      const response = await fetch(`/api/invoices/daily-batch?date=${date}`)
      if (response.ok) {
        const data = await response.json()
        setBatchSummary(data)
      } else {
        console.error('Failed to fetch batch summary')
      }
    } catch (error) {
      console.error('Error fetching batch summary:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const processBatch = async (customerId?: number) => {
    setIsProcessing(true)
    setMessage(null)
    try {
      const response = await fetch('/api/invoices/daily-batch', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          date: selectedDate,
          customerId,
          triggerType: 'manual-batch'
        }),
      })

      if (response.ok) {
        const result = await response.json()
        setCreatedInvoices(result.invoices || [])
        setMessage(result.message)
        // Refresh the summary to show updated state
        await fetchBatchSummary(selectedDate)
      } else {
        const error = await response.json()
        setMessage(error.error || 'Failed to process batch')
      }
    } catch (error) {
      console.error('Error processing batch:', error)
      setMessage('Error processing batch')
    } finally {
      setIsProcessing(false)
    }
  }

  useEffect(() => {
    fetchBatchSummary(selectedDate)
  }, [selectedDate])

  return (
    <div className="space-y-6 py-4">
      <div className="flex items-center gap-4">
        <Button variant="outline" size="icon" asChild className="min-h-[44px] min-w-[44px]">
          <Link href="/invoices">
            <LuArrowLeft className="h-5 w-5" />
          </Link>
        </Button>
        <div>
          <h1 className="text-2xl font-bold">Daily Invoice Batching</h1>
          <p className="text-muted-foreground">
            Generate consolidated invoices for packed orders by day
          </p>
        </div>
      </div>

      {/* Date Selection */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <LuCalendar className="h-5 w-5" />
            Select Date
          </CardTitle>
          <CardDescription>
            Choose the date to check for packed orders ready for invoicing
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex gap-4 items-end">
            <div className="flex-1 max-w-xs">
              <Label htmlFor="date">Date</Label>
              <Input
                id="date"
                type="date"
                value={selectedDate}
                onChange={(e) => setSelectedDate(e.target.value)}
                className="min-h-[44px]"
              />
            </div>
            <Button
              onClick={() => fetchBatchSummary(selectedDate)}
              disabled={isLoading}
              className="min-h-[44px]"
            >
              {isLoading ? 'Loading...' : 'Check Date'}
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Batch Summary */}
      {batchSummary && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <LuPackage className="h-5 w-5" />
              Batch Summary for {new Date(batchSummary.date).toLocaleDateString()}
            </CardTitle>
            <CardDescription>
              Orders packed on this date that are ready for invoicing
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">{batchSummary.eligibleCustomers}</div>
                <div className="text-sm text-muted-foreground">Customers</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">{batchSummary.totalOrders}</div>
                <div className="text-sm text-muted-foreground">Orders</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-purple-600">₱{batchSummary.totalValue.toLocaleString()}</div>
                <div className="text-sm text-muted-foreground">Total Value</div>
              </div>
              <div className="text-center">
                <Button
                  onClick={() => processBatch()}
                  disabled={isProcessing || batchSummary.eligibleCustomers === 0}
                  className="min-h-[44px] w-full"
                >
                  {isProcessing ? 'Processing...' : 'Process All'}
                </Button>
              </div>
            </div>

            {batchSummary.eligibleCustomers === 0 && (
              <div className="text-center py-8 text-muted-foreground">
                <LuClock className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <p>No packed orders found for this date that need invoicing.</p>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Customer Details */}
      {batchSummary && batchSummary.customers.length > 0 && (
        <div className="space-y-4">
          <h2 className="text-xl font-semibold">Customers Ready for Invoicing</h2>
          {batchSummary.customers.map((customer) => (
            <Card key={customer.customerId}>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <LuUser className="h-5 w-5" />
                    <div>
                      <CardTitle className="text-lg">{customer.customerName}</CardTitle>
                      <CardDescription>
                        {customer.orderCount} orders • ₱{customer.totalValue.toLocaleString()}
                      </CardDescription>
                    </div>
                  </div>
                  <Button
                    onClick={() => processBatch(customer.customerId)}
                    disabled={isProcessing}
                    className="min-h-[44px]"
                  >
                    {isProcessing ? 'Processing...' : 'Generate Invoice'}
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {customer.orders.map((order) => (
                    <div key={order.id} className="flex items-center justify-between py-2 border-b last:border-b-0">
                      <div className="flex-1">
                        <div className="font-medium">{order.productName}</div>
                        <div className="text-sm text-muted-foreground">
                          Qty: {order.quantity} • Unit: ₱{order.customerPrice.toLocaleString()}
                        </div>
                      </div>
                      <div className="font-medium">₱{order.totalPrice.toLocaleString()}</div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Results */}
      {message && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <LuFileText className="h-5 w-5" />
              Batch Processing Results
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="mb-4">{message}</p>
            {createdInvoices.length > 0 && (
              <div className="space-y-2">
                <h4 className="font-medium">Created Invoices:</h4>
                {createdInvoices.map((invoice) => (
                  <div key={invoice.id} className="flex items-center justify-between p-3 bg-muted rounded-lg">
                    <div>
                      <Link
                        href={`/invoices/${invoice.id}`}
                        className="font-medium text-blue-600 hover:underline"
                      >
                        {invoice.invoiceNumber}
                      </Link>
                      <div className="text-sm text-muted-foreground">
                        {invoice.customerName} • {invoice.orderCount} orders
                      </div>
                    </div>
                    <div className="font-medium">₱{invoice.total.toLocaleString()}</div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  )
}

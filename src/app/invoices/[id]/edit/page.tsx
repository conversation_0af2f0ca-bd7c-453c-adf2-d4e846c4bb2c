'use client'

import { useEffect, useState, useCallback } from 'react'
import { use<PERSON>ara<PERSON>, useRouter } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { Card } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Checkbox } from '@/components/ui/checkbox'
import Link from 'next/link'
import { LuArrowLeft, LuPlus, LuMinus, LuShoppingCart, LuTrash2 } from 'react-icons/lu'
import { Invoice, InvoiceStatus, Order } from '@/lib/store'

interface SelectedOrder {
  orderId: number
  order: Order
  quantity: number
}

const statusLabels: Record<InvoiceStatus, string> = {
  DRAFT: 'Draft',
  SENT: 'Sent',
  PAID: 'Paid',
  OVERDUE: 'Overdue',
  CANCELLED: 'Cancelled',
}

export default function EditInvoicePage() {
  const [invoice, setInvoice] = useState<Invoice | null>(null)
  const [availableOrders, setAvailableOrders] = useState<Order[]>([])
  const [selectedOrders, setSelectedOrders] = useState<SelectedOrder[]>([])
  const [status, setStatus] = useState<InvoiceStatus>('DRAFT')
  const [dueDate, setDueDate] = useState('')
  const [notes, setNotes] = useState('')
  const [isLoading, setIsLoading] = useState(true)
  const [isSaving, setIsSaving] = useState(false)
  const [invoiceId, setInvoiceId] = useState<string>('')
  const params = useParams()
  const router = useRouter()

  useEffect(() => {
    // Handle async params
    const resolveParams = async () => {
      const resolvedParams = await params
      setInvoiceId(resolvedParams.id as string)
    }
    resolveParams()
  }, [params])

  const fetchInvoice = useCallback(async () => {
    try {
      setIsLoading(true)
      const response = await fetch(`/api/invoices/${invoiceId}`)
      if (response.ok) {
        const data = await response.json()
        setInvoice(data)

        // Set form values
        setStatus(data.status)
        setDueDate(data.dueDate ? new Date(data.dueDate).toISOString().split('T')[0] : '')
        setNotes(data.notes || '')

        // Set selected orders
        const selectedOrdersData = data.invoiceItems?.map((invoiceOrder: any) => ({
          orderId: invoiceOrder.orderId,
          order: invoiceOrder.order,
          quantity: invoiceOrder.quantity
        })) || []
        setSelectedOrders(selectedOrdersData)

        // Fetch available orders for this customer
        if (data.customerId) {
          fetchAvailableOrders(data.customerId)
        }
      } else {
        router.push('/invoices')
      }
    } catch (error) {
      console.error('Error fetching invoice:', error)
      router.push('/invoices')
    } finally {
      setIsLoading(false)
    }
  }, [invoiceId])

  const fetchAvailableOrders = async (customerId: number) => {
    try {
      const response = await fetch(`/api/orders?customerId=${customerId}&isBought=true`)
      if (response.ok) {
        const data = await response.json()
        setAvailableOrders(data)
      }
    } catch (error) {
      console.error('Error fetching available orders:', error)
    }
  }

  useEffect(() => {
    if (invoiceId) {
      fetchInvoice()
    }
  }, [invoiceId, fetchInvoice])

  const handleOrderToggle = (order: Order, checked: boolean) => {
    if (checked) {
      // Add order if not already selected
      if (!selectedOrders.find(so => so.orderId === order.id)) {
        setSelectedOrders(prev => [...prev, { orderId: order.id, order, quantity: 1 }])
      }
    } else {
      // Remove order
      setSelectedOrders(prev => prev.filter(so => so.orderId !== order.id))
    }
  }

  const updateOrderQuantity = (orderId: number, quantity: number) => {
    if (quantity < 1) return
    setSelectedOrders(prev =>
      prev.map(so => so.orderId === orderId ? { ...so, quantity } : so)
    )
  }

  const removeOrder = (orderId: number) => {
    setSelectedOrders(prev => prev.filter(so => so.orderId !== orderId))
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-PH', {
      style: 'currency',
      currency: 'PHP',
    }).format(amount)
  }

  const calculateTotal = () => {
    return selectedOrders.reduce((total, so) => total + (so.quantity * so.order.customerPrice), 0)
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (selectedOrders.length === 0) {
      alert('Please select at least one order')
      return
    }

    try {
      setIsSaving(true)

      const updateData = {
        status,
        dueDate: dueDate || null,
        notes: notes || null,
        orders: selectedOrders.map(so => ({
          orderId: so.orderId,
          quantity: so.quantity
        }))
      }

      const response = await fetch(`/api/invoices/${invoiceId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updateData),
      })

      if (response.ok) {
        router.push(`/invoices/${invoiceId}`)
      } else {
        const error = await response.json()
        alert(error.error || 'Failed to update invoice')
      }
    } catch (error) {
      console.error('Error updating invoice:', error)
      alert('Failed to update invoice')
    } finally {
      setIsSaving(false)
    }
  }

  if (isLoading) {
    return (
      <div className="space-y-6 py-4">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/3 mb-4"></div>
          <div className="h-4 bg-gray-200 rounded w-1/2"></div>
        </div>
      </div>
    )
  }

  if (!invoice) {
    return (
      <div className="space-y-6 py-4">
        <div className="text-center">
          <h1 className="text-2xl font-bold">Invoice not found</h1>
          <Link href="/invoices">
            <Button className="mt-4 min-h-[44px]">Back to Invoices</Button>
          </Link>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6 py-4">
      {/* Header */}
      <div className="flex items-center gap-4">
        <Link href={`/invoices/${invoiceId}`}>
          <Button variant="outline" size="icon" className="min-w-[44px] min-h-[44px]">
            <LuArrowLeft className="h-5 w-5" />
          </Button>
        </Link>
        <div>
          <h1 className="text-2xl font-bold">Edit Invoice {invoice.invoiceNumber}</h1>
          <p className="text-muted-foreground">
            Modify invoice details and line items for {invoice.customer?.name}
          </p>
        </div>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Invoice Details */}
        <Card className="p-6">
          <h3 className="text-base font-medium mb-4">Invoice Details</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <Label htmlFor="status">Status</Label>
              <Select value={status} onValueChange={(value) => setStatus(value as InvoiceStatus)}>
                <SelectTrigger className="min-h-[44px]">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {Object.entries(statusLabels).map(([statusValue, label]) => (
                    <SelectItem key={statusValue} value={statusValue}>
                      {label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="dueDate">Due Date</Label>
              <Input
                id="dueDate"
                type="date"
                value={dueDate}
                onChange={(e) => setDueDate(e.target.value)}
                className="min-h-[44px]"
              />
            </div>

            <div className="space-y-2">
              <Label>Invoice Number</Label>
              <Input
                value={invoice.invoiceNumber}
                disabled
                className="min-h-[44px] bg-muted"
              />
            </div>
          </div>

          <div className="mt-4 space-y-2">
            <Label htmlFor="notes">Notes</Label>
            <Input
              id="notes"
              placeholder="Optional notes for the invoice..."
              value={notes}
              onChange={(e) => setNotes(e.target.value)}
              className="min-h-[44px]"
            />
          </div>
        </Card>

        {/* Current Invoice Orders */}
        <Card className="p-6">
          <h3 className="text-base font-medium mb-4">Invoice Orders</h3>
          {selectedOrders.length === 0 ? (
            <div className="text-center py-8">
              <LuShoppingCart className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
              <p className="text-muted-foreground">
                No orders selected. Add orders from the available orders below.
              </p>
            </div>
          ) : (
            <div className="space-y-3">
              {selectedOrders.map((selectedOrder) => (
                <div key={selectedOrder.orderId} className="flex items-center gap-4 p-4 border rounded-lg">
                  <div className="flex-1 min-w-0">
                    <h4 className="font-medium truncate">{selectedOrder.order.productName}</h4>
                    <p className="text-sm text-muted-foreground">
                      {formatCurrency(selectedOrder.order.customerPrice)} each
                    </p>
                    {selectedOrder.order.storeCode && (
                      <p className="text-xs text-muted-foreground">
                        Store: {selectedOrder.order.storeCode.name || selectedOrder.order.storeCode.code}
                      </p>
                    )}
                  </div>

                  <div className="flex items-center gap-2">
                    <Button
                      type="button"
                      variant="outline"
                      size="icon"
                      className="h-8 w-8"
                      onClick={() => updateOrderQuantity(selectedOrder.orderId, selectedOrder.quantity - 1)}
                      disabled={selectedOrder.quantity <= 1}
                      title="Decrease quantity"
                      aria-label="Decrease quantity"
                    >
                      <LuMinus className="h-4 w-4" />
                    </Button>
                    <span className="w-8 text-center text-sm font-medium">
                      {selectedOrder.quantity}
                    </span>
                    <Button
                      type="button"
                      variant="outline"
                      size="icon"
                      className="h-8 w-8"
                      onClick={() => updateOrderQuantity(selectedOrder.orderId, selectedOrder.quantity + 1)}
                      title="Increase quantity"
                      aria-label="Increase quantity"
                    >
                      <LuPlus className="h-4 w-4" />
                    </Button>
                  </div>

                  <div className="text-right">
                    <p className="font-medium">
                      {formatCurrency(selectedOrder.quantity * selectedOrder.order.customerPrice)}
                    </p>
                  </div>

                  <Button
                    type="button"
                    variant="outline"
                    size="icon"
                    className="h-8 w-8 text-destructive hover:text-destructive"
                    onClick={() => removeOrder(selectedOrder.orderId)}
                    title="Remove order"
                    aria-label="Remove order"
                  >
                    <LuTrash2 className="h-4 w-4" />
                  </Button>
                </div>
              ))}
            </div>
          )}
        </Card>

        {/* Available Orders to Add */}
        <Card className="p-6">
          <h3 className="text-base font-medium mb-4">Add Orders</h3>
          {availableOrders.length === 0 ? (
            <div className="text-center py-8">
              <LuShoppingCart className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
              <p className="text-muted-foreground">
                No additional orders available for this customer
              </p>
            </div>
          ) : (
            <div className="space-y-3">
              {availableOrders.map((order) => {
                const isSelected = selectedOrders.some(so => so.orderId === order.id)

                return (
                  <div key={order.id} className="flex items-center gap-4 p-3 border rounded-lg">
                    <Checkbox
                      checked={isSelected}
                      onCheckedChange={(checked) => handleOrderToggle(order, checked as boolean)}
                    />

                    <div className="flex-1 min-w-0">
                      <h4 className="font-medium truncate">{order.productName}</h4>
                      <p className="text-sm text-muted-foreground">
                        {formatCurrency(order.customerPrice)} each
                      </p>
                      {order.storeCode && (
                        <p className="text-xs text-muted-foreground">
                          Store: {order.storeCode.name || order.storeCode.code}
                        </p>
                      )}
                    </div>

                    <div className="text-right">
                      <p className="font-medium">
                        {formatCurrency(order.customerPrice)}
                      </p>
                    </div>
                  </div>
                )
              })}
            </div>
          )}
        </Card>

        {/* Summary */}
        {selectedOrders.length > 0 && (
          <Card className="p-6">
            <h3 className="text-base font-medium mb-4">Invoice Summary</h3>
            <div className="space-y-2">
              <div className="flex justify-between">
                <span>Orders:</span>
                <span>{selectedOrders.length}</span>
              </div>
              <div className="flex justify-between">
                <span>Total Quantity:</span>
                <span>{selectedOrders.reduce((sum, so) => sum + so.quantity, 0)}</span>
              </div>
              <div className="flex justify-between text-lg font-medium">
                <span>Total:</span>
                <span>{formatCurrency(calculateTotal())}</span>
              </div>
            </div>
          </Card>
        )}

        {/* Actions */}
        <div className="flex flex-col sm:flex-row gap-4">
          <Link href={`/invoices/${invoiceId}`} className="flex-1">
            <Button variant="outline" className="w-full min-h-[44px]">
              Cancel
            </Button>
          </Link>
          <Button
            type="submit"
            disabled={isSaving || selectedOrders.length === 0}
            className="flex-1 min-h-[44px]"
          >
            {isSaving ? 'Saving...' : 'Save Changes'}
          </Button>
        </div>
      </form>
    </div>
  )
}

import { NextRequest, NextResponse } from 'next/server'
import { EnhancedInvoiceService } from '@/lib/enhanced-invoice-service'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    
    // Parse filters
    const filters: any = {}
    
    if (searchParams.get('customerId')) filters.customerId = parseInt(searchParams.get('customerId')!)
    if (searchParams.get('status')) {
      filters.status = searchParams.get('status')!.split(',')
    }
    if (searchParams.get('invoiceType')) {
      filters.invoiceType = searchParams.get('invoiceType')!.split(',')
    }
    if (searchParams.get('priority')) {
      filters.priority = searchParams.get('priority')!.split(',')
    }
    if (searchParams.get('approvalStatus')) {
      filters.approvalStatus = searchParams.get('approvalStatus')!.split(',')
    }
    
    // Date filters
    if (searchParams.get('issueDateAfter')) {
      filters.issueDateAfter = new Date(searchParams.get('issueDateAfter')!)
    }
    if (searchParams.get('issueDateBefore')) {
      filters.issueDateBefore = new Date(searchParams.get('issueDateBefore')!)
    }
    if (searchParams.get('dueDateAfter')) {
      filters.dueDateAfter = new Date(searchParams.get('dueDateAfter')!)
    }
    if (searchParams.get('dueDateBefore')) {
      filters.dueDateBefore = new Date(searchParams.get('dueDateBefore')!)
    }
    
    // Amount filters
    if (searchParams.get('minTotal')) filters.minTotal = parseFloat(searchParams.get('minTotal')!)
    if (searchParams.get('maxTotal')) filters.maxTotal = parseFloat(searchParams.get('maxTotal')!)
    
    // Status filters
    if (searchParams.get('isOverdue')) filters.isOverdue = searchParams.get('isOverdue') === 'true'
    if (searchParams.get('isPaid')) filters.isPaid = searchParams.get('isPaid') === 'true'
    if (searchParams.get('needsApproval')) filters.needsApproval = searchParams.get('needsApproval') === 'true'
    
    // Search
    if (searchParams.get('searchTerm')) filters.searchTerm = searchParams.get('searchTerm')
    
    // Pagination
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '50')
    const pagination = { page, limit }
    
    // Sorting
    const sortField = searchParams.get('sortField') || 'issueDate'
    const sortDirection = (searchParams.get('sortDirection') || 'desc') as 'asc' | 'desc'
    const sorting = { field: sortField, direction: sortDirection }
    
    const result = await EnhancedInvoiceService.searchInvoices(filters, pagination, sorting)
    
    return NextResponse.json(result)
  } catch (error) {
    console.error('Error searching invoices:', error)
    return NextResponse.json(
      { error: 'Error searching invoices' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { invoiceData, context } = body
    
    if (!invoiceData || !invoiceData.customerId || !invoiceData.orderIds || invoiceData.orderIds.length === 0) {
      return NextResponse.json(
        { error: 'Customer ID and order IDs are required' },
        { status: 400 }
      )
    }
    
    const invoice = await EnhancedInvoiceService.createInvoice(invoiceData, context)
    
    return NextResponse.json(invoice, { status: 201 })
  } catch (error) {
    console.error('Error creating invoice:', error)
    return NextResponse.json(
      { error: 'Error creating invoice' },
      { status: 500 }
    )
  }
}

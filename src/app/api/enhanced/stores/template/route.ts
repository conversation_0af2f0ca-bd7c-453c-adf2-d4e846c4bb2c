import { NextRequest, NextResponse } from 'next/server'
import { EnhancedStoreService } from '@/lib/enhanced-store-service'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const format = (searchParams.get('format') || 'EXCEL') as 'CSV' | 'EXCEL'

    // Generate template
    const buffer = EnhancedStoreService.generateImportTemplate(format)

    // Set response headers
    const filename = `store_import_template`
    const contentType = format === 'CSV' 
      ? 'text/csv' 
      : 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    const fileExtension = format === 'CSV' ? 'csv' : 'xlsx'

    return new NextResponse(buffer, {
      headers: {
        'Content-Type': contentType,
        'Content-Disposition': `attachment; filename="${filename}.${fileExtension}"`,
        'Content-Length': buffer.length.toString(),
      },
    })
  } catch (error) {
    console.error('Error generating template:', error)
    return NextResponse.json(
      { error: 'Internal server error generating template' },
      { status: 500 }
    )
  }
}

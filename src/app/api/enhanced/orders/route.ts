import { NextRequest, NextResponse } from 'next/server'
import { EnhancedOrderService } from '@/lib/enhanced-order-service'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    
    // Parse filters
    const filters: any = {}
    
    // Basic filters
    if (searchParams.get('productName')) filters.productName = searchParams.get('productName')
    if (searchParams.get('customerId')) filters.customerId = parseInt(searchParams.get('customerId')!)
    if (searchParams.get('storeCodeId')) filters.storeCodeId = parseInt(searchParams.get('storeCodeId')!)
    if (searchParams.get('isBought')) filters.isBought = searchParams.get('isBought') === 'true'
    if (searchParams.get('packingStatus')) filters.packingStatus = searchParams.get('packingStatus')
    if (searchParams.get('deliveryStatus')) filters.deliveryStatus = searchParams.get('deliveryStatus')
    
    // Enhanced filters
    if (searchParams.get('priority')) {
      filters.priority = searchParams.get('priority')!.split(',')
    }
    if (searchParams.get('urgency')) {
      filters.urgency = searchParams.get('urgency')!.split(',')
    }
    if (searchParams.get('source')) {
      filters.source = searchParams.get('source')!.split(',')
    }
    if (searchParams.get('category')) {
      filters.category = searchParams.get('category')!.split(',')
    }
    if (searchParams.get('brand')) {
      filters.brand = searchParams.get('brand')!.split(',')
    }
    if (searchParams.get('tags')) {
      filters.tags = searchParams.get('tags')!.split(',')
    }
    
    // Date filters
    if (searchParams.get('createdAfter')) {
      filters.createdAfter = new Date(searchParams.get('createdAfter')!)
    }
    if (searchParams.get('createdBefore')) {
      filters.createdBefore = new Date(searchParams.get('createdBefore')!)
    }
    if (searchParams.get('estimatedDeliveryAfter')) {
      filters.estimatedDeliveryAfter = new Date(searchParams.get('estimatedDeliveryAfter')!)
    }
    if (searchParams.get('estimatedDeliveryBefore')) {
      filters.estimatedDeliveryBefore = new Date(searchParams.get('estimatedDeliveryBefore')!)
    }
    
    // Price filters
    if (searchParams.get('minPrice')) filters.minPrice = parseFloat(searchParams.get('minPrice')!)
    if (searchParams.get('maxPrice')) filters.maxPrice = parseFloat(searchParams.get('maxPrice')!)
    
    // Status filters
    if (searchParams.get('hasDiscount')) filters.hasDiscount = searchParams.get('hasDiscount') === 'true'
    if (searchParams.get('isOverdue')) filters.isOverdue = searchParams.get('isOverdue') === 'true'
    if (searchParams.get('hasIssues')) filters.hasIssues = searchParams.get('hasIssues') === 'true'
    
    // Search
    if (searchParams.get('searchTerm')) filters.searchTerm = searchParams.get('searchTerm')
    
    // Pagination
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '50')
    const pagination = { page, limit }
    
    // Sorting
    const sortField = searchParams.get('sortField') || 'createdAt'
    const sortDirection = (searchParams.get('sortDirection') || 'desc') as 'asc' | 'desc'
    const sorting = { field: sortField, direction: sortDirection }
    
    const result = await EnhancedOrderService.searchOrders(filters, pagination, sorting)
    
    return NextResponse.json(result)
  } catch (error) {
    console.error('Error searching orders:', error)
    return NextResponse.json(
      { error: 'Error searching orders' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { orderData, context } = body
    
    if (!orderData) {
      return NextResponse.json(
        { error: 'Order data is required' },
        { status: 400 }
      )
    }
    
    const order = await EnhancedOrderService.createOrder(orderData, context)
    
    return NextResponse.json(order, { status: 201 })
  } catch (error) {
    console.error('Error creating order:', error)
    return NextResponse.json(
      { error: 'Error creating order' },
      { status: 500 }
    )
  }
}

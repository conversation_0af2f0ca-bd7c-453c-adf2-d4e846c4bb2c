import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/db'

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    const invoiceId = parseInt(id)

    if (isNaN(invoiceId)) {
      return NextResponse.json(
        { error: 'Invalid invoice ID' },
        { status: 400 }
      )
    }

    const invoice = await prisma.invoice.findUnique({
      where: { id: invoiceId },
      include: {
        customer: true,
        invoiceItems: {
          include: {
            order: {
              include: {
                storeCode: true
              }
            }
          }
        }
      }
    })

    if (!invoice) {
      return NextResponse.json(
        { error: 'Invoice not found' },
        { status: 404 }
      )
    }

    // Return invoice data for client-side PDF generation
    return NextResponse.json(invoice)
  } catch (error) {
    console.error('Error fetching invoice for PDF:', error)
    return NextResponse.json(
      { error: 'Error fetching invoice' },
      { status: 500 }
    )
  }
}

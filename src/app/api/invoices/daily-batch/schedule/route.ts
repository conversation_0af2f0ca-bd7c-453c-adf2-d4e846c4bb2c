import { NextRequest, NextResponse } from 'next/server'
import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

export async function POST(request: NextRequest) {
  try {
    // Verify this is an authorized request (you might want to add API key validation)
    const authHeader = request.headers.get('authorization')
    const expectedToken = process.env.CRON_SECRET || 'your-secret-token'

    if (authHeader !== `Bearer ${expectedToken}`) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const { date, dryRun = false } = body

    // Use provided date or default to today
    const targetDate = date ? new Date(date) : new Date()
    const startOfDay = new Date(targetDate)
    startOfDay.setHours(0, 0, 0, 0)

    const endOfDay = new Date(targetDate)
    endOfDay.setHours(23, 59, 59, 999)

    console.log(`🕐 Daily batch scheduler running for ${targetDate.toLocaleDateString()} (dry run: ${dryRun})`)

    // Find all customers with packed orders from today
    const customers = await prisma.customer.findMany({
      include: {
        orders: {
          where: {
            isBought: true,
            packingStatus: 'Packed',
            updatedAt: {
              gte: startOfDay,
              lte: endOfDay
            },
            // Exclude orders already in invoices
            NOT: {
              invoiceItems: {
                some: {}
              }
            }
          },
          include: {
            storeCode: true
          }
        }
      }
    })

    const eligibleCustomers = customers.filter(customer => customer.orders.length > 0)

    if (eligibleCustomers.length === 0) {
      console.log('📭 No eligible orders found for daily batch processing')
      return NextResponse.json({
        success: true,
        message: 'No eligible orders found for daily batch processing',
        date: targetDate.toISOString().split('T')[0],
        eligibleCustomers: 0,
        invoicesCreated: 0,
        dryRun
      })
    }

    console.log(`📊 Found ${eligibleResellers.length} resellers with ${eligibleResellers.reduce((sum, r) => sum + r.items.length, 0)} items for batch processing`)

    if (dryRun) {
      const summary = eligibleResellers.map(reseller => ({
        resellerId: reseller.id,
        resellerName: reseller.name,
        itemCount: reseller.items.length,
        totalValue: reseller.items.reduce((sum, item) => sum + (item.resellerPrice * item.quantity), 0)
      }))

      return NextResponse.json({
        success: true,
        message: `Dry run completed - would create ${eligibleResellers.length} invoices`,
        date: targetDate.toISOString().split('T')[0],
        eligibleResellers: eligibleResellers.length,
        totalItems: eligibleResellers.reduce((sum, r) => sum + r.items.length, 0),
        totalValue: summary.reduce((sum, r) => sum + r.totalValue, 0),
        resellers: summary,
        dryRun: true
      })
    }

    // Process each reseller
    const createdInvoices = []
    const errors = []

    for (const reseller of eligibleResellers) {
      try {
        // Get current invoice count for numbering
        const invoiceCount = await prisma.invoice.count()
        const invoiceNumber = `INV-${String(invoiceCount + 1).padStart(6, '0')}`

        // Calculate totals
        let subtotal = 0
        const invoiceItemsData = reseller.items.map((item) => {
          const quantity = item.quantity
          const unitPrice = item.resellerPrice
          const totalPrice = quantity * unitPrice

          subtotal += totalPrice

          return {
            itemId: item.id,
            quantity,
            unitPrice,
            totalPrice
          }
        })

        const total = subtotal
        const dueDate = new Date()
        dueDate.setDate(dueDate.getDate() + 30) // 30 days from now

        // Create invoice with transaction
        const invoice = await prisma.invoice.create({
          data: {
            invoiceNumber,
            resellerId: reseller.id,
            status: 'DRAFT',
            subtotal,
            total,
            dueDate,
            notes: `Daily batch auto-generated invoice for ${targetDate.toLocaleDateString()} (scheduled)`,
            invoiceItems: {
              create: invoiceItemsData
            }
          },
          include: {
            reseller: true,
            invoiceItems: {
              include: {
                item: true
              }
            }
          }
        })

        createdInvoices.push({
          id: invoice.id,
          invoiceNumber: invoice.invoiceNumber,
          resellerName: invoice.reseller?.name,
          total: invoice.total,
          itemCount: invoice.invoiceItems.length
        })

        console.log(`✅ Created invoice ${invoice.invoiceNumber} for ${reseller.name} with ${reseller.items.length} items (₱${total.toLocaleString()})`)
      } catch (error) {
        console.error(`❌ Error creating invoice for reseller ${reseller.name}:`, error)
        errors.push({
          resellerId: reseller.id,
          resellerName: reseller.name,
          error: error instanceof Error ? error.message : 'Unknown error'
        })
      }
    }

    const successCount = createdInvoices.length
    const errorCount = errors.length

    console.log(`🎉 Daily batch completed: ${successCount} invoices created, ${errorCount} errors`)

    return NextResponse.json({
      success: true,
      message: `Daily batch completed: ${successCount} invoice${successCount !== 1 ? 's' : ''} created${errorCount > 0 ? `, ${errorCount} error${errorCount !== 1 ? 's' : ''}` : ''}`,
      date: targetDate.toISOString().split('T')[0],
      eligibleResellers: eligibleResellers.length,
      invoicesCreated: successCount,
      errors: errorCount,
      createdInvoices,
      errorDetails: errors,
      dryRun: false
    }, { status: 201 })
  } catch (error) {
    console.error('❌ Error in daily batch scheduler:', error)
    return NextResponse.json(
      { error: 'Error in daily batch scheduler' },
      { status: 500 }
    )
  }
}

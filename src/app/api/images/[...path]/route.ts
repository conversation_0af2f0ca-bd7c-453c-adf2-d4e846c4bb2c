import { NextRequest, NextResponse } from 'next/server'
import { readFile } from 'fs/promises'
import { join } from 'path'
import { existsSync } from 'fs'

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ path: string[] }> }
) {
  try {
    const { path } = await params
    const imagePath = path.join('/')
    
    // Construct the full file path
    const fullPath = join(process.cwd(), 'public', 'uploads', imagePath)
    
    // Check if file exists
    if (!existsSync(fullPath)) {
      return new NextResponse('Image not found', { status: 404 })
    }
    
    // Read the file
    const fileBuffer = await readFile(fullPath)
    
    // Determine content type based on file content
    const fileContent = fileBuffer.toString('utf8', 0, 100) // Read first 100 chars
    let contentType = 'image/jpeg' // default
    
    if (fileContent.startsWith('<?xml') || fileContent.includes('<svg')) {
      contentType = 'image/svg+xml'
    } else if (fileContent.startsWith('\x89PNG')) {
      contentType = 'image/png'
    } else if (fileContent.startsWith('\xFF\xD8\xFF')) {
      contentType = 'image/jpeg'
    } else if (fileContent.startsWith('GIF8')) {
      contentType = 'image/gif'
    } else if (fileContent.startsWith('RIFF') && fileContent.includes('WEBP')) {
      contentType = 'image/webp'
    }
    
    // Return the image with correct content type
    return new NextResponse(fileBuffer, {
      headers: {
        'Content-Type': contentType,
        'Cache-Control': 'public, max-age=31536000, immutable',
      },
    })
  } catch (error) {
    console.error('Error serving image:', error)
    return new NextResponse('Internal Server Error', { status: 500 })
  }
}

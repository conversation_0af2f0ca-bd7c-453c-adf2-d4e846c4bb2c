import { NextRequest, NextResponse } from 'next/server'
import { appwriteStoreService } from '@/lib/services/appwrite-store-service'
import { authService } from '@/lib/auth'

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Check authentication
    const userId = await authService.getUserId()
    if (!userId) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    const { id } = await params

    const storeCode = await appwriteStoreService.getStoreCodeById(id, userId)

    if (!storeCode) {
      return NextResponse.json(
        { error: 'Store code not found' },
        { status: 404 }
      )
    }

    return NextResponse.json(storeCode)
  } catch (error: unknown) {
    console.error('Error fetching store code:', error)
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Error fetching store code' },
      { status: 500 }
    )
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Check authentication
    const userId = await authService.getUserId()
    if (!userId) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    const { id } = await params
    const body = await request.json()

    const storeCode = await appwriteStoreService.updateStoreCode(id, body, userId)

    return NextResponse.json(storeCode)
  } catch (error: unknown) {
    console.error('Error updating store code:', error)

    const errorMessage = error instanceof Error ? error.message : ''

    if (errorMessage.includes('not found')) {
      return NextResponse.json(
        { error: 'Store code not found' },
        { status: 404 }
      )
    }

    if (errorMessage.includes('already exists')) {
      return NextResponse.json(
        { error: 'Store code already exists' },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Error updating store code' },
      { status: 500 }
    )
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Check authentication
    const userId = await authService.getUserId()
    if (!userId) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    const { id } = await params

    await appwriteStoreService.deleteStoreCode(id, userId)

    return NextResponse.json({ success: true })
  } catch (error: unknown) {
    console.error('Error deleting store code:', error)

    const errorMessage = error instanceof Error ? error.message : ''

    if (errorMessage.includes('not found')) {
      return NextResponse.json(
        { error: 'Store code not found' },
        { status: 404 }
      )
    }

    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Error deleting store code' },
      { status: 500 }
    )
  }
}

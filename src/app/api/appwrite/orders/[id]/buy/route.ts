import { NextRequest, NextResponse } from 'next/server'
import { appwriteOrderService } from '@/lib/services/appwrite-order-service'
import { authService } from '@/lib/auth'

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Check authentication
    const userId = await authService.getUserId()
    if (!userId) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    const { id } = await params
    const body = await request.json()
    const { storePrice, pasabuyFee, customerPrice } = body

    if (storePrice === undefined || pasabuyFee === undefined || customerPrice === undefined) {
      return NextResponse.json(
        { error: 'Store price, pasabuy fee, and customer price are required' },
        { status: 400 }
      )
    }

    const order = await appwriteOrderService.markAsBought(
      id, 
      storePrice, 
      pasabuyFee, 
      customerPrice, 
      userId
    )

    return NextResponse.json(order)
  } catch (error: unknown) {
    console.error('Error marking order as bought:', error)

    const errorMessage = error instanceof Error ? error.message : ''

    if (errorMessage.includes('not found')) {
      return NextResponse.json(
        { error: 'Order not found' },
        { status: 404 }
      )
    }

    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Error marking order as bought' },
      { status: 500 }
    )
  }
}

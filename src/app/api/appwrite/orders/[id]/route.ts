import { NextRequest, NextResponse } from 'next/server'
import { appwriteOrderService } from '@/lib/services/appwrite-order-service'
import { authService } from '@/lib/auth'

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Check authentication
    const userId = await authService.getUserId()
    if (!userId) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    const { id } = await params

    const order = await appwriteOrderService.getOrderById(id, userId)

    if (!order) {
      return NextResponse.json(
        { error: 'Order not found' },
        { status: 404 }
      )
    }

    return NextResponse.json(order)
  } catch (error: unknown) {
    console.error('Error fetching order:', error)
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Error fetching order' },
      { status: 500 }
    )
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Check authentication
    const userId = await authService.getUserId()
    if (!userId) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    const { id } = await params
    const body = await request.json()

    const order = await appwriteOrderService.updateOrder(id, body, userId)

    return NextResponse.json(order)
  } catch (error: unknown) {
    console.error('Error updating order:', error)

    const errorMessage = error instanceof Error ? error.message : ''

    if (errorMessage.includes('not found')) {
      return NextResponse.json(
        { error: 'Order not found' },
        { status: 404 }
      )
    }

    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Error updating order' },
      { status: 500 }
    )
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Check authentication
    const userId = await authService.getUserId()
    if (!userId) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    const { id } = await params

    await appwriteOrderService.deleteOrder(id, userId)

    return NextResponse.json({ success: true })
  } catch (error: unknown) {
    console.error('Error deleting order:', error)

    const errorMessage = error instanceof Error ? error.message : ''

    if (errorMessage.includes('not found')) {
      return NextResponse.json(
        { error: 'Order not found' },
        { status: 404 }
      )
    }

    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Error deleting order' },
      { status: 500 }
    )
  }
}

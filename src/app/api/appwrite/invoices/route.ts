import { NextRequest, NextResponse } from 'next/server'
import { appwriteInvoiceService } from '@/lib/services/appwrite-invoice-service'
import { authService } from '@/lib/auth'

export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const userId = await authService.getUserId()
    if (!userId) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const search = searchParams.get('search')
    const customerId = searchParams.get('customerId')
    const status = searchParams.get('status')

    let invoices

    if (search) {
      invoices = await appwriteInvoiceService.searchInvoices(search, userId)
    } else if (customerId) {
      invoices = await appwriteInvoiceService.getInvoicesByCustomer(customerId, userId)
    } else if (status) {
      invoices = await appwriteInvoiceService.getInvoicesByStatus(status, userId)
    } else {
      invoices = await appwriteInvoiceService.getInvoices(userId)
    }

    return NextResponse.json(invoices)
  } catch (error: unknown) {
    console.error('Error fetching invoices:', error)
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Error fetching invoices' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const userId = await authService.getUserId()
    if (!userId) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const { invoiceNumber, customerId } = body

    if (!invoiceNumber || !customerId) {
      return NextResponse.json(
        { error: 'Invoice number and customer ID are required' },
        { status: 400 }
      )
    }

    const invoice = await appwriteInvoiceService.createInvoice(body, userId)

    return NextResponse.json(invoice, { status: 201 })
  } catch (error: unknown) {
    console.error('Error creating invoice:', error)

    const errorMessage = error instanceof Error ? error.message : ''

    if (errorMessage.includes('already exists')) {
      return NextResponse.json(
        { error: 'Invoice number already exists' },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Error creating invoice' },
      { status: 500 }
    )
  }
}

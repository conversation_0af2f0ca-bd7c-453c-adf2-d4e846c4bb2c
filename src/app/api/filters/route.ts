import { NextRequest, NextResponse } from 'next/server'
import { FilterConfig, SortConfig, FilterValidationError } from '@/lib/filter-types'

// In-memory storage for filter presets (in a real app, this would be in a database)
interface FilterPreset {
  id: string
  name: string
  description?: string
  filters: FilterConfig
  sort: SortConfig
  createdAt: string
  updatedAt: string
}

// Mock storage - in production, this would be stored in the database
const filterPresets: FilterPreset[] = [
  {
    id: '1',
    name: 'High Value Items',
    description: 'Items with store price above ₱10,000',
    filters: {
      storePrice: {
        operator: 'gte',
        value: 10000
      }
    },
    sort: {
      columns: [
        {
          field: 'storePrice',
          direction: 'desc',
          priority: 0
        }
      ]
    },
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  },
  {
    id: '2',
    name: 'Unbought Items',
    description: 'Items that still need to be purchased',
    filters: {
      isBought: false
    },
    sort: {
      columns: [
        {
          field: 'createdAt',
          direction: 'asc',
          priority: 0
        }
      ]
    },
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  },
  {
    id: '3',
    name: 'Ready to Pack',
    description: 'Bought items that need packing',
    filters: {
      isBought: true,
      packingStatus: ['Not Packed']
    },
    sort: {
      columns: [
        {
          field: 'customer.name',
          direction: 'asc',
          priority: 0
        },
        {
          field: 'createdAt',
          direction: 'asc',
          priority: 1
        }
      ]
    },
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  }
]

/**
 * GET /api/filters - Get all filter presets
 */
export async function GET() {
  try {
    return NextResponse.json({
      presets: filterPresets,
      total: filterPresets.length
    })
  } catch (error) {
    console.error('Error fetching filter presets:', error)
    return NextResponse.json(
      { error: 'Error fetching filter presets' },
      { status: 500 }
    )
  }
}

/**
 * POST /api/filters - Create a new filter preset
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { name, description, filters, sort } = body

    // Validate required fields
    if (!name || typeof name !== 'string' || name.trim().length === 0) {
      throw new FilterValidationError('Preset name is required')
    }

    if (!filters || typeof filters !== 'object') {
      throw new FilterValidationError('Filters configuration is required')
    }

    if (!sort || typeof sort !== 'object') {
      throw new FilterValidationError('Sort configuration is required')
    }

    // Check for duplicate names
    const existingPreset = filterPresets.find(
      preset => preset.name.toLowerCase() === name.trim().toLowerCase()
    )
    if (existingPreset) {
      throw new FilterValidationError('A preset with this name already exists')
    }

    // Create new preset
    const newPreset: FilterPreset = {
      id: Date.now().toString(), // In production, use a proper UUID
      name: name.trim(),
      description: description?.trim() || undefined,
      filters,
      sort,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }

    filterPresets.push(newPreset)

    return NextResponse.json(newPreset, { status: 201 })

  } catch (error) {
    console.error('Error creating filter preset:', error)

    if (error instanceof FilterValidationError) {
      return NextResponse.json(
        {
          error: 'Invalid preset data',
          details: error.message
        },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { error: 'Error creating filter preset' },
      { status: 500 }
    )
  }
}

/**
 * PUT /api/filters - Update an existing filter preset
 */
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json()
    const { id, name, description, filters, sort } = body

    if (!id) {
      throw new FilterValidationError('Preset ID is required')
    }

    const presetIndex = filterPresets.findIndex(preset => preset.id === id)
    if (presetIndex === -1) {
      return NextResponse.json(
        { error: 'Filter preset not found' },
        { status: 404 }
      )
    }

    // Validate fields if provided
    if (name !== undefined) {
      if (typeof name !== 'string' || name.trim().length === 0) {
        throw new FilterValidationError('Preset name must be a non-empty string')
      }

      // Check for duplicate names (excluding current preset)
      const existingPreset = filterPresets.find(
        preset => preset.id !== id && preset.name.toLowerCase() === name.trim().toLowerCase()
      )
      if (existingPreset) {
        throw new FilterValidationError('A preset with this name already exists')
      }
    }

    // Update preset
    const updatedPreset: FilterPreset = {
      ...filterPresets[presetIndex],
      ...(name !== undefined && { name: name.trim() }),
      ...(description !== undefined && { description: description?.trim() || undefined }),
      ...(filters !== undefined && { filters }),
      ...(sort !== undefined && { sort }),
      updatedAt: new Date().toISOString()
    }

    filterPresets[presetIndex] = updatedPreset

    return NextResponse.json(updatedPreset)

  } catch (error) {
    console.error('Error updating filter preset:', error)

    if (error instanceof FilterValidationError) {
      return NextResponse.json(
        {
          error: 'Invalid preset data',
          details: error.message
        },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { error: 'Error updating filter preset' },
      { status: 500 }
    )
  }
}

/**
 * DELETE /api/filters - Delete a filter preset
 */
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const id = searchParams.get('id')

    if (!id) {
      throw new FilterValidationError('Preset ID is required')
    }

    const presetIndex = filterPresets.findIndex(preset => preset.id === id)
    if (presetIndex === -1) {
      return NextResponse.json(
        { error: 'Filter preset not found' },
        { status: 404 }
      )
    }

    const deletedPreset = filterPresets.splice(presetIndex, 1)[0]

    return NextResponse.json({
      message: 'Filter preset deleted successfully',
      deletedPreset
    })

  } catch (error) {
    console.error('Error deleting filter preset:', error)

    if (error instanceof FilterValidationError) {
      return NextResponse.json(
        {
          error: 'Invalid request',
          details: error.message
        },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { error: 'Error deleting filter preset' },
      { status: 500 }
    )
  }
}

'use client'

import { useEffect, useState } from 'react'
import { useRouter, useParams } from 'next/navigation'
import { StoreCodeForm } from '@/components/forms/store-code-form'
import { useAppStore } from '@/lib/store'
import { Card } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { LuArrowLeft, LuTrash2 } from 'react-icons/lu'
import Link from 'next/link'

// Enhanced store data type
interface EnhancedStoreData {
  id: number
  code: string
  name?: string | null
  storeType?: string
  status?: string
  parentStoreId?: number
  storeGroup?: string
  region?: string
  district?: string
  address?: string
  city?: string
  state?: string
  postalCode?: string
  country?: string
  phone?: string
  email?: string
  website?: string
  managerName?: string
  managerPhone?: string
  managerEmail?: string
  contactPerson?: string
  operatingHours?: string
  timezone?: string
  isOpen?: boolean
  allowsPickup?: boolean
  allowsDelivery?: boolean
  deliveryRadius?: number
  notes?: string
  internalNotes?: string
  specialInstructions?: string
  _count?: {
    items?: number
    orders?: number
  }
}

export default function EditStoreCodePage() {
  const router = useRouter()
  const params = useParams()
  const { updateStoreCode, removeStoreCode } = useAppStore()
  const [storeCode, setStoreCode] = useState<EnhancedStoreData | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [isDeleting, setIsDeleting] = useState(false)
  const [isFetching, setIsFetching] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const storeCodeId = params.id as string

  useEffect(() => {
    async function fetchStoreCode() {
      try {
        setIsFetching(true)
        setError(null)

        const response = await fetch(`/api/store-codes/${storeCodeId}`)
        if (!response.ok) {
          if (response.status === 404) {
            throw new Error('Store code not found')
          }
          throw new Error('Failed to fetch store code')
        }

        const data = await response.json()
        setStoreCode(data)
      } catch (err) {
        setError(err instanceof Error ? err.message : 'An error occurred')
      } finally {
        setIsFetching(false)
      }
    }

    if (storeCodeId) {
      fetchStoreCode()
    }
  }, [storeCodeId])

  const handleSubmit = async (data: Partial<EnhancedStoreData>) => {
    try {
      setIsLoading(true)

      // Use the enhanced store service for updates
      const response = await fetch('/api/enhanced/stores/bulk', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          operation: 'UPDATE',
          data: {
            updates: [{
              storeId: parseInt(storeCodeId),
              data: data
            }],
            validateOnly: false,
            continueOnError: false
          }
        }),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to update store')
      }

      const result = await response.json()

      if (result.failedItems > 0) {
        const errorMessage = result.errors.length > 0
          ? result.errors[0].errorMessage
          : 'Failed to update store'
        throw new Error(errorMessage)
      }

      // For backward compatibility, also update the simple store codes list
      const updatedStoreCode = {
        id: parseInt(storeCodeId),
        code: data.code || storeCode?.code || '',
        name: data.name || null,
        _count: { orders: storeCode?._count?.orders || 0 }
      }
      updateStoreCode(updatedStoreCode)

      router.push('/stores')
    } catch (error) {
      console.error('Error updating store:', error)
      alert(error instanceof Error ? error.message : 'Failed to update store')
    } finally {
      setIsLoading(false)
    }
  }

  const handleDelete = async () => {
    if (!storeCode) return

    const confirmDelete = confirm(
      `Are you sure you want to delete the store code "${storeCode.code}"? This action cannot be undone.`
    )

    if (!confirmDelete) return

    try {
      setIsDeleting(true)

      const response = await fetch(`/api/store-codes/${storeCodeId}`, {
        method: 'DELETE',
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to delete store code')
      }

      removeStoreCode(parseInt(storeCodeId))
      router.push('/stores')
    } catch (error) {
      console.error('Error deleting store code:', error)
      alert(error instanceof Error ? error.message : 'Failed to delete store code')
    } finally {
      setIsDeleting(false)
    }
  }

  const handleCancel = () => {
    router.back()
  }

  if (isFetching) {
    return (
      <div className="space-y-6 py-4">
        <div className="flex items-center gap-4">
          <Button variant="ghost" size="icon" asChild>
            <Link href="/stores">
              <LuArrowLeft className="h-4 w-4" />
            </Link>
          </Button>
          <h1 className="text-2xl font-semibold tracking-tight">Edit Store Code</h1>
        </div>
        <Card className="p-6">
          <div className="text-center">Loading...</div>
        </Card>
      </div>
    )
  }

  if (error) {
    return (
      <div className="space-y-6 py-4">
        <div className="flex items-center gap-4">
          <Button variant="ghost" size="icon" asChild>
            <Link href="/stores">
              <LuArrowLeft className="h-4 w-4" />
            </Link>
          </Button>
          <h1 className="text-2xl font-semibold tracking-tight">Edit Store Code</h1>
        </div>
        <Card className="p-6">
          <div className="text-center text-red-600">{error}</div>
        </Card>
      </div>
    )
  }

  if (!storeCode) {
    return null
  }

  return (
    <div className="space-y-6 py-4">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="ghost" size="icon" asChild>
            <Link href="/stores">
              <LuArrowLeft className="h-4 w-4" />
            </Link>
          </Button>
          <div>
            <h1 className="text-2xl font-semibold tracking-tight">Edit Store</h1>
            <p className="text-muted-foreground">
              Update the details for store &quot;{storeCode.code}&quot;
            </p>
          </div>
        </div>

        {storeCode._count.items === 0 && (
          <Button
            variant="destructive"
            size="sm"
            onClick={handleDelete}
            disabled={isDeleting}
          >
            <LuTrash2 className="h-4 w-4 mr-2" />
            {isDeleting ? 'Deleting...' : 'Delete'}
          </Button>
        )}
      </div>

      <StoreCodeForm
        onSubmit={handleSubmit}
        onCancel={handleCancel}
        initialData={storeCode}
        isLoading={isLoading}
        enableRealTimeValidation={true}
      />

      {storeCode._count.items > 0 && (
        <Card className="p-4 bg-yellow-50 border-yellow-200">
          <p className="text-sm text-yellow-800">
            <strong>Note:</strong> This store code has {storeCode._count.items} associated item(s) and cannot be deleted.
            You can still edit its details.
          </p>
        </Card>
      )}
    </div>
  )
}

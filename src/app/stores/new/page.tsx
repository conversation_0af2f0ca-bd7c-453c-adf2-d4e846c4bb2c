'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { StoreCodeForm } from '@/components/forms/store-code-form'
import { useAppStore } from '@/lib/store'

// Enhanced store data type
interface EnhancedStoreData {
  code: string
  name?: string
  storeType?: string
  status?: string
  parentStoreId?: number
  storeGroup?: string
  region?: string
  district?: string
  address?: string
  city?: string
  state?: string
  postalCode?: string
  country?: string
  phone?: string
  email?: string
  website?: string
  managerName?: string
  managerPhone?: string
  managerEmail?: string
  contactPerson?: string
  operatingHours?: string
  timezone?: string
  isOpen?: boolean
  allowsPickup?: boolean
  allowsDelivery?: boolean
  deliveryRadius?: number
  notes?: string
  internalNotes?: string
  specialInstructions?: string
}

export default function NewStoreCodePage() {
  const router = useRouter()
  const { addStoreCode } = useAppStore()
  const [isLoading, setIsLoading] = useState(false)

  const handleSubmit = async (data: EnhancedStoreData) => {
    try {
      setIsLoading(true)

      // Use the enhanced store service for creation
      const response = await fetch('/api/enhanced/stores/bulk', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          operation: 'CREATE',
          data: {
            stores: [data],
            validateOnly: false,
            continueOnError: false
          }
        }),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to create store')
      }

      const result = await response.json()

      if (result.failedItems > 0) {
        const errorMessage = result.errors.length > 0
          ? result.errors[0].errorMessage
          : 'Failed to create store'
        throw new Error(errorMessage)
      }

      // For backward compatibility, also add to the simple store codes list
      const newStoreCode = {
        id: Date.now(), // Temporary ID
        code: data.code,
        name: data.name || null,
        _count: { orders: 0 }
      }
      addStoreCode(newStoreCode)

      router.push('/stores')
    } catch (error) {
      console.error('Error creating store:', error)
      alert(error instanceof Error ? error.message : 'Failed to create store')
    } finally {
      setIsLoading(false)
    }
  }

  const handleCancel = () => {
    router.back()
  }

  return (
    <div className="space-y-6 py-4">
      <div>
        <h1 className="text-2xl font-semibold tracking-tight">Add New Store</h1>
        <p className="text-muted-foreground">
          Create a new store with comprehensive details and configuration options.
        </p>
      </div>

      <StoreCodeForm
        onSubmit={handleSubmit}
        onCancel={handleCancel}
        isLoading={isLoading}
        enableRealTimeValidation={true}
      />
    </div>
  )
}

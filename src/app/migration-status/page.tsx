'use client'

import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { CheckCircle, XCircle, Clock, AlertTriangle, Database, Users, ShoppingCart, FileText, Settings } from 'lucide-react'

interface MigrationStep {
  id: string
  name: string
  description: string
  status: 'pending' | 'in-progress' | 'completed' | 'error'
  icon: React.ReactNode
  details?: string
}

export default function MigrationStatusPage() {
  const [steps, setSteps] = useState<MigrationStep[]>([
    {
      id: 'appwrite-setup',
      name: 'Appwrite Configuration',
      description: 'Set up Appwrite client and server configurations',
      status: 'completed',
      icon: <Settings className="h-5 w-5" />,
      details: 'Appwrite SDK installed and configured'
    },
    {
      id: 'database-schema',
      name: 'Database Schema Migration',
      description: 'Create Appwrite collections and attributes',
      status: 'pending',
      icon: <Database className="h-5 w-5" />,
      details: 'Collections need to be created in Appwrite'
    },
    {
      id: 'auth-system',
      name: 'Authentication System',
      description: 'Implement Appwrite authentication',
      status: 'completed',
      icon: <Users className="h-5 w-5" />,
      details: 'Auth pages and providers created'
    },
    {
      id: 'data-export',
      name: 'Data Export',
      description: 'Export existing Prisma data',
      status: 'completed',
      icon: <FileText className="h-5 w-5" />,
      details: 'Data exported to JSON files'
    },
    {
      id: 'api-migration',
      name: 'API Migration',
      description: 'Replace Prisma APIs with Appwrite services',
      status: 'completed',
      icon: <ShoppingCart className="h-5 w-5" />,
      details: 'All core APIs migrated (stores, customers, orders, invoices)'
    },
    {
      id: 'data-import',
      name: 'Data Import',
      description: 'Import data to Appwrite',
      status: 'pending',
      icon: <Database className="h-5 w-5" />,
      details: 'Waiting for database setup completion'
    },
    {
      id: 'frontend-update',
      name: 'Frontend Integration',
      description: 'Update frontend to use Appwrite APIs',
      status: 'completed',
      icon: <Settings className="h-5 w-5" />,
      details: 'Data fetching hooks created with API switching capability'
    },
    {
      id: 'testing',
      name: 'Testing & Validation',
      description: 'Test all functionality with Appwrite',
      status: 'pending',
      icon: <CheckCircle className="h-5 w-5" />,
      details: 'Comprehensive testing required'
    }
  ])

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="h-5 w-5 text-green-500" />
      case 'in-progress':
        return <Clock className="h-5 w-5 text-blue-500" />
      case 'error':
        return <XCircle className="h-5 w-5 text-red-500" />
      default:
        return <Clock className="h-5 w-5 text-gray-400" />
    }
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'completed':
        return <Badge variant="default" className="bg-green-100 text-green-800">Completed</Badge>
      case 'in-progress':
        return <Badge variant="default" className="bg-blue-100 text-blue-800">In Progress</Badge>
      case 'error':
        return <Badge variant="destructive">Error</Badge>
      default:
        return <Badge variant="secondary">Pending</Badge>
    }
  }

  const completedSteps = steps.filter(step => step.status === 'completed').length
  const totalSteps = steps.length
  const progressPercentage = (completedSteps / totalSteps) * 100

  const handleRunDatabaseSetup = async () => {
    setSteps(prev => prev.map(step => 
      step.id === 'database-schema' 
        ? { ...step, status: 'in-progress', details: 'Creating Appwrite collections...' }
        : step
    ))

    try {
      // This would trigger the database setup script
      console.log('Running database setup...')
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000))
      
      setSteps(prev => prev.map(step => 
        step.id === 'database-schema' 
          ? { ...step, status: 'completed', details: 'All collections created successfully' }
          : step
      ))
    } catch {
      setSteps(prev => prev.map(step =>
        step.id === 'database-schema'
          ? { ...step, status: 'error', details: 'Failed to create collections' }
          : step
      ))
    }
  }

  const handleRunDataImport = async () => {
    setSteps(prev => prev.map(step => 
      step.id === 'data-import' 
        ? { ...step, status: 'in-progress', details: 'Importing data to Appwrite...' }
        : step
    ))

    try {
      // This would trigger the data import script
      console.log('Running data import...')
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 3000))
      
      setSteps(prev => prev.map(step => 
        step.id === 'data-import' 
          ? { ...step, status: 'completed', details: 'All data imported successfully' }
          : step
      ))
    } catch {
      setSteps(prev => prev.map(step =>
        step.id === 'data-import'
          ? { ...step, status: 'error', details: 'Failed to import data' }
          : step
      ))
    }
  }

  return (
    <div className="container mx-auto py-8 space-y-6">
      <div className="text-center space-y-2">
        <h1 className="text-3xl font-bold">Appwrite Migration Status</h1>
        <p className="text-gray-600">
          Tracking the progress of migrating PasaBuy Pal from Prisma/SQLite to Appwrite
        </p>
      </div>

      {/* Progress Overview */}
      <Card>
        <CardHeader>
          <CardTitle>Migration Progress</CardTitle>
          <CardDescription>
            {completedSteps} of {totalSteps} steps completed
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="w-full bg-gray-200 rounded-full h-2.5">
            <div 
              className="bg-blue-600 h-2.5 rounded-full transition-all duration-300" 
              style={{ width: `${progressPercentage}%` }}
            ></div>
          </div>
          <p className="text-sm text-gray-600 mt-2">
            {progressPercentage.toFixed(1)}% complete
          </p>
        </CardContent>
      </Card>

      {/* Migration Steps */}
      <div className="space-y-4">
        {steps.map((step, index) => (
          <Card key={step.id}>
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className="flex items-center space-x-2">
                    {getStatusIcon(step.status)}
                    {step.icon}
                  </div>
                  <div>
                    <CardTitle className="text-lg">{step.name}</CardTitle>
                    <CardDescription>{step.description}</CardDescription>
                  </div>
                </div>
                {getStatusBadge(step.status)}
              </div>
            </CardHeader>
            {step.details && (
              <CardContent className="pt-0">
                <p className="text-sm text-gray-600">{step.details}</p>
                
                {/* Action buttons for specific steps */}
                {step.id === 'database-schema' && step.status === 'pending' && (
                  <Button 
                    onClick={handleRunDatabaseSetup}
                    className="mt-3"
                    size="sm"
                  >
                    Run Database Setup
                  </Button>
                )}
                
                {step.id === 'data-import' && step.status === 'pending' && (
                  <Button
                    onClick={handleRunDataImport}
                    className="mt-3"
                    size="sm"
                    disabled={steps.find(s => s.id === 'database-schema')?.status !== 'completed'}
                  >
                    Import Data
                  </Button>
                )}

                {step.id === 'testing' && step.status === 'pending' && (
                  <Button
                    onClick={() => {
                      console.log('Running complete migration...')
                      // This would run the complete migration script
                    }}
                    className="mt-3"
                    size="sm"
                  >
                    Complete Migration
                  </Button>
                )}
              </CardContent>
            )}
          </Card>
        ))}
      </div>

      {/* Important Notes */}
      <Alert>
        <AlertTriangle className="h-4 w-4" />
        <AlertDescription>
          <strong>Important:</strong> This migration will replace the current SQLite/Prisma setup with Appwrite. 
          Make sure you have backed up your data before proceeding. The migration is currently in progress and 
          some features may be temporarily unavailable.
        </AlertDescription>
      </Alert>

      {/* Next Steps */}
      <Card>
        <CardHeader>
          <CardTitle>Next Steps</CardTitle>
        </CardHeader>
        <CardContent className="space-y-2">
          <p className="text-sm">1. Complete database schema setup in Appwrite</p>
          <p className="text-sm">2. Import existing data to Appwrite</p>
          <p className="text-sm">3. Migrate remaining API endpoints</p>
          <p className="text-sm">4. Update frontend components</p>
          <p className="text-sm">5. Test all functionality</p>
          <p className="text-sm">6. Deploy with Appwrite backend</p>
        </CardContent>
      </Card>
    </div>
  )
}

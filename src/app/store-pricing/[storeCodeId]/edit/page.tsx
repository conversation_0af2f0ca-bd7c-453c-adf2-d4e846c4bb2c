'use client'

import { useEffect, useState } from 'react'
import { useRouter, useParams } from 'next/navigation'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { useScrollToTop } from '@/hooks/use-scroll'
import { LuArrowLeft, LuSave, LuLoader, LuPlus, LuTrash2 } from 'react-icons/lu'

interface PricingTier {
  id?: number
  minPrice: number
  maxPrice: number | null
  markupType: 'PERCENTAGE' | 'FIXED_AMOUNT'
  markupValue: number
  pasabuyFee: number
  sortOrder: number
}

interface StorePricing {
  id: number
  storeCodeId: number
  name: string
  serviceFee: number
  isActive: boolean
  storeCode: {
    id: number
    code: string
    name: string | null
  }
  pricingTiers: PricingTier[]
}

export default function EditStorePricingPage() {
  const router = useRouter()
  const params = useParams()
  const storeCodeId = parseInt(params.storeCodeId as string)

  const [storePricing, setStorePricing] = useState<StorePricing | null>(null)
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // Form state
  const [name, setName] = useState('')
  const [serviceFee, setServiceFee] = useState('')
  const [pricingTiers, setPricingTiers] = useState<PricingTier[]>([])
  const [validationErrors, setValidationErrors] = useState<string[]>([])

  useScrollToTop()

  // Hide bottom navigation for focused experience
  useEffect(() => {
    document.body.style.paddingBottom = '0'
    return () => {
      document.body.style.paddingBottom = '4rem'
    }
  }, [])

  useEffect(() => {
    if (storeCodeId) {
      fetchStorePricing()
    }
  }, [storeCodeId])

  const fetchStorePricing = async () => {
    try {
      setLoading(true)
      setError(null)
      
      const response = await fetch(`/api/store-pricing/${storeCodeId}`)
      
      if (!response.ok) {
        if (response.status === 404) {
          setError('Store pricing configuration not found')
        } else {
          throw new Error('Failed to fetch store pricing')
        }
        return
      }
      
      const data = await response.json()
      setStorePricing(data)
      
      // Populate form
      setName(data.name)
      setServiceFee(data.serviceFee.toString())
      setPricingTiers(data.pricingTiers || [])
    } catch (error) {
      console.error('Error fetching store pricing:', error)
      setError('Failed to load store pricing configuration')
    } finally {
      setLoading(false)
    }
  }

  const validatePricingTiers = (tiers: PricingTier[]): string[] => {
    const errors: string[] = []
    
    if (tiers.length === 0) {
      errors.push('At least one pricing tier is required')
      return errors
    }

    // Sort tiers by minPrice for validation
    const sortedTiers = [...tiers].sort((a, b) => a.minPrice - b.minPrice)
    
    // Check for gaps and overlaps
    for (let i = 0; i < sortedTiers.length; i++) {
      const currentTier = sortedTiers[i]
      const nextTier = sortedTiers[i + 1]
      
      // Validate current tier
      if (currentTier.minPrice < 0) {
        errors.push(`Tier ${i + 1}: Minimum price cannot be negative`)
      }
      
      if (currentTier.maxPrice !== null && currentTier.maxPrice <= currentTier.minPrice) {
        errors.push(`Tier ${i + 1}: Maximum price must be greater than minimum price`)
      }
      
      if (currentTier.markupValue < 0) {
        errors.push(`Tier ${i + 1}: Markup value cannot be negative`)
      }
      
      // Check for gaps between tiers
      if (nextTier && currentTier.maxPrice !== null) {
        if (currentTier.maxPrice + 0.01 < nextTier.minPrice) {
          errors.push(`Gap between tier ${i + 1} (max: ₱${currentTier.maxPrice}) and tier ${i + 2} (min: ₱${nextTier.minPrice})`)
        }
        
        // Check for overlaps
        if (currentTier.maxPrice >= nextTier.minPrice) {
          errors.push(`Overlap between tier ${i + 1} and tier ${i + 2}`)
        }
      }
    }
    
    // Ensure the last tier covers "and above" or there's no gap at the end
    const lastTier = sortedTiers[sortedTiers.length - 1]
    if (lastTier.maxPrice !== null) {
      errors.push('The highest tier must have no maximum price (to cover all prices above)')
    }
    
    // Ensure the first tier starts at 0
    if (sortedTiers[0].minPrice > 0) {
      errors.push('The first tier must start at ₱0 to cover all possible prices')
    }

    return errors
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!name.trim()) {
      setError('Pricing configuration name is required')
      return
    }

    const serviceFeeVal = parseFloat(serviceFee)

    if (isNaN(serviceFeeVal) || serviceFeeVal < 0) {
      setError('Service fee must be a valid non-negative number')
      return
    }

    if (pricingTiers.length === 0) {
      setError('At least one pricing tier is required')
      return
    }

    // Validate pricing tiers
    const errors = validatePricingTiers(pricingTiers)
    if (errors.length > 0) {
      setValidationErrors(errors)
      setError('Please fix the pricing tier validation errors')
      return
    }

    try {
      setSaving(true)
      setError(null)
      setValidationErrors([])

      const response = await fetch(`/api/store-pricing/${storeCodeId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: name.trim(),
          serviceFee: serviceFeeVal,
          pricingTiers: pricingTiers
        })
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to update store pricing')
      }

      router.push('/store-pricing')
    } catch (error) {
      console.error('Error updating store pricing:', error)
      setError(error instanceof Error ? error.message : 'Failed to update store pricing')
    } finally {
      setSaving(false)
    }
  }

  const handleCancel = () => {
    router.push('/store-pricing')
  }

  // Tier management functions
  const addTier = () => {
    const newTier: PricingTier = {
      minPrice: 0,
      maxPrice: null,
      markupType: 'FIXED_AMOUNT',
      markupValue: 0,
      pasabuyFee: 0,
      sortOrder: pricingTiers.length
    }
    setPricingTiers([...pricingTiers, newTier])
  }

  const updateTier = (index: number, updates: Partial<PricingTier>) => {
    const updatedTiers = [...pricingTiers]
    updatedTiers[index] = { ...updatedTiers[index], ...updates }
    setPricingTiers(updatedTiers)
    setValidationErrors([]) // Clear validation errors when user makes changes
  }

  const removeTier = (index: number) => {
    const updatedTiers = pricingTiers.filter((_, i) => i !== index)
    // Update sort orders
    updatedTiers.forEach((tier, i) => {
      tier.sortOrder = i
    })
    setPricingTiers(updatedTiers)
    setValidationErrors([])
  }

  const formatMarkupDisplay = (markupType: string, markupValue: number) => {
    switch (markupType) {
      case 'PERCENTAGE':
        return `${markupValue}%`
      case 'FIXED_AMOUNT':
        return `₱${markupValue.toFixed(2)}`
      default:
        return markupValue.toString()
    }
  }

  const formatPriceRange = (tier: PricingTier) => {
    if (tier.maxPrice === null) {
      return `₱${tier.minPrice}+`
    }
    return `₱${tier.minPrice} - ₱${tier.maxPrice}`
  }

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-6">
        <div className="flex items-center justify-center py-8">
          <LuLoader className="h-6 w-6 animate-spin mr-2" />
          <span>Loading store pricing configuration...</span>
        </div>
      </div>
    )
  }

  if (error && !storePricing) {
    return (
      <div className="container mx-auto px-4 py-6">
        <Card className="border-destructive">
          <CardContent className="p-6 text-center">
            <div className="text-destructive text-lg font-medium mb-2">Error</div>
            <div className="text-muted-foreground mb-4">{error}</div>
            <Button onClick={() => router.push('/store-pricing')}>
              <LuArrowLeft className="h-4 w-4 mr-2" />
              Back to Store Pricing
            </Button>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="container mx-auto px-4 py-6">
      {/* Header */}
      <div className="flex items-center gap-4 mb-6">
        <Button
          variant="ghost"
          size="icon"
          onClick={handleCancel}
          className="h-10 w-10"
        >
          <LuArrowLeft className="h-4 w-4" />
        </Button>
        <div>
          <h1 className="text-2xl font-bold">Edit Store Pricing</h1>
          {storePricing && (
            <p className="text-muted-foreground">
              {storePricing.storeCode.code} {storePricing.storeCode.name && `(${storePricing.storeCode.name})`}
            </p>
          )}
        </div>
      </div>

      <form onSubmit={handleSubmit} className="grid gap-6 max-w-4xl">
        {/* Basic Configuration */}
        <Card>
          <CardHeader>
            <CardTitle>Basic Configuration</CardTitle>
            <CardDescription>
              Configure store name and Pasabuy fee
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Configuration Name */}
            <div className="space-y-2">
              <Label htmlFor="name">Configuration Name</Label>
              <Input
                id="name"
                value={name}
                onChange={(e) => setName(e.target.value)}
                placeholder="e.g., Premium Store Pricing"
                className="min-h-[44px]"
                required
              />
            </div>

            {/* Pasabuy Fee */}
            <div className="space-y-2">
              <Label htmlFor="serviceFee">Pasabuy Fee (₱)</Label>
              <Input
                id="serviceFee"
                type="number"
                step="0.01"
                min="0"
                value={serviceFee}
                onChange={(e) => setServiceFee(e.target.value)}
                placeholder="20.00"
                className="min-h-[44px]"
                required
              />
            </div>
          </CardContent>
        </Card>

        {/* Pricing Tiers */}
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle>Pricing Tiers</CardTitle>
                <CardDescription>
                  Configure different markup amounts based on store price ranges
                </CardDescription>
              </div>
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={addTier}
                className="h-9"
              >
                <LuPlus className="h-4 w-4 mr-2" />
                Add Tier
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            {pricingTiers.length === 0 ? (
              <div className="text-center py-8 text-muted-foreground">
                <p className="mb-4">No pricing tiers configured</p>
                <Button type="button" variant="outline" onClick={addTier}>
                  <LuPlus className="h-4 w-4 mr-2" />
                  Add First Tier
                </Button>
              </div>
            ) : (
              <div className="space-y-4">
                {pricingTiers
                  .sort((a, b) => a.sortOrder - b.sortOrder)
                  .map((tier, index) => (
                    <div key={index} className="p-4 border rounded-lg space-y-4">
                      <div className="flex items-center justify-between">
                        <Badge variant="outline">
                          Tier {index + 1}: {formatPriceRange(tier)}
                        </Badge>
                        {pricingTiers.length > 1 && (
                          <Button
                            type="button"
                            variant="ghost"
                            size="sm"
                            onClick={() => removeTier(index)}
                            className="h-8 w-8 p-0 text-destructive hover:text-destructive"
                          >
                            <LuTrash2 className="h-4 w-4" />
                          </Button>
                        )}
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
                        {/* Min Price */}
                        <div className="space-y-2">
                          <Label>Min Price (₱)</Label>
                          <Input
                            type="number"
                            step="0.01"
                            min="0"
                            value={tier.minPrice}
                            onChange={(e) => updateTier(index, { minPrice: parseFloat(e.target.value) || 0 })}
                            className="min-h-[44px]"
                          />
                        </div>

                        {/* Max Price */}
                        <div className="space-y-2">
                          <Label>Max Price (₱)</Label>
                          <Input
                            type="number"
                            step="0.01"
                            min="0"
                            value={tier.maxPrice || ''}
                            onChange={(e) => updateTier(index, { 
                              maxPrice: e.target.value ? parseFloat(e.target.value) : null 
                            })}
                            placeholder="Leave empty for 'and above'"
                            className="min-h-[44px]"
                          />
                        </div>

                        {/* Markup Type */}
                        <div className="space-y-2">
                          <Label>Markup Type</Label>
                          <Select 
                            value={tier.markupType} 
                            onValueChange={(value: 'PERCENTAGE' | 'FIXED_AMOUNT') => 
                              updateTier(index, { markupType: value })
                            }
                          >
                            <SelectTrigger className="min-h-[44px]">
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="PERCENTAGE">Percentage (%)</SelectItem>
                              <SelectItem value="FIXED_AMOUNT">Fixed Amount (₱)</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>

                        {/* Markup Value */}
                        <div className="space-y-2">
                          <Label>
                            Markup Value {tier.markupType === 'PERCENTAGE' ? '(%)' : '(₱)'}
                          </Label>
                          <Input
                            type="number"
                            step="0.01"
                            min="0"
                            value={tier.markupValue}
                            onChange={(e) => updateTier(index, { markupValue: parseFloat(e.target.value) || 0 })}
                            className="min-h-[44px]"
                          />
                        </div>

                        {/* Pasabuy Fee */}
                        <div className="space-y-2">
                          <Label>Pasabuy Fee (₱)</Label>
                          <Input
                            type="number"
                            step="0.01"
                            min="0"
                            value={tier.pasabuyFee}
                            onChange={(e) => updateTier(index, { pasabuyFee: parseFloat(e.target.value) || 0 })}
                            className="min-h-[44px]"
                          />
                        </div>
                      </div>

                      {/* Tier Preview */}
                      <div className="text-sm text-muted-foreground">
                        <strong>Preview:</strong> {formatPriceRange(tier)} → {formatMarkupDisplay(tier.markupType, tier.markupValue)} markup - ₱{tier.pasabuyFee.toFixed(2)} Pasabuy fee
                      </div>
                    </div>
                  ))}
              </div>
            )}
          </CardContent>
        </Card>

        {/* Validation Errors */}
        {validationErrors.length > 0 && (
          <Card className="border-destructive">
            <CardHeader>
              <CardTitle className="text-destructive">Validation Errors</CardTitle>
            </CardHeader>
            <CardContent>
              <ul className="list-disc list-inside space-y-1 text-sm text-destructive">
                {validationErrors.map((error, index) => (
                  <li key={index}>{error}</li>
                ))}
              </ul>
            </CardContent>
          </Card>
        )}

        {/* Error Display */}
        {error && (
          <Card className="border-destructive">
            <CardContent className="p-4">
              <div className="text-destructive text-sm">{error}</div>
            </CardContent>
          </Card>
        )}

        {/* Action Buttons */}
        <div className="flex gap-3">
          <Button
            type="submit"
            disabled={saving}
            className="min-h-[44px] flex-1"
          >
            {saving ? (
              <>
                <LuLoader className="h-4 w-4 mr-2 animate-spin" />
                Saving...
              </>
            ) : (
              <>
                <LuSave className="h-4 w-4 mr-2" />
                Save Changes
              </>
            )}
          </Button>
          <Button
            type="button"
            variant="outline"
            onClick={handleCancel}
            disabled={saving}
            className="min-h-[44px]"
          >
            Cancel
          </Button>
        </div>
      </form>
    </div>
  )
}

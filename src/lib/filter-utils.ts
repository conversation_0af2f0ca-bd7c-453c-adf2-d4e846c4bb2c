import { NextRequest } from 'next/server'
import {
  FilterConfig,
  SortConfig,
  PaginationConfig,
  TextFilter,
  NumberFilter,
  DateFilter,
  EntityFilter,
  SortColumn,
  FilterValidationError,
  VALID_TEXT_OPERATORS,
  VALID_NUMBER_OPERATORS,
  VALID_DATE_OPERATORS,
  VALID_SORT_DIRECTIONS,
  VALID_ORDER_SORT_FIELDS,
  TextOperator,
  NumberOperator,
  DateOperator,
  SortDirection,
  ValidOrderSortField
} from './filter-types'

/**
 * Parse filter parameters from URL search params
 */
export function parseFiltersFromRequest(request: NextRequest): {
  filters: FilterConfig
  sort: SortConfig
  pagination: PaginationConfig
} {
  const { searchParams } = new URL(request.url)

  return {
    filters: parseFilterConfig(searchParams),
    sort: parseSortConfig(searchParams),
    pagination: parsePaginationConfig(searchParams)
  }
}

/**
 * Parse filter configuration from search parameters
 */
function parseFilterConfig(searchParams: URLSearchParams): FilterConfig {
  const filters: FilterConfig = {}

  // Text filters
  const productNameFilter = parseTextFilter(searchParams, 'productName')
  if (productNameFilter) filters.productName = productNameFilter

  // Number filters
  const quantityFilter = parseNumberFilter(searchParams, 'quantity')
  if (quantityFilter) filters.quantity = quantityFilter

  const storePriceFilter = parseNumberFilter(searchParams, 'storePrice')
  if (storePriceFilter) filters.storePrice = storePriceFilter

  const pasabuyFeeFilter = parseNumberFilter(searchParams, 'pasabuyFee')
  if (pasabuyFeeFilter) filters.pasabuyFee = pasabuyFeeFilter

  const customerPriceFilter = parseNumberFilter(searchParams, 'customerPrice')
  if (customerPriceFilter) filters.customerPrice = customerPriceFilter

  // Date filters
  const createdAtFilter = parseDateFilter(searchParams, 'createdAt')
  if (createdAtFilter) filters.createdAt = createdAtFilter

  const updatedAtFilter = parseDateFilter(searchParams, 'updatedAt')
  if (updatedAtFilter) filters.updatedAt = updatedAtFilter

  // Entity filters
  const customersFilter = parseEntityFilter(searchParams, 'customers')
  if (customersFilter) filters.customers = customersFilter

  const storeCodesFilter = parseEntityFilter(searchParams, 'storeCodes')
  if (storeCodesFilter) filters.storeCodes = storeCodesFilter

  // Status filters
  const isBought = searchParams.get('isBought')
  if (isBought !== null) {
    if (isBought === 'true') filters.isBought = true
    else if (isBought === 'false') filters.isBought = false
    else if (isBought === 'null') filters.isBought = null
  }

  const packingStatus = searchParams.get('packingStatus')
  if (packingStatus) {
    filters.packingStatus = packingStatus.split(',').map(s => s.trim()).filter(s => s.length > 0)
  }

  // Legacy search support
  const search = searchParams.get('search')
  if (search) filters.search = search

  return filters
}

/**
 * Parse text filter from search parameters
 */
function parseTextFilter(searchParams: URLSearchParams, field: string): TextFilter | null {
  const value = searchParams.get(`${field}.value`)
  const operator = searchParams.get(`${field}.operator`) as TextOperator
  const caseSensitive = searchParams.get(`${field}.caseSensitive`) === 'true'

  if (!value) return null

  if (operator && !VALID_TEXT_OPERATORS.includes(operator)) {
    throw new FilterValidationError(`Invalid text operator for ${field}: ${operator}`, field)
  }

  return {
    value,
    operator: operator || 'contains',
    caseSensitive
  }
}

/**
 * Parse number filter from search parameters
 */
function parseNumberFilter(searchParams: URLSearchParams, field: string): NumberFilter | null {
  const operator = searchParams.get(`${field}.operator`) as NumberOperator
  const value = searchParams.get(`${field}.value`)
  const min = searchParams.get(`${field}.min`)
  const max = searchParams.get(`${field}.max`)

  if (!operator && !value && !min && !max) return null

  if (operator && !VALID_NUMBER_OPERATORS.includes(operator)) {
    throw new FilterValidationError(`Invalid number operator for ${field}: ${operator}`, field)
  }

  const filter: NumberFilter = {
    operator: operator || 'equals'
  }

  if (value !== null) {
    const numValue = parseFloat(value)
    if (isNaN(numValue)) {
      throw new FilterValidationError(`Invalid number value for ${field}: ${value}`, field)
    }
    filter.value = numValue
  }

  if (min !== null) {
    const numMin = parseFloat(min)
    if (isNaN(numMin)) {
      throw new FilterValidationError(`Invalid min value for ${field}: ${min}`, field)
    }
    filter.min = numMin
  }

  if (max !== null) {
    const numMax = parseFloat(max)
    if (isNaN(numMax)) {
      throw new FilterValidationError(`Invalid max value for ${field}: ${max}`, field)
    }
    filter.max = numMax
  }

  return filter
}

/**
 * Parse date filter from search parameters
 */
function parseDateFilter(searchParams: URLSearchParams, field: string): DateFilter | null {
  const operator = searchParams.get(`${field}.operator`) as DateOperator
  const value = searchParams.get(`${field}.value`)
  const startDate = searchParams.get(`${field}.startDate`)
  const endDate = searchParams.get(`${field}.endDate`)

  if (!operator && !value && !startDate && !endDate) return null

  if (operator && !VALID_DATE_OPERATORS.includes(operator)) {
    throw new FilterValidationError(`Invalid date operator for ${field}: ${operator}`, field)
  }

  const filter: DateFilter = {
    operator: operator || 'equals'
  }

  if (value) {
    const date = new Date(value)
    if (isNaN(date.getTime())) {
      throw new FilterValidationError(`Invalid date value for ${field}: ${value}`, field)
    }
    filter.value = date
  }

  if (startDate) {
    const date = new Date(startDate)
    if (isNaN(date.getTime())) {
      throw new FilterValidationError(`Invalid start date for ${field}: ${startDate}`, field)
    }
    filter.startDate = date
  }

  if (endDate) {
    const date = new Date(endDate)
    if (isNaN(date.getTime())) {
      throw new FilterValidationError(`Invalid end date for ${field}: ${endDate}`, field)
    }
    filter.endDate = date
  }

  return filter
}

/**
 * Parse entity filter from search parameters
 */
function parseEntityFilter(searchParams: URLSearchParams, field: string): EntityFilter | null {
  const include = searchParams.get(`${field}.include`)
  const exclude = searchParams.get(`${field}.exclude`)
  const includeNull = searchParams.get(`${field}.includeNull`) === 'true'

  if (!include && !exclude && !includeNull) return null

  const filter: EntityFilter = {
    include: [],
    exclude: [],
    includeNull
  }

  if (include) {
    filter.include = include.split(',').map(id => {
      const numId = parseInt(id.trim())
      if (isNaN(numId)) {
        throw new FilterValidationError(`Invalid include ID for ${field}: ${id}`, field)
      }
      return numId
    })
  }

  if (exclude) {
    filter.exclude = exclude.split(',').map(id => {
      const numId = parseInt(id.trim())
      if (isNaN(numId)) {
        throw new FilterValidationError(`Invalid exclude ID for ${field}: ${id}`, field)
      }
      return numId
    })
  }

  return filter
}

/**
 * Parse sort configuration from search parameters
 */
function parseSortConfig(searchParams: URLSearchParams): SortConfig {
  const sortParam = searchParams.get('sort')
  const sortDirParam = searchParams.get('sortDir')

  const config: SortConfig = {
    columns: [],
    defaultSort: {
      field: 'createdAt',
      direction: 'desc',
      priority: 0
    }
  }

  // Handle indexed sort parameters (sort.0.field, sort.0.direction, sort.0.priority)
  const indexedSortColumns: SortColumn[] = []
  const sortIndices = new Set<number>()

  // Find all sort indices
  for (const [key] of searchParams.entries()) {
    const match = key.match(/^sort\.(\d+)\./)
    if (match) {
      sortIndices.add(parseInt(match[1]))
    }
  }

  // Parse each indexed sort column
  for (const index of Array.from(sortIndices).sort()) {
    const field = searchParams.get(`sort.${index}.field`)
    const direction = searchParams.get(`sort.${index}.direction`) as SortDirection
    const priority = searchParams.get(`sort.${index}.priority`)

    if (field && direction) {
      if (!VALID_SORT_DIRECTIONS.includes(direction)) {
        throw new FilterValidationError(`Invalid sort direction: ${direction}`)
      }

      if (!VALID_ORDER_SORT_FIELDS.includes(field as ValidOrderSortField)) {
        throw new FilterValidationError(`Invalid sort field: ${field}`)
      }

      indexedSortColumns.push({
        field,
        direction,
        priority: priority ? parseInt(priority) : index
      })
    }
  }

  if (indexedSortColumns.length > 0) {
    config.columns = indexedSortColumns.sort((a, b) => a.priority - b.priority)
    return config
  }

  // Handle legacy single-column sort
  if (sortParam) {
    const direction = (sortDirParam as SortDirection) || 'asc'

    if (!VALID_SORT_DIRECTIONS.includes(direction)) {
      throw new FilterValidationError(`Invalid sort direction: ${direction}`)
    }

    if (!VALID_ORDER_SORT_FIELDS.includes(sortParam as ValidOrderSortField)) {
      throw new FilterValidationError(`Invalid sort field: ${sortParam}`)
    }

    config.columns.push({
      field: sortParam,
      direction,
      priority: 0
    })
  }

  // Handle multi-column sort
  const multiSort = searchParams.get('multiSort')
  if (multiSort) {
    try {
      const sortColumns = JSON.parse(multiSort) as SortColumn[]

      for (const column of sortColumns) {
        if (!VALID_SORT_DIRECTIONS.includes(column.direction)) {
          throw new FilterValidationError(`Invalid sort direction: ${column.direction}`)
        }

        if (!VALID_ORDER_SORT_FIELDS.includes(column.field as ValidOrderSortField)) {
          throw new FilterValidationError(`Invalid sort field: ${column.field}`)
        }
      }

      config.columns = sortColumns
    } catch {
      throw new FilterValidationError('Invalid multiSort JSON format')
    }
  }

  return config
}

/**
 * Parse pagination configuration from search parameters
 */
function parsePaginationConfig(searchParams: URLSearchParams): PaginationConfig {
  const page = searchParams.get('page')
  const limit = searchParams.get('limit')
  const offset = searchParams.get('offset')

  const config: PaginationConfig = {}

  if (page) {
    const pageNum = parseInt(page)
    if (isNaN(pageNum) || pageNum < 1) {
      throw new FilterValidationError('Page must be a positive integer')
    }
    config.page = pageNum
  }

  if (limit) {
    const limitNum = parseInt(limit)
    if (isNaN(limitNum) || limitNum < 1 || limitNum > 1000) {
      throw new FilterValidationError('Limit must be between 1 and 1000')
    }
    config.limit = limitNum
  }

  if (offset) {
    const offsetNum = parseInt(offset)
    if (isNaN(offsetNum) || offsetNum < 0) {
      throw new FilterValidationError('Offset must be a non-negative integer')
    }
    config.offset = offsetNum
  }

  return config
}

/**
 * Build pagination parameters for Prisma query
 */
export function buildPaginationParams(pagination: PaginationConfig): {
  skip?: number
  take?: number
} {
  const params: { skip?: number; take?: number } = {}

  if (pagination.limit) {
    params.take = pagination.limit
  }

  if (pagination.offset !== undefined) {
    params.skip = pagination.offset
  } else if (pagination.page && pagination.limit) {
    params.skip = (pagination.page - 1) * pagination.limit
  }

  return params
}

/**
 * Calculate pagination metadata
 */
export function calculatePaginationMeta(
  total: number,
  pagination: PaginationConfig
): {
  page: number
  limit: number
  total: number
  totalPages: number
  hasNext: boolean
  hasPrev: boolean
} {
  const page = pagination.page || 1
  const limit = pagination.limit || total
  const totalPages = Math.ceil(total / limit)

  return {
    page,
    limit,
    total,
    totalPages,
    hasNext: page < totalPages,
    hasPrev: page > 1
  }
}

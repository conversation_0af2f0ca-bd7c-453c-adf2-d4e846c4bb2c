import { Prisma } from '@prisma/client'
import {
  FilterConfig,
  SortConfig,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>F<PERSON>er,
  DateF<PERSON>er,
  <PERSON><PERSON>tyFilter,
  SortColumn,
  FilterValidationError,
  QueryBuilderError,
  VALID_TEXT_OPERATORS,
  VALID_NUMBER_OPERATORS,
  VALID_DATE_OPERATORS,
  VALID_SORT_DIRECTIONS,
  VALID_ORDER_SORT_FIELDS,
  ValidOrderSortField
} from './filter-types'

/**
 * Query builder utility for constructing complex Prisma queries
 * with advanced filtering and sorting capabilities
 */
export class QueryBuilder {
  private whereClause: Prisma.OrderWhereInput = {}
  private orderByClause: Prisma.OrderOrderByWithRelationInput[] = []

  /**
   * Build a complete Prisma query from filter and sort configurations
   */
  static buildOrderQuery(
    filters?: FilterConfig,
    sort?: SortConfig,
    include?: Prisma.OrderInclude
  ): {
    where: Prisma.OrderWhereInput
    orderBy: Prisma.OrderOrderByWithRelationInput[]
    include?: Prisma.OrderInclude
  } {
    const builder = new QueryBuilder()

    if (filters) {
      builder.applyFilters(filters)
    }

    if (sort) {
      builder.applySorting(sort)
    }

    return {
      where: builder.whereClause,
      orderBy: builder.orderByClause,
      include
    }
  }

  /**
   * Apply all filters to the where clause
   */
  private applyFilters(filters: FilterConfig): void {
    const conditions: Prisma.OrderWhereInput[] = []

    // Text filters
    if (filters.productName) {
      conditions.push(this.buildTextFilter('productName', filters.productName))
    }

    // Number filters
    if (filters.quantity) {
      conditions.push(this.buildNumberFilter('quantity', filters.quantity))
    }
    if (filters.storePrice) {
      conditions.push(this.buildNumberFilter('storePrice', filters.storePrice))
    }
    if (filters.pasabuyFee) {
      conditions.push(this.buildNumberFilter('pasabuyFee', filters.pasabuyFee))
    }
    if (filters.customerPrice) {
      conditions.push(this.buildNumberFilter('customerPrice', filters.customerPrice))
    }

    // Date filters
    if (filters.createdAt) {
      conditions.push(this.buildDateFilter('createdAt', filters.createdAt))
    }
    if (filters.updatedAt) {
      conditions.push(this.buildDateFilter('updatedAt', filters.updatedAt))
    }

    // Entity filters
    if (filters.customers) {
      conditions.push(this.buildEntityFilter('customerId', filters.customers))
    }
    if (filters.storeCodes) {
      conditions.push(this.buildEntityFilter('storeCodeId', filters.storeCodes))
    }

    // Status filters
    if (filters.isBought !== undefined && filters.isBought !== null) {
      conditions.push({ isBought: filters.isBought })
    }
    if (filters.packingStatus && filters.packingStatus.length > 0) {
      conditions.push({ packingStatus: { in: filters.packingStatus } })
    }

    // Legacy search support
    if (filters.search) {
      conditions.push(this.buildLegacySearch(filters.search))
    }

    // Combine all conditions with AND
    if (conditions.length > 0) {
      this.whereClause = { AND: conditions }
    }
  }

  /**
   * Build text filter conditions
   */
  private buildTextFilter(field: string, filter: TextFilter): Prisma.OrderWhereInput {
    this.validateTextFilter(filter)

    const value = filter.caseSensitive ? filter.value : filter.value.toLowerCase()

    // Note: SQLite doesn't support mode parameter, so we handle case sensitivity differently
    const filterOptions = filter.caseSensitive
      ? { contains: value }
      : { contains: value }

    switch (filter.operator) {
      case 'contains':
        return { [field]: filterOptions }
      case 'startsWith':
        return { [field]: { startsWith: value } }
      case 'endsWith':
        return { [field]: { endsWith: value } }
      case 'equals':
        return { [field]: { equals: value } }
      case 'notContains':
        return { NOT: { [field]: filterOptions } }
      default:
        throw new QueryBuilderError(`Invalid text operator: ${filter.operator}`)
    }
  }

  /**
   * Build number filter conditions
   */
  private buildNumberFilter(field: string, filter: NumberFilter): Prisma.OrderWhereInput {
    this.validateNumberFilter(filter)

    switch (filter.operator) {
      case 'equals':
        return { [field]: filter.value }
      case 'gt':
        return { [field]: { gt: filter.value } }
      case 'gte':
        return { [field]: { gte: filter.value } }
      case 'lt':
        return { [field]: { lt: filter.value } }
      case 'lte':
        return { [field]: { lte: filter.value } }
      case 'between':
        if (filter.min === undefined || filter.max === undefined) {
          throw new FilterValidationError('Between operator requires both min and max values', field)
        }
        return { [field]: { gte: filter.min, lte: filter.max } }
      default:
        throw new QueryBuilderError(`Invalid number operator: ${filter.operator}`)
    }
  }

  /**
   * Build date filter conditions
   */
  private buildDateFilter(field: string, filter: DateFilter): Prisma.OrderWhereInput {
    this.validateDateFilter(filter)

    const getValue = (value: string | Date) => {
      return typeof value === 'string' ? new Date(value) : value
    }

    switch (filter.operator) {
      case 'equals':
        return { [field]: getValue(filter.value!) }
      case 'gt':
        return { [field]: { gt: getValue(filter.value!) } }
      case 'gte':
        return { [field]: { gte: getValue(filter.value!) } }
      case 'lt':
        return { [field]: { lt: getValue(filter.value!) } }
      case 'lte':
        return { [field]: { lte: getValue(filter.value!) } }
      case 'between':
        if (!filter.startDate || !filter.endDate) {
          throw new FilterValidationError('Between operator requires both startDate and endDate', field)
        }
        return {
          [field]: {
            gte: getValue(filter.startDate),
            lte: getValue(filter.endDate)
          }
        }
      default:
        throw new QueryBuilderError(`Invalid date operator: ${filter.operator}`)
    }
  }

  /**
   * Build entity filter conditions (for foreign keys)
   */
  private buildEntityFilter(field: string, filter: EntityFilter): Prisma.OrderWhereInput {
    const conditions: Prisma.OrderWhereInput[] = []

    // Include specific IDs
    if (filter.include.length > 0) {
      conditions.push({ [field]: { in: filter.include } })
    }

    // Exclude specific IDs
    if (filter.exclude.length > 0) {
      conditions.push({ NOT: { [field]: { in: filter.exclude } } })
    }

    // Include null values
    if (filter.includeNull) {
      if (conditions.length > 0) {
        return { OR: [...conditions, { [field]: null }] }
      } else {
        return { [field]: null }
      }
    }

    return conditions.length === 1 ? conditions[0] : { AND: conditions }
  }

  /**
   * Build legacy search conditions for backward compatibility
   */
  private buildLegacySearch(searchTerm: string): Prisma.OrderWhereInput {
    // Convert to lowercase for case-insensitive search in SQLite
    const lowerSearchTerm = searchTerm.toLowerCase()

    return {
      OR: [
        { productName: { contains: lowerSearchTerm } },
        { customer: { name: { contains: lowerSearchTerm } } },
        { storeCode: { code: { contains: lowerSearchTerm } } }
      ]
    }
  }

  /**
   * Apply sorting configuration
   */
  private applySorting(sort: SortConfig): void {
    // Sort columns by priority (lower number = higher priority)
    const sortedColumns = [...sort.columns].sort((a, b) => a.priority - b.priority)

    this.orderByClause = sortedColumns.map(column => this.buildSortClause(column))

    // Add default sort if no columns specified
    if (this.orderByClause.length === 0 && sort.defaultSort) {
      this.orderByClause.push(this.buildSortClause(sort.defaultSort))
    }
  }

  /**
   * Build individual sort clause
   */
  private buildSortClause(column: SortColumn): Prisma.OrderOrderByWithRelationInput {
    this.validateSortColumn(column)

    // Handle nested fields (e.g., 'customer.name')
    if (column.field.includes('.')) {
      const [relation, field] = column.field.split('.')
      return { [relation]: { [field]: column.direction } }
    }

    return { [column.field]: column.direction }
  }

  // Validation methods
  private validateTextFilter(filter: TextFilter): void {
    if (!VALID_TEXT_OPERATORS.includes(filter.operator)) {
      throw new FilterValidationError(`Invalid text operator: ${filter.operator}`)
    }
    if (!filter.value || filter.value.trim().length === 0) {
      throw new FilterValidationError('Text filter value cannot be empty')
    }
  }

  private validateNumberFilter(filter: NumberFilter): void {
    if (!VALID_NUMBER_OPERATORS.includes(filter.operator)) {
      throw new FilterValidationError(`Invalid number operator: ${filter.operator}`)
    }
    if (filter.operator === 'between') {
      if (filter.min === undefined || filter.max === undefined) {
        throw new FilterValidationError('Between operator requires both min and max values')
      }
      if (filter.min > filter.max) {
        throw new FilterValidationError('Min value cannot be greater than max value')
      }
    } else if (filter.value === undefined) {
      throw new FilterValidationError(`${filter.operator} operator requires a value`)
    }
  }

  private validateDateFilter(filter: DateFilter): void {
    if (!VALID_DATE_OPERATORS.includes(filter.operator)) {
      throw new FilterValidationError(`Invalid date operator: ${filter.operator}`)
    }
    if (filter.operator === 'between') {
      if (!filter.startDate || !filter.endDate) {
        throw new FilterValidationError('Between operator requires both startDate and endDate')
      }
    } else if (!filter.value) {
      throw new FilterValidationError(`${filter.operator} operator requires a value`)
    }
  }

  private validateSortColumn(column: SortColumn): void {
    if (!VALID_SORT_DIRECTIONS.includes(column.direction)) {
      throw new FilterValidationError(`Invalid sort direction: ${column.direction}`)
    }
    if (!VALID_ORDER_SORT_FIELDS.includes(column.field as ValidOrderSortField)) {
      throw new FilterValidationError(`Invalid sort field: ${column.field}`)
    }
  }
}

/**
 * Basic tests for the QueryBuilder functionality
 * This is a simple test to verify the query builder works correctly
 */

import { QueryBuilder } from '../query-builder'
import { FilterConfig, SortConfig } from '../filter-types'

describe('QueryBuilder', () => {
  describe('buildOrderQuery', () => {
    it('should build a basic query with no filters', () => {
      const result = QueryBuilder.buildOrderQuery()

      expect(result.where).toEqual({})
      expect(result.orderBy).toEqual([])
    })

    it('should build a query with text filter', () => {
      const filters: FilterConfig = {
        productName: {
          value: 'iPhone',
          operator: 'contains'
        }
      }

      const result = QueryBuilder.buildOrderQuery(filters)

      expect(result.where).toEqual({
        AND: [
          {
            productName: {
              contains: 'iphone' // Should be lowercase for case-insensitive search
            }
          }
        ]
      })
    })

    it('should build a query with number filter', () => {
      const filters: FilterConfig = {
        storePrice: {
          operator: 'gte',
          value: 1000
        }
      }

      const result = QueryBuilder.buildOrderQuery(filters)

      expect(result.where).toEqual({
        AND: [
          {
            storePrice: {
              gte: 1000
            }
          }
        ]
      })
    })

    it('should build a query with range filter', () => {
      const filters: FilterConfig = {
        storePrice: {
          operator: 'between',
          min: 1000,
          max: 5000
        }
      }

      const result = QueryBuilder.buildOrderQuery(filters)

      expect(result.where).toEqual({
        AND: [
          {
            storePrice: {
              gte: 1000,
              lte: 5000
            }
          }
        ]
      })
    })

    it('should build a query with entity filter', () => {
      const filters: FilterConfig = {
        customers: {
          include: [1, 2, 3],
          exclude: []
        }
      }

      const result = QueryBuilder.buildOrderQuery(filters)

      expect(result.where).toEqual({
        AND: [
          {
            customerId: {
              in: [1, 2, 3]
            }
          }
        ]
      })
    })

    it('should build a query with sorting', () => {
      const sort: SortConfig = {
        columns: [
          {
            field: 'productName',
            direction: 'asc',
            priority: 0
          },
          {
            field: 'storePrice',
            direction: 'desc',
            priority: 1
          }
        ]
      }

      const result = QueryBuilder.buildOrderQuery(undefined, sort)

      expect(result.orderBy).toEqual([
        { productName: 'asc' },
        { storePrice: 'desc' }
      ])
    })

    it('should build a query with nested field sorting', () => {
      const sort: SortConfig = {
        columns: [
          {
            field: 'customer.name',
            direction: 'asc',
            priority: 0
          }
        ]
      }

      const result = QueryBuilder.buildOrderQuery(undefined, sort)

      expect(result.orderBy).toEqual([
        { customer: { name: 'asc' } }
      ])
    })

    it('should build a complex query with multiple filters and sorting', () => {
      const filters: FilterConfig = {
        productName: {
          value: 'iPhone',
          operator: 'contains'
        },
        storePrice: {
          operator: 'between',
          min: 1000,
          max: 10000
        },
        isBought: false,
        customers: {
          include: [1, 2],
          exclude: []
        }
      }

      const sort: SortConfig = {
        columns: [
          {
            field: 'storePrice',
            direction: 'desc',
            priority: 0
          }
        ]
      }

      const result = QueryBuilder.buildOrderQuery(filters, sort)

      expect(result.where).toEqual({
        AND: [
          {
            productName: {
              contains: 'iphone'
            }
          },
          {
            storePrice: {
              gte: 1000,
              lte: 10000
            }
          },
          {
            isBought: false
          },
          {
            customerId: {
              in: [1, 2]
            }
          }
        ]
      })

      expect(result.orderBy).toEqual([
        { storePrice: 'desc' }
      ])
    })

    it('should handle legacy search', () => {
      const filters: FilterConfig = {
        search: 'iPhone'
      }

      const result = QueryBuilder.buildOrderQuery(filters)

      expect(result.where).toEqual({
        AND: [
          {
            OR: [
              { productName: { contains: 'iphone' } },
              { customer: { name: { contains: 'iphone' } } },
              { storeCode: { code: { contains: 'iphone' } } }
            ]
          }
        ]
      })
    })
  })
})

// Mock console methods to avoid noise in tests
global.console = {
  ...console,
  log: jest.fn(),
  error: jest.fn(),
  warn: jest.fn(),
}

import { describe, it, expect, beforeEach, jest } from '@jest/globals'
import { EnhancedStoreService } from '../enhanced-store-service'

// Mock Prisma
jest.mock('../db', () => ({
  prisma: {
    storeCode: {
      create: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
      findUnique: jest.fn(),
      findFirst: jest.fn(),
      findMany: jest.fn(),
      count: jest.fn(),
    },
    storeConfiguration: {
      upsert: jest.fn(),
      findUnique: jest.fn(),
      deleteMany: jest.fn(),
    },
    $transaction: jest.fn(),
  },
}))

describe('EnhancedStoreService', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  describe('Validation', () => {
    it('should validate store data successfully', async () => {
      const validStoreData = {
        code: 'TEST-STORE',
        name: 'Test Store',
        storeType: 'RETAIL' as const,
        status: 'ACTIVE' as const,
        email: '<EMAIL>',
        phone: '+63-2-123-4567',
        allowsDelivery: true,
        deliveryRadius: 10,
      }

      const result = await EnhancedStoreService.validateStoreData(validStoreData)
      
      expect(result.isValid).toBe(true)
      expect(result.errors).toHaveLength(0)
    })

    it('should fail validation for invalid store code', async () => {
      const invalidStoreData = {
        code: 'invalid code with spaces',
        name: 'Test Store',
      }

      const result = await EnhancedStoreService.validateStoreData(invalidStoreData)
      
      expect(result.isValid).toBe(false)
      expect(result.errors.some(e => e.field === 'code')).toBe(true)
    })

    it('should fail validation for invalid email', async () => {
      const invalidStoreData = {
        code: 'TEST-STORE',
        email: 'invalid-email',
      }

      const result = await EnhancedStoreService.validateStoreData(invalidStoreData)
      
      expect(result.isValid).toBe(false)
      expect(result.errors.some(e => e.field === 'email')).toBe(true)
    })

    it('should generate warnings for high values', async () => {
      const storeDataWithWarnings = {
        code: 'TEST-STORE',
        capacity: 2000, // High capacity
        serviceFee: 1500, // High service fee
      }

      const result = await EnhancedStoreService.validateStoreData(storeDataWithWarnings)
      
      expect(result.warnings.length).toBeGreaterThan(0)
      expect(result.warnings.some(w => w.field === 'capacity')).toBe(true)
      expect(result.warnings.some(w => w.field === 'serviceFee')).toBe(true)
    })
  })

  describe('Bulk Operations', () => {
    it('should handle bulk create validation-only mode', async () => {
      const bulkData = {
        stores: [
          { code: 'STORE1', name: 'Store 1' },
          { code: 'STORE2', name: 'Store 2' },
          { code: 'invalid code', name: 'Invalid Store' }, // This should fail
        ],
        validateOnly: true,
      }

      const result = await EnhancedStoreService.bulkCreateStores(bulkData)
      
      expect(result.operationType).toBe('CREATE')
      expect(result.totalItems).toBe(3)
      expect(result.failedItems).toBe(1)
      expect(result.successfulItems).toBe(2)
      expect(result.status).toBe('FAILED')
    })

    it('should handle bulk update validation', async () => {
      const bulkData = {
        updates: [
          { storeId: 1, data: { name: 'Updated Store 1' } },
          { storeId: 999, data: { name: 'Non-existent Store' } }, // Should fail
        ],
        validateOnly: true,
      }

      // Mock store existence check
      const { prisma } = require('../db')
      prisma.storeCode.findUnique
        .mockResolvedValueOnce({ id: 1, code: 'STORE1' }) // First store exists
        .mockResolvedValueOnce(null) // Second store doesn't exist

      const result = await EnhancedStoreService.bulkUpdateStores(bulkData)
      
      expect(result.operationType).toBe('UPDATE')
      expect(result.totalItems).toBe(2)
      expect(result.failedItems).toBe(1)
      expect(result.successfulItems).toBe(1)
    })

    it('should handle bulk delete with dependency checks', async () => {
      const bulkData = {
        storeIds: [1, 2, 3],
        validateOnly: true,
      }

      // Mock store existence and dependency checks
      const { prisma } = require('../db')
      prisma.storeCode.findUnique
        .mockResolvedValueOnce({ id: 1, orders: [] }) // No orders
        .mockResolvedValueOnce({ id: 2, orders: [{ id: 1 }] }) // Has orders
        .mockResolvedValueOnce(null) // Doesn't exist

      const result = await EnhancedStoreService.bulkDeleteStores(bulkData)
      
      expect(result.operationType).toBe('DELETE')
      expect(result.totalItems).toBe(3)
      expect(result.failedItems).toBe(2) // One with orders, one non-existent
      expect(result.successfulItems).toBe(1)
    })
  })

  describe('Import/Export', () => {
    it('should generate import template', () => {
      const csvTemplate = EnhancedStoreService.generateImportTemplate('CSV')
      const excelTemplate = EnhancedStoreService.generateImportTemplate('EXCEL')
      
      expect(csvTemplate).toBeInstanceOf(Buffer)
      expect(excelTemplate).toBeInstanceOf(Buffer)
      expect(csvTemplate.length).toBeGreaterThan(0)
      expect(excelTemplate.length).toBeGreaterThan(0)
    })

    it('should handle CSV import with valid data', async () => {
      const csvData = `Code,Name,Type,Status
STORE1,Store One,RETAIL,ACTIVE
STORE2,Store Two,WHOLESALE,ACTIVE`
      
      const buffer = Buffer.from(csvData, 'utf-8')
      
      // Mock database operations
      const { prisma } = require('../db')
      prisma.storeCode.findFirst
        .mockResolvedValue(null) // No existing stores
      prisma.storeCode.create
        .mockResolvedValueOnce({ id: 1, code: 'STORE1' })
        .mockResolvedValueOnce({ id: 2, code: 'STORE2' })

      const result = await EnhancedStoreService.importStores(buffer, 'test.csv')
      
      expect(result.totalRows).toBe(2)
      expect(result.successfulRows).toBe(2)
      expect(result.failedRows).toBe(0)
      expect(result.createdStores).toHaveLength(2)
    })

    it('should handle import with validation errors', async () => {
      const csvData = `Code,Name,Type,Status
,Store One,RETAIL,ACTIVE
INVALID CODE,Store Two,INVALID_TYPE,ACTIVE`
      
      const buffer = Buffer.from(csvData, 'utf-8')
      
      const result = await EnhancedStoreService.importStores(buffer, 'test.csv')
      
      expect(result.totalRows).toBe(2)
      expect(result.failedRows).toBe(2)
      expect(result.successfulRows).toBe(0)
      expect(result.errors.length).toBeGreaterThan(0)
    })
  })

  describe('Business Rules', () => {
    it('should enforce unique store codes', async () => {
      const storeData = {
        code: 'EXISTING-STORE',
        name: 'Test Store',
      }

      // Mock existing store
      const { prisma } = require('../db')
      prisma.storeCode.findFirst.mockResolvedValue({ id: 1, code: 'EXISTING-STORE' })

      const result = await EnhancedStoreService.validateStoreData(storeData)
      
      expect(result.isValid).toBe(false)
      expect(result.errors.some(e => e.errorCode === 'CUSTOM')).toBe(true)
    })

    it('should validate delivery radius dependency', async () => {
      const storeData = {
        code: 'TEST-STORE',
        allowsDelivery: false,
        deliveryRadius: 10, // Should not be allowed when delivery is disabled
      }

      const result = await EnhancedStoreService.validateStoreData(storeData)
      
      expect(result.isValid).toBe(false)
      expect(result.errors.some(e => e.field === 'deliveryRadius')).toBe(true)
    })

    it('should validate minimum order for store types', async () => {
      const wholesaleStore = {
        code: 'WHOLESALE-STORE',
        storeType: 'WHOLESALE' as const,
        minimumOrder: 500, // Too low for wholesale
      }

      const result = await EnhancedStoreService.validateStoreData(wholesaleStore)
      
      expect(result.isValid).toBe(false)
      expect(result.errors.some(e => e.field === 'minimumOrder')).toBe(true)
    })
  })
})

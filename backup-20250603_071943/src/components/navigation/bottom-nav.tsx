'use client'

import { usePathname, useRouter } from 'next/navigation'
import { ShoppingBag, Package, List, Plus, FileText, MoreHorizontal } from 'lucide-react'
import { useAppStore } from '@/lib/store'
import { NavigationItem } from './navigation-item'
import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'

const navItems = [
  {
    href: '/orders',
    label: 'Orders',
    icon: List,
  },
  {
    href: '/buy-list',
    label: 'Buy List',
    icon: ShoppingBag,
  },
  {
    href: '/packing',
    label: 'Packing',
    icon: Package,
  },
  {
    href: '/invoices',
    label: 'Invoices',
    icon: FileText,
  },
]

// Context-aware button configuration
function getContextAwareAction(pathname: string, storeCodes: any[]) {
  // Extract the base path and any dynamic segments
  const pathSegments = pathname.split('/').filter(Boolean)
  const basePath = `/${pathSegments[0] || ''}`

  switch (basePath) {
    case '/orders':
      return {
        href: '/orders/new',
        label: 'Add Order',
        action: 'menu', // Changed to menu to show options
        options: [
          {
            href: '/orders/new',
            label: 'Single Order',
            description: 'Add one order'
          },
          {
            href: '/orders/new/multi',
            label: 'Multiple Orders',
            description: 'Add multiple orders at once'
          }
        ]
      }
    case '/packing':
      // For packing, add order that can be packed
      return {
        href: '/orders/new',
        label: 'Add Order',
        action: 'navigate'
      }
    case '/customers':
      // Check if we're on a specific customer page
      if (pathSegments.length > 1 && !isNaN(Number(pathSegments[1]))) {
        // On specific customer page - pass customer ID
        const customerId = pathSegments[1]
        return {
          href: `/orders/new?customerId=${customerId}`,
          label: 'Add Order for Customer',
          action: 'navigate'
        }
      }
      // On main customers page
      return {
        href: '/customers/new',
        label: 'Add Customer',
        action: 'navigate'
      }
    case '/stores':
      return {
        href: '/stores/new',
        label: 'Add Store',
        action: 'navigate'
      }
    case '/buy-list':
      // For buy-list, check if we're on a specific store page
      if (pathSegments.length > 1 && pathSegments[1] !== 'all') {
        // On specific store buy-list page - find the store code ID
        const storeCodeParam = pathSegments[1]
        const matchingStoreCode = storeCodes.find(
          sc => sc.code.toLowerCase() === storeCodeParam.toLowerCase()
        )

        if (matchingStoreCode) {
          return {
            href: `/orders/new?storeCodeId=${matchingStoreCode.id}`,
            label: 'Add Order to Store',
            action: 'navigate'
          }
        } else {
          // Fallback to store code parameter for backward compatibility
          return {
            href: `/orders/new?storeCode=${storeCodeParam}`,
            label: 'Add Order to Store',
            action: 'navigate'
          }
        }
      }
      // On main buy-list page or all orders page
      return {
        href: '/orders/new',
        label: 'Add Order',
        action: 'navigate'
      }
    case '/invoices':
      // For invoices, add new invoice
      return {
        href: '/invoices/new',
        label: 'Add Invoice',
        action: 'navigate'
      }
    default:
      // Default to add order
      return {
        href: '/orders/new',
        label: 'Add Order',
        action: 'navigate'
      }
  }
}

export function BottomNav() {
  const pathname = usePathname()
  const router = useRouter()
  const { storeCodes } = useAppStore()
  const [isMenuOpen, setIsMenuOpen] = useState(false)

  // Get context-aware action configuration
  const contextAction = getContextAwareAction(pathname, storeCodes)

  const handleCenterButtonClick = (e: React.MouseEvent) => {
    e.preventDefault()

    if (contextAction.action === 'navigate') {
      router.push(contextAction.href)
    } else if (contextAction.action === 'menu') {
      setIsMenuOpen(!isMenuOpen)
    }
    // Future: handle modal actions here if needed
  }

  const handleMenuItemClick = (href: string) => {
    setIsMenuOpen(false)
    router.push(href)
  }

  return (
    <nav className="fixed bottom-0 left-0 right-0 z-50 bg-background border-t">
      <div className="container mx-auto px-4">
        {/* 5-element layout with center + button */}
        <div className="flex items-center justify-evenly py-2 min-h-[60px]">
          {/* First navigation item */}
          <div className="flex-1 max-w-[80px] flex justify-center">
            <NavigationItem
              href={navItems[0].href}
              label={navItems[0].label}
              icon={navItems[0].icon}
              variant="bottom-nav"
            />
          </div>

          {/* Second navigation item */}
          <div className="flex-1 max-w-[80px] flex justify-center">
            <NavigationItem
              href={navItems[1].href}
              label={navItems[1].label}
              icon={navItems[1].icon}
              variant="bottom-nav"
            />
          </div>

          {/* Center Add Button - Context-aware and enhanced for mobile */}
          <div className="flex-1 max-w-[80px] flex justify-center">
            {contextAction.action === 'menu' ? (
              <Popover open={isMenuOpen} onOpenChange={setIsMenuOpen}>
                <PopoverTrigger asChild>
                  <button
                    className="flex items-center justify-center bg-primary text-primary-foreground rounded-full w-14 h-14 hover:bg-primary/90 active:bg-primary/80 transition-all duration-200 shadow-lg hover:shadow-xl transform hover:scale-105 active:scale-95 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
                    title={contextAction.label}
                    aria-label={contextAction.label}
                  >
                    <Plus className="h-7 w-7" />
                  </button>
                </PopoverTrigger>
                <PopoverContent className="w-64 p-3" align="center" side="top" sideOffset={8}>
                  <div className="space-y-2">
                    <h4 className="font-medium text-sm">{contextAction.label}</h4>
                    <div className="space-y-1">
                      {contextAction.options?.map((option, index) => (
                        <Button
                          key={index}
                          variant="ghost"
                          className="w-full justify-start h-auto p-2 text-left"
                          onClick={() => handleMenuItemClick(option.href)}
                        >
                          <div>
                            <div className="font-medium text-sm">{option.label}</div>
                            <div className="text-xs text-muted-foreground">{option.description}</div>
                          </div>
                        </Button>
                      ))}
                    </div>
                  </div>
                </PopoverContent>
              </Popover>
            ) : (
              <button
                onClick={handleCenterButtonClick}
                className="flex items-center justify-center bg-primary text-primary-foreground rounded-full w-14 h-14 hover:bg-primary/90 active:bg-primary/80 transition-all duration-200 shadow-lg hover:shadow-xl transform hover:scale-105 active:scale-95 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
                title={contextAction.label}
                aria-label={contextAction.label}
              >
                <Plus className="h-7 w-7" />
              </button>
            )}
          </div>

          {/* Third navigation item */}
          <div className="flex-1 max-w-[80px] flex justify-center">
            <NavigationItem
              href={navItems[2].href}
              label={navItems[2].label}
              icon={navItems[2].icon}
              variant="bottom-nav"
            />
          </div>

          {/* Fourth navigation item */}
          <div className="flex-1 max-w-[80px] flex justify-center">
            <NavigationItem
              href={navItems[3].href}
              label={navItems[3].label}
              icon={navItems[3].icon}
              variant="bottom-nav"
            />
          </div>
        </div>
      </div>
    </nav>
  )
}

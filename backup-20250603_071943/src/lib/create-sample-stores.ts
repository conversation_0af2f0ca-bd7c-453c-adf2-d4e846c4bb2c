import { prisma } from './db'

async function createSampleStores() {
  console.log('🏪 Creating sample stores...')
  
  try {
    // Create AYALA store
    const ayala = await prisma.storeCode.create({
      data: {
        code: 'AYALA',
        name: 'Ayala Malls'
      }
    })
    console.log(`✅ Created store: ${ayala.code}`)

    // Create SM store
    const sm = await prisma.storeCode.create({
      data: {
        code: 'SM',
        name: 'SM Supermalls'
      }
    })
    console.log(`✅ Created store: ${sm.code}`)

    // Create LAZADA store
    const lazada = await prisma.storeCode.create({
      data: {
        code: 'LAZADA',
        name: 'Lazada Online'
      }
    })
    console.log(`✅ Created store: ${lazada.code}`)

    console.log('🎉 Sample stores created successfully!')
    
    // Now create pricing for AYALA
    console.log('💰 Creating pricing for AYALA...')
    const ayalaPricing = await prisma.storePricing.create({
      data: {
        storeCodeId: ayala.id,
        name: 'AYALA Tiered Pricing',
        serviceFee: 25.00,
        isActive: true,
        pricingTiers: {
          create: [
            {
              minPrice: 0,
              maxPrice: 200,
              markupType: 'FIXED_AMOUNT',
              markupValue: 70,
              pasabuyFee: 15,
              sortOrder: 0
            },
            {
              minPrice: 200.01,
              maxPrice: 500,
              markupType: 'FIXED_AMOUNT',
              markupValue: 100,
              pasabuyFee: 25,
              sortOrder: 1
            },
            {
              minPrice: 500.01,
              maxPrice: null,
              markupType: 'FIXED_AMOUNT',
              markupValue: 150,
              pasabuyFee: 35,
              sortOrder: 2
            }
          ]
        }
      },
      include: {
        pricingTiers: true
      }
    })
    
    console.log(`✅ Created ${ayalaPricing.pricingTiers.length} pricing tiers for AYALA`)

    // Create pricing for SM
    console.log('💰 Creating pricing for SM...')
    const smPricing = await prisma.storePricing.create({
      data: {
        storeCodeId: sm.id,
        name: 'SM Tiered Pricing',
        serviceFee: 20.00,
        isActive: true,
        pricingTiers: {
          create: [
            {
              minPrice: 0,
              maxPrice: 300,
              markupType: 'FIXED_AMOUNT',
              markupValue: 60,
              pasabuyFee: 12,
              sortOrder: 0
            },
            {
              minPrice: 300.01,
              maxPrice: 800,
              markupType: 'FIXED_AMOUNT',
              markupValue: 90,
              pasabuyFee: 20,
              sortOrder: 1
            },
            {
              minPrice: 800.01,
              maxPrice: null,
              markupType: 'FIXED_AMOUNT',
              markupValue: 120,
              pasabuyFee: 30,
              sortOrder: 2
            }
          ]
        }
      },
      include: {
        pricingTiers: true
      }
    })
    
    console.log(`✅ Created ${smPricing.pricingTiers.length} pricing tiers for SM`)

    // Create pricing for LAZADA (percentage-based)
    console.log('💰 Creating pricing for LAZADA...')
    const lazadaPricing = await prisma.storePricing.create({
      data: {
        storeCodeId: lazada.id,
        name: 'LAZADA Percentage Pricing',
        serviceFee: 15.00,
        isActive: true,
        pricingTiers: {
          create: [
            {
              minPrice: 0,
              maxPrice: 500,
              markupType: 'PERCENTAGE',
              markupValue: 30, // 30% markup
              pasabuyFee: 10,
              sortOrder: 0
            },
            {
              minPrice: 500.01,
              maxPrice: 1500,
              markupType: 'PERCENTAGE',
              markupValue: 25, // 25% markup
              pasabuyFee: 18,
              sortOrder: 1
            },
            {
              minPrice: 1500.01,
              maxPrice: null,
              markupType: 'PERCENTAGE',
              markupValue: 20, // 20% markup for expensive items
              pasabuyFee: 25,
              sortOrder: 2
            }
          ]
        }
      },
      include: {
        pricingTiers: true
      }
    })
    
    console.log(`✅ Created ${lazadaPricing.pricingTiers.length} pricing tiers for LAZADA`)

    console.log('\n📊 Summary:')
    console.log(`- Stores created: 3`)
    console.log(`- Pricing configurations: 3`)
    console.log(`- Total pricing tiers: ${ayalaPricing.pricingTiers.length + smPricing.pricingTiers.length + lazadaPricing.pricingTiers.length}`)
    
  } catch (error) {
    console.error('❌ Error creating sample stores:', error)
    throw error
  } finally {
    await prisma.$disconnect()
  }
}

// Run if executed directly
if (require.main === module) {
  createSampleStores()
    .then(() => {
      console.log('🏁 Sample stores creation completed!')
      process.exit(0)
    })
    .catch((error) => {
      console.error('💥 Error:', error)
      process.exit(1)
    })
}

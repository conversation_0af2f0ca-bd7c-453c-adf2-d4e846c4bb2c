import { prisma } from './db'

async function quickMigration() {
  console.log('🔄 Quick migration: Adding default tiers to existing pricing...')
  
  try {
    // Add default tier to default pricing if it doesn't exist
    const defaultPricing = await prisma.defaultPricing.findFirst({
      where: { isActive: true },
      include: { pricingTiers: true }
    })

    if (defaultPricing && defaultPricing.pricingTiers.length === 0) {
      await prisma.defaultPricingTier.create({
        data: {
          defaultPricingId: defaultPricing.id,
          minPrice: 0,
          maxPrice: null,
          markupType: 'PERCENTAGE',
          markupValue: 100.00,
          sortOrder: 0
        }
      })
      console.log('✅ Added default tier to default pricing')
    }

    // Add tiers to store pricing configurations
    const storePricings = await prisma.storePricing.findMany({
      include: { pricingTiers: true }
    })

    for (const pricing of storePricings) {
      if (pricing.pricingTiers.length === 0) {
        // Create a simple 3-tier structure with tiered Pasabuy fees
        const tiers = [
          {
            minPrice: 0,
            maxPrice: 200,
            markupType: 'FIXED_AMOUNT',
            markupValue: 70,
            pasabuyFee: 15, // Lower Pasabuy fee for low-price items
            sortOrder: 0
          },
          {
            minPrice: 200.01,
            maxPrice: 500,
            markupType: 'FIXED_AMOUNT',
            markupValue: 100,
            pasabuyFee: 25, // Standard Pasabuy fee
            sortOrder: 1
          },
          {
            minPrice: 500.01,
            maxPrice: null,
            markupType: 'FIXED_AMOUNT',
            markupValue: 150,
            pasabuyFee: 35, // Higher Pasabuy fee for expensive items
            sortOrder: 2
          }
        ]

        for (const tier of tiers) {
          await prisma.storePricingTier.create({
            data: {
              storePricingId: pricing.id,
              ...tier
            }
          })
        }
        console.log(`✅ Added ${tiers.length} tiers to ${pricing.name}`)
      }
    }

    console.log('🎉 Quick migration completed!')
    
  } catch (error) {
    console.error('❌ Migration failed:', error)
    throw error
  } finally {
    await prisma.$disconnect()
  }
}

quickMigration()

'use client'

import { useEffect, useState } from 'react'
import { Card } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import Image from 'next/image'

interface Order {
  id: number
  productName: string
  imageFilename: string | null
  storePrice: number
  customerPrice: number
  isBought: boolean
  packingStatus: string
}

export default function TestImagesPage() {
  const [orders, setOrders] = useState<Order[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    async function fetchOrders() {
      try {
        const response = await fetch('/api/orders')
        if (!response.ok) {
          throw new Error('Failed to fetch orders')
        }
        const data = await response.json()
        setOrders(data.data || [])
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Unknown error')
      } finally {
        setLoading(false)
      }
    }

    fetchOrders()
  }, [])

  if (loading) {
    return (
      <div className="container mx-auto p-6">
        <h1 className="text-2xl font-bold mb-6">Image Test Page</h1>
        <p>Loading orders...</p>
      </div>
    )
  }

  if (error) {
    return (
      <div className="container mx-auto p-6">
        <h1 className="text-2xl font-bold mb-6">Image Test Page</h1>
        <p className="text-red-500">Error: {error}</p>
      </div>
    )
  }

  const ordersWithImages = orders.filter(order => order.imageFilename)

  return (
    <div className="container mx-auto p-6">
      <h1 className="text-2xl font-bold mb-6">Image Test Page</h1>
      <p className="text-muted-foreground mb-6">
        Testing image display with {ordersWithImages.length} orders that have images.
      </p>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {ordersWithImages.map((order) => (
          <Card key={order.id} className="p-4">
            <div className="space-y-4">
              {/* Image using Next.js Image component */}
              <div className="aspect-square relative bg-muted rounded-lg overflow-hidden">
                <Image
                  src={`/api/images/orders/${order.imageFilename}`}
                  alt={order.productName}
                  fill
                  className="object-cover"
                  sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                />
              </div>

              {/* Product details */}
              <div>
                <h3 className="font-semibold text-lg mb-2">{order.productName}</h3>
                <div className="flex justify-between items-center mb-2">
                  <span className="text-sm text-muted-foreground">Store Price:</span>
                  <span className="font-medium">₱{order.storePrice.toLocaleString()}</span>
                </div>
                <div className="flex justify-between items-center mb-3">
                  <span className="text-sm text-muted-foreground">Customer Price:</span>
                  <span className="font-medium">₱{order.customerPrice.toLocaleString()}</span>
                </div>
                <div className="flex gap-2">
                  <Badge variant={order.isBought ? "default" : "secondary"}>
                    {order.isBought ? "Bought" : "Not Bought"}
                  </Badge>
                  <Badge variant={order.packingStatus === "Packed" ? "default" : "outline"}>
                    {order.packingStatus}
                  </Badge>
                </div>
              </div>

              {/* Direct image test */}
              <div className="border-t pt-4">
                <p className="text-xs text-muted-foreground mb-2">Direct img tag test:</p>
                <img
                  src={`/api/images/orders/${order.imageFilename}`}
                  alt={order.productName}
                  className="w-16 h-16 object-cover rounded border"
                />
              </div>

              {/* Image URL info */}
              <div className="border-t pt-4">
                <p className="text-xs text-muted-foreground">Image URL:</p>
                <code className="text-xs bg-muted p-1 rounded block break-all">
                  /api/images/orders/{order.imageFilename}
                </code>
              </div>
            </div>
          </Card>
        ))}
      </div>

      {ordersWithImages.length === 0 && (
        <Card className="p-6 text-center">
          <p className="text-muted-foreground">No orders with images found.</p>
        </Card>
      )}
    </div>
  )
}
